<?php

/*
header('HTTP/1.1 503 Service Temporarily Unavailable');
header('Status: 503 Service Temporarily Unavailable');
header('Retry-After: 300');//300 seconds
die('<html><head><title>Ažuriranje</title><meta http-equiv="refresh" content="60"></head><body><div style="text-align: center;"><br><img src="/media/images/logo-mail.png"><br><br>Web se ažurira. Biti ćemo dostupni kroz nekoliko minuta</div></body></html>');
*/

$application = 'application';
$modules = '/var/www/vhosts/_marker_app/modules';
$system = '/var/www/vhosts/_marker_app/system';

define('EXT', '.php');

/**
 * @see  http://php.net/error_reporting
 * Developing: E_ALL | E_STRICT
 * Production: E_ALL ^ E_NOTICE
 * Production: 0
 * Disable deprecated notices: E_ALL & ~E_DEPRECATED
 */
error_reporting(E_ALL | E_STRICT);

define('DOCROOT', realpath(dirname(__FILE__)) . DIRECTORY_SEPARATOR);
if (!is_dir($application) AND is_dir(DOCROOT . $application)) {
    $application = DOCROOT . $application;
}
if (!is_dir($modules) AND is_dir(DOCROOT . $modules)) {
    $modules = DOCROOT . $modules;
}
if (!is_dir($system) AND is_dir(DOCROOT . $system)) {
    $system = DOCROOT . $system;
}

define('APPPATH', realpath($application) . DIRECTORY_SEPARATOR);
define('MODPATH', realpath($modules) . DIRECTORY_SEPARATOR);
define('SYSPATH', realpath($system) . DIRECTORY_SEPARATOR);

unset($application, $modules, $system);

if (file_exists('install' . EXT)) {
    return include 'install' . EXT;
}
if (!defined('KOHANA_START_TIME')) {
    define('KOHANA_START_TIME', microtime(TRUE));
}
if (!defined('KOHANA_START_MEMORY')) {
    define('KOHANA_START_MEMORY', memory_get_usage());
}

require APPPATH . 'bootstrap' . EXT;

echo Request::factory()->execute()->send_headers()->body();
