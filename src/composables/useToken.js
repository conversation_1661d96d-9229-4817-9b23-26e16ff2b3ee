import { useConfig } from '@/composables/useConfig'
import { useCookie } from '@/composables/useCookie'
import { useFetch } from '@/composables/useFetch'
import { ref } from 'vue'

const config = useConfig()
const authToken = ref('')

export function useToken() {
	const tokenCookie = useCookie('hapi_auth_token')
	if(tokenCookie){
		authToken.value = tokenCookie
	}

	const generateToken = async(options = Object) => {
		const params = (options) && '?'
		const tokenId = (options.tokenId) ? 'set_token_id=' + options.tokenId : null
		const tokenHash = (options.tokenHash) ? '&token_hash=' + options.tokenHash : null

		return await useFetch({
			url: `/${config.apiUrl}/${config.apiVersion}/auth/generate-token/` + params + tokenId + tokenHash,
			credentials: options.credentials
		})
		.then(res => setToken(res.data.jwt))
	}

	const setToken = (token) => {
		const cookieExpiration = new Date();
		cookieExpiration.setFullYear(cookieExpiration.getFullYear() + 1);
		document.cookie = `hapi_auth_token=${token}; expires=${cookieExpiration}; path=/`;
		authToken.value = token
		return token
	}

	return { authToken, generateToken, setToken }
}