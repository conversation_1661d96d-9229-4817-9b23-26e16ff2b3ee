import {useConfig} from '@/composables/useConfig';
import {useFetch} from '@/composables/useFetch';
import {ref} from 'vue';

const config = useConfig();
const endpoints = ref(null);

export function useEndpoints() {
	const getEndpoints = async () => {
		return await useFetch({url: `/${config.apiUrl}/${config.apiVersion}/misc/endpoints/?version=v1`}).then(res => {
			//console.debug('composables/endpoints', res.data);
			endpoints.value = res.data;
			return res.data;
		});
	};

	return {endpoints, getEndpoints};
}
