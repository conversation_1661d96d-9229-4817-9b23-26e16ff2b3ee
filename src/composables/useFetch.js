import { useToken } from '@/composables/useToken'
import { useConfig } from '@/composables/useConfig'

export async function useFetch(options) {
	const { authToken } = useToken()
	const { baseUrl } = useConfig()

	const method = (options.method) ? options.method : 'GET'
	const url = options.url

	const fetchOptions = {
		method: method,
		headers: {'Authorization': 'Bearer ' + authToken.value},
		body: JSON.stringify(options.body)
	}

	if(location.href.includes('localhost')) {
		if(!options.credentials || options.credentials != 'omit') {
			fetchOptions.credentials = 'include'
		}
	}

	return await fetch(`${baseUrl}${url}`, fetchOptions)
	.then(response => {
		if (response.status >= 200) {
			return response.json();
		} else {
			console.log(response);
		}
	})
	.then(res => res)
}