export default function useHelpers() {
	// format currency
	// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/NumberFormat
	function formatCurrency(value, options = {}) {
		const amount = new Intl.NumberFormat('sl-SI', {
			minimumFractionDigits: 2,
			maximumFractionDigits: 2,
		}).format(value);

		return options?.showCurrency == false ? amount : amount + ' €';
	}

	function formatDate(value, options = {}) {
		const d = new Date(value * 1000);
		const date = d.getDate();
		const month = d.getMonth() < 9 ? '0' + (d.getMonth() + 1) : d.getMonth() + 1;
		const year = d.getFullYear();
		return (options?.short) ? date + '.' + month + '.' : date + '.' + month + '.' + year;
	}

	// Takes price value and returns formatted price. From 123456 to 1.234,56
	function maskPrice(value, callback, options) {
		let input = value;
		if(options?.init && !input.toString().includes('.')) {
			input = input + '00';
		}
		input = input.replace(/[^\d]/g, '');
		let paddedInput = input.padStart(3, '0');
		const integerPart = paddedInput.slice(0, -2);
		const decimalPart = paddedInput.slice(-2);
		let formattedIntegerPart = integerPart.replace(/^0+(?=\d)/, '');
		if (formattedIntegerPart === '') {
			formattedIntegerPart = '0';
		}

		const formattedInteger = formattedIntegerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
		const formattedValue = `${formattedInteger},${decimalPart}`;
		if(callback) callback(formattedValue);
		return formattedValue;
	}

	// Takes formatted price value and returns unformatted value. From 1.234,56 to 1234.56
	function unmaskPrice(value) {
		if (!value) return '';
		const numericValue = value.replace(/\./g, '').replace(',', '.');
		return Number(numericValue);
	}

	function getFileName(url) {
		if(!url) return;
		const fileName = url.split('/').pop();
		return fileName ? fileName : url;
	}	

	function isObjectEmpty(source, options = {}) {
		if (Object.keys(source).length === 0) return true;
		if (options?.ignoreArrays) return Object.values(source).every(value => Array.isArray(value) && value.length === 0);
	}	

	return {formatCurrency, formatDate, maskPrice, unmaskPrice, getFileName, isObjectEmpty};
}