import {useConfig} from '@/composables/useConfig';
import {useToken} from '@/composables/useToken';
import {useFetch} from '@/composables/useFetch';

const config = useConfig();
const token = useToken();

export function useAuth() {
	const login = async options => {
		return await useFetch({
			url: `/${config.apiUrl}/${config.apiVersion}/auth/login/`,
			method: 'POST',
			body: options ? options : {'email': '<EMAIL>', 'password': 'zL7B1ef6'},
			credentials: 'omit',
		}).then(res => {
			token.setToken(res.data.jwt);
			console.debug('composables/useAuth/login', res);
			return res.data;
		});
	};

	const isLoggedIn = async () => {
		return await useFetch({
			url: `/${config.apiUrl}/${config.apiVersion}/auth/is-login/`,
		}).then(res => {
			console.debug('composables/useAuth/isLoggedIn', res);
			return res;
		});
	};

	return {login, isLoggedIn};
}
