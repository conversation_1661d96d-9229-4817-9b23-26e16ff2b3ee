export function useConfig() {
	const baseUrl = window.location.host == 'localhost:3000' ? 'https://pa.bigbang.si' : window.location.origin;
	const apiVersion = 'v1';
	const apiUrl = 'hapi';
	const appVersion = '1.3.0';
	const channel = 'SI';
	const thumbPresets = {
		categoryEntry: {
			imageFields: ['main_image_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 150,
						height: 150,
						crop: false,
					},
				},
			],
		},
		catalogEntry: {
			imageFields: ['main_image_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 100,
						height: 100,
						crop: false,
					},
				},
			],
		},
		catalogDetail: {
			imageFields: ['url'],
			thumbs: [
				{
					alias: 'thumb_large',
					config: {
						width: 490,
						height: 490,
						crop: false,
					},
				},
				{
					alias: 'thumb',
					config: {
						width: 80,
						height: 80,
						crop: false,
					},
				},
			],
		},
		searchEntry: {
			imageFields: ['main_image_upload_path'],
			thumbs: [
				{
					alias: 'thumb',
					config: {
						width: 60,
						height: 60,
						crop: false,
					},
				},
			],
		},
	};

	return {baseUrl, channel, apiVersion, apiUrl, thumbPresets, appVersion};
}
