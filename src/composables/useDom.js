export default function useDom() {
	function addBodyClass(classes) {
		if (typeof classes === 'string') {
			document.body.classList.add(classes);
		} else if (Array.isArray(classes)) {
			classes.forEach(c => document.body.classList.add(c));
		}
	}

	function removeBodyClass(classes) {
		if (typeof classes === 'string') {
			document.body.classList.remove(classes);
		} else if (Array.isArray(classes)) {
			classes.forEach(c => document.body.classList.remove(c));
		}
	}

	function resetBodyClass() {
		const classes = document.body.classList;
		for (let i = 0; i < classes.length; i++) {
			document.body.classList.remove(classes[i]);
		}
	}

	function scrollTo(targetSelector, options) {
		const targetElem = document.querySelector(targetSelector);

		const offsetValue = options?.offset ? options?.offset : 0;
		const behaviorValue = options?.behavior ? options?.behavior : 'smooth';

		if (targetElem) {
			const elementRect = targetElem.getBoundingClientRect();
			const absoluteTop = elementRect.top + window.scrollY;

			window.scrollTo({
				behavior: behaviorValue,
				top: absoluteTop - offsetValue,
			});
		}
	}	

	return {addBodyClass, removeBodyClass, resetBodyClass, scrollTo};
}