import {useStore} from '@/stores';
import useDom from '@/composables/useDom';

export default function useFlyout() {
	const store = useStore();
	const {addBodyClass, removeBodyClass} = useDom();
	
	function openFlyout(data, callback) {
		store.flyout = data;
		if (callback) callback();
		addBodyClass('flyout-active');
	}

	function closeFlyout(callback) {
		store.flyout = 0;
		if (callback) callback();
		removeBodyClass('flyout-active');
	}

	function appendFlyoutData(data) {
		if (store.flyout) {
			store.flyout = {...store.flyout, ...data};
		}
	}

	function isFlyoutActive() {
		return store.flyout ? true : false;
	}

	return {openFlyout, closeFlyout, appendFlyoutData, isFlyoutActive};
}