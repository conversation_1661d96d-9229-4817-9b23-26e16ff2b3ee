import {useStore} from '@/stores';
import {useEndpoints} from '@/composables/useEndpoints';
import {useFetch} from '@/composables/useFetch';
import useFlyout from '@/composables/useFlyout';

export function useCatalog() {
	const ep = useEndpoints();
	const store = useStore();
	const {openFlyout} = useFlyout();
	
	async function openServiceList() {
		store.loading = 1;

		const res = await useFetch({
			url: ep.endpoints.value._post_hapi_catalog_products,
			method: 'POST',
			body: {
				'lang': 'si',
				'mode': 'normal',
				'product_type': 'standalone',
				'limit': 100,
			},
		})

		const availableServices = res?.success ? res : store.labels?.pa_services_empty;
		store.loading = 0;

		openFlyout({
			mode: 'services',
			title: store?.labels?.pa_services_list_title || '',
			availableServices: availableServices,
		});
	}

	return {openServiceList}
}