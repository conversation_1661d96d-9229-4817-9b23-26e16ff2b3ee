import {useConfig} from '@/composables/useConfig';
import {useFetch} from '@/composables/useFetch';

export function useImages() {
	const config = useConfig();

	// add /upload/ prefix to file path
	function filePath(payload) {
		const path = payload.split('/');
		if (!path.includes('upload')) {
			return '/upload/' + payload;
		}

		return payload;
	}

	// get all image urls from data based on provided field names
	const getFileUrls = (data, urlFields) => {
		let fileUrls = [];

		if (Array.isArray(data)) {
			data.forEach(el => {
				urlFields.forEach(url => {
					if (el[url]) {
						fileUrls.push({
							related: el.id,
							field: url,
							file: filePath(el[url]),
						});
					}
				});
			});
			return fileUrls;
		}

		urlFields.forEach(url => {
			if (data[url]) {
				fileUrls.push({
					related: data.id,
					field: url,
					file: filePath(data[url]),
				});
			}
		});

		return fileUrls;
	};

	// generate thumbs config
	const prepareThumbsConfig = (files, thumbsConfig) => {
		let thumbs = [];
		files.forEach(file => {
			thumbsConfig.forEach(thumb => {
				thumbs.push({
					alias: `${file.related}-${file.field}_${thumb.alias}`,
					config: thumb.config,
					url: file.file,
				});
			});
		});

		return thumbs;
	};

	// get all images with provided ID prefix
	function findImagesById(id, images, callback) {
		if (!id) {
			return console.debug('composables/useImages findImagesById() Error: ID is not provided');
		}

		Object.entries(images).forEach(image => {
			const key = image[0].split('-');
			const imageId = key[0];
			const alias = key[1];

			if (imageId == id) {
				return callback(alias, image[1]);
			}
		});
	}

	// append images back to provided data
	function appendTo(data, images) {
		// if data is array
		if (Array.isArray(data)) {
			data.forEach(item => {
				findImagesById(item.id, images, (alias, imgData) => (item[alias] = imgData));
			});
			return true;
		}

		// if data is object
		const dataId = data.id ? data.id : null;
		findImagesById(dataId, images, (alias, imgData) => (data[alias] = imgData));
	}

	/*
		Generate thumbs
		Params:
		
		data:Array,Object,
		imageFields: Array,
		thumbs: Array
			{
				alias: String,
				config: {
					width: Number,
					height: Number,
					crop: Boolean
				},
			},
		],
	*/
	function generateThumbs(options) {
		const append = options && 'append' in options ? options.append : true;

		const imageFieldsPreset = options.preset ? config.thumbPresets[options.preset].imageFields : options.imageFields;
		const files = getFileUrls(options.data, imageFieldsPreset); // extract image urls from data

		const thumbPresets = options.preset ? config.thumbPresets[options.preset].thumbs : options.thumbs;
		const thumbsConfig = prepareThumbsConfig(files, thumbPresets); // generate/prepare thumbs config

		return useFetch({
			url: `/${config.apiUrl}/${config.apiVersion}/misc/generate-thumbnail/`,
			method: 'POST',
			body: thumbsConfig,
		}).then(res => {
			if (append) appendTo(options.data, res.data);
			return res.data;
		});
	}

	return {filePath, generateThumbs};
}
