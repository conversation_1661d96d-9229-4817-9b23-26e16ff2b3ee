import {createRouter, createWebHistory} from 'vue-router';
import {useStore} from '@/stores';
import {useWebshopStore} from '@/stores/webshop';
import {useCatalogStore} from '@/stores/catalog';
import {useFetch} from '@/composables/useFetch';
import {useEndpoints} from '@/composables/useEndpoints';
import useDom from '@/composables/useDom';

const router = createRouter({
	history: createWebHistory('/'),
	routes: [
		{
			path: '/',
			name: 'homepage',
			component: () => import('../views/Home.vue'),
		},
		{
			path: '/uporabnik/change_store/',
			name: 'changeStore',
			component: () => import('../views/ChangeStore.vue'),
		},
		{
			path: '/webshop/store/',
			name: 'shoppingCart',
			meta: {cart: true},
			component: () => import('../views/ShoppingCart.vue'),
		},
		{
			path: '/webshop/copy-cart/',
			name: 'copyCart',
			component: () => import('../views/CopyCart.vue'),
		},
		{
			path: '/izdelki/',
			name: 'products',
			component: () => import('../views/Category.vue'),
		},
		{
			path: '/izdelki/:category+/', // .*/izdelki/.*
			name: 'category',
			component: () => import('../views/Category.vue'),
		},
		{
			path: '/izdelki/iskanje/',
			name: 'search',
			component: () => import('../views/Category.vue'),
		},
		{
			path: '/:product(.*-izdelek-\\d+)/',
			name: 'itemDetail',
			meta: {detail: true},
			component: () => import('../views/ItemDetail.vue'),
		},
		{
			path: '/pa-uporabniki/',
			name: 'users',
			meta: {users: true},
			component: () => import('../views/Users.vue'),
		},
		{
			path: '/external/cart/:id+',
			name: 'externalCart',
			meta: {users: true},
			component: () => import('../views/ExternalCart.vue'),
		},
	],
});

const {removeBodyClass} = useDom();
router.beforeEach(async (to, from, next) => {
	const store = useStore();
	const catalogStore = useCatalogStore();
	const webshopStore = useWebshopStore();
	store.images = {};
	store.flyout = 0;
	removeBodyClass(['page-catalog-detail', 'flyout-active']);

	if (!store.redirects.length) {
		const ep = useEndpoints();
		await useFetch({url: ep.endpoints.value._get_hapi_cms_redirects}).then(res => (store.redirects = res.data));
	}

	function redirectUrl(value) {
		if (value.startsWith('http://') || value.startsWith('https://') || value.startsWith('//')) {
			window.location.href = value;
		} else {
			next(value);
		}
	}

	let redirect = store.redirects.find(r => {
		let regexString = r.old_path.replace(/\/\(\.\*\)|\/\(\.\+\)/, '/(.*)');
		if (regexString.endsWith('/(slug*)/')) {
			regexString = regexString.replace('/(slug*)/', '/(.*)');
		}
		if (regexString.endsWith('/(slug+)/')) {
			regexString = regexString.replace('/(slug+)/', '/(.*)');
		}
		if (regexString.endsWith('/(id*)/')) {
			regexString = regexString.replace('/(id*)/', '/(.*)');
		}
		if (regexString.endsWith('/(id+)/')) {
			regexString = regexString.replace('/(id+)/', '/(.*)');
		}
		if (regexString.endsWith('/(id+)-(slug+)/')) {
			regexString = regexString.replace('/(id+)-(slug+)/', '/(.*)');
		}
		if (regexString.endsWith('/(slug+)-(id+)/')) {
			regexString = regexString.replace('/(slug+)-(id+)/', '/(.*)');
		}
		const regex = new RegExp(`^${regexString}$`);
		return regex.test(to.path);
	});

	if (redirect) {
		const regexNumber = /^\/(?:[^\/]+\/)+\d+\/?$/;

		if (redirect.old_path.endsWith('/(slug*)/')) {
			const regex = new RegExp(`^${redirect.old_path.replace('/(slug*)/', '(.*)')}$`);
			const match = to.path.match(regex);
			const oldPathSlashes = redirect.old_path.split('/').length;
			const toPathSlashes = to.path.split('/').length - 1;
			if (match && oldPathSlashes > toPathSlashes) {
				const newPath = redirect.new_path.replace('/(1)/', match[1]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else if (redirect.old_path.endsWith('/(slug+)/')) {
			const regex = new RegExp(`^${redirect.old_path.replace('/(slug+)/', '/(.+)/')}$`);
			const match = to.path.match(regex);
			const oldPathSlashes = redirect.old_path.split('/').length;
			const toPathSlashes = to.path.split('/').length - 1;
			if (match && match[1] && oldPathSlashes > toPathSlashes) {
				const newPath = redirect.new_path.replace('(1)', match[1]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else if (redirect.old_path.endsWith('/(id*)/')) {
			const regex = new RegExp(`^${redirect.old_path.replace('/(id*)/', '(.*)')}$`);
			const match = to.path.match(regex);
			const oldPathSlashes = redirect.old_path.split('/').length - 1;
			const toPathSlashes = to.path.split('/').length;
			if ((match && oldPathSlashes == toPathSlashes) || (match && regexNumber.test(to.path))) {
				console.log(regexNumber.test(to.path));
				const newPath = redirect.new_path.replace('/(1)/', match[1]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else if (redirect.old_path.endsWith('/(id+)/')) {
			const regex = new RegExp(`^${redirect.old_path.replace('/(id+)/', '/(.+)/')}$`);
			const match = to.path.match(regex);
			if (match && match[1] && regexNumber.test(to.path)) {
				const newPath = redirect.new_path.replace('(1)', match[1]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else if (redirect.old_path.endsWith('/(id+)-(slug+)/')) {
			const regex = new RegExp(`^${redirect.old_path.replace('/(id+)-(slug+)/', '/(\\d+)-(.+)/')}$`);
			const match = to.path.match(regex);
			if (match && match[1] && match[2]) {
				const newPath = redirect.new_path.replace('(1)', match[1]).replace('(2)', match[2]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		}
		if (redirect.old_path.endsWith('/(slug+)-(id+)/')) {
			const regex = new RegExp(`^${redirect.old_path.replace('/(slug+)-(id+)/', '/(.+)-(\\d+)/')}$`);
			const match = to.path.match(regex);
			if (match && match[1] && match[2]) {
				const newPath = redirect.new_path.replace('(1)', match[1]).replace('(2)', match[2]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else if (redirect.old_path.endsWith('(.*)')) {
			const regex = new RegExp(`^${redirect.old_path.replace('(.*)', '(.*)')}$`);
			const match = to.path.match(regex);
			if (match) {
				const newPath = redirect.new_path.replace('(.*)', match[1]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else if (redirect.old_path.endsWith('(.+)')) {
			const regex = new RegExp(`^${redirect.old_path.replace('(.+)', '(.+)')}$`);
			const match = to.path.match(regex);
			if (match && match[1]) {
				const newPath = redirect.new_path.replace('(.+)', match[1]);
				redirectUrl(newPath);
				return;
			} else {
				next();
				return;
			}
		} else {
			redirectUrl(redirect.new_path);
			return;
		}
	}

	next();

	if (to.meta.detail) {
		catalogStore.itemDetail = true;
	} else {
		catalogStore.itemDetail = false;
		catalogStore.redirected = false;
	}

	if(!to.meta.cart) {
		webshopStore.itemSelected = []
	}

	if (to.meta.users) {
		document.body.classList.add('page-users');
	} else {
		document.body.classList.remove('page-users');
	}
});

export default router;
