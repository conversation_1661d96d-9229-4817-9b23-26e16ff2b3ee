<template>
	<SearchUser :cart="cart" />

	<div class="w-cart-content">
		<template v-if="webshopStore.cart?.cart?.errors?.cart">
			<div class="global-error" v-for="error in webshopStore.cart.cart.errors.cart" :key="error">
				<span class="label" v-html="store.labels[error.label_name] ? store.labels[error.label_name] : error.label_name"></span>
				<span v-if="error.label_name == 'error_a_limit_insufficient_total_for_payment'" class="error-btn" @click="setDueDateError" v-html="store.labels.error_limit_insufficient_btn"></span>
			</div>
		</template>
		<template v-if="webshopStore.cart?.cart?.warnings?.cart">
			<div class="global-error" v-for="warning in webshopStore.cart.cart.warnings.cart" :key="warning" :class="{'special': warning.label_name == 'warning_payment_required'}" v-html="store.labels[warning.label_name] ? store.labels[warning.label_name] : warning.label_name" />
		</template>
		<template v-if="webshopStore.cartError">
			<div class="global-error">
				<div v-html="webshopStore.cartError"></div>
				<template v-if="webshopStore.cartErrorInfo">
					<div v-for="(item, index) in webshopStore.cartErrorInfo" :key="index">
						- <span v-for="(value, key, i) in item" :key="key">{{ key }}: {{ value }}{{ i !== Object.keys(item).length - 1 ? ', ' : '' }}</span>
					</div>
				</template>
			</div>
		</template>
		<template v-if="webshopStore.cart?.cart?.indicators?.manual_shopping_cart_code_required && webshopStore.cart?.cart?.code == null">
			<div class="global-error">{{ store.labels.pa_cart_code_notification }}</div>
		</template>

		<template v-if="webshopStore.cart.parcels && webshopStore.cart.parcels.length">
			<template v-for="(parcel, index) in webshopStore.cart.parcels" :key="index">
				<div v-if="parcel.shipping?.selected?.title" class="w-cart-group">
					<div @click="selectAllItems(parcel.items)" class="w-cart-package-select-all" :class="{ 'active': allItemsSelected(parcel.items) }"></div>
					<div class="w-cart-package-title">
						<strong>{{ store.labels.pa_cart_parcel }} {{ index + 1 }}</strong>
					</div>
					<div
						class="w-cart-group-badge"
						:class="{
							'badge2':
								(webshopStore.cart.cart.store_id && parcel.shipping.pickup_location.selected && webshopStore.cart.cart.store_id == parcel.shipping.pickup_location.selected.id) ||
								parcel.shipping.selected.code == 'osobno_preuzimanje_poslovnica' ||
								parcel.shipping.selected.code == 'osobno_preuzimanje_skladiste',
							'badge3': webshopStore.cart.cart.store_id && parcel.shipping.pickup_location.selected && webshopStore.cart.cart.store_id != parcel.shipping.pickup_location.selected.id && parcel.shipping.selected.code == 'osobno_preuzimanje',
						}">
						{{ parcel.shipping?.selected?.title }}
					</div>
					<div class="w-cart-group-col2">
						<div class="w-cart-group-date">
							<template v-if="parcel.shipping.selected.delivery_date_humanized != null">{{ parcel.shipping.selected.delivery_date_humanized }}</template>
							<template v-else>{{ store.labels.pa_parcel_shipping_today }}</template>
						</div>
						<div
							class="w-cart-group-address"
							:class="{
								'badge2':
									(webshopStore.cart.cart.store_id && parcel.shipping.pickup_location.selected && webshopStore.cart.cart.store_id == parcel.shipping.pickup_location.selected.id) ||
									parcel.shipping.selected.code == 'osobno_preuzimanje_poslovnica' ||
									parcel.shipping.selected.code == 'osobno_preuzimanje_skladiste',
								'badge3':
									webshopStore.cart.cart.store_id && parcel.shipping.pickup_location.selected && parcel.shipping.pickup_location.selected.id && webshopStore.cart.cart.store_id != parcel.shipping.pickup_location.selected.id && parcel.shipping.selected.code == 'osobno_preuzimanje',
							}">
							<template v-if="parcel.shipping.pickup_location.selected != null">{{ parcel.shipping.pickup_location.selected.title }}</template>
							<!--
							<template v-if="parcel.customer && parcel.customer.first_name != '000'">
								<strong>{{ parcel.customer.first_name }} {{ parcel.customer.last_name }}</strong
								>, {{ parcel.customer.address.street }} {{ parcel.customer.address.house_number }}, {{ parcel.customer.address.zipcode }} {{ parcel.customer.address.city }}
							</template>
							-->
						</div>
					</div>
				</div>
				<div v-else class="w-cart-group">
					<div @click="selectAllItems(parcel.items)" class="w-cart-package-select-all" :class="{ 'active': allItemsSelected(parcel.items) }"></div>
					<div class="w-cart-package-title">
						<strong>{{ store.labels.pa_cart_parcel }} {{ index + 1 }}</strong>
					</div>
				</div>
				<template v-if="parcel?.recipient_select_allowed && webshopStore?.cart?.customer?.api_code && webshopStore?.cart?.customer?.first_name != '000'">
					<div class="w-cart-group-sw" :class="{'active': parcel?.customer?.parcel_has_selected_recipient}" @click="openRecipient(webshopStore.cart, parcel?.items)">
						<span v-if="parcel?.customer?.parcel_has_selected_recipient" class="icon">
							<span v-if="parcel?.customer?.first_name" class="name"
								><strong>{{ parcel?.customer.first_name }} <template v-if="parcel?.customer?.first_name != parcel?.customer?.last_name">{{ parcel?.customer.last_name }}</template></strong></span
							>
							<span v-if="parcel?.customer?.api_code2" class="id"> ({{ parcel?.customer.api_code2 }}) <br /></span>
							<span v-if="parcel?.customer?.address" class="mail">{{ parcel?.customer.address.street }}, {{ parcel?.customer.address.zipcode }} {{ parcel?.customer.address.city }} <br /></span>
							<span v-if="parcel?.customer?.phone" class="mail">{{ parcel?.customer.phone }} <br /></span>
							<span v-if="parcel?.customer?.email" class="mail">{{ parcel?.customer.email }}</span>
						</span>
						<span v-else class="icon empty"
							><strong>{{ store.labels.pa_enter_recipient }}</strong></span
						>
						<div v-if="parcel?.customer?.parcel_has_selected_recipient" class="w-cart-sw-btn w-cart-sw-clear" @click.stop="clearRecipient(parcel?.items)"></div>
					</div>
				</template>

				<ShoppingCartProduct v-for="(item, index) in parcel.items" :key="index" :item="item" />

				<div class="w-cart-total-shipping">
					<span class="w-cart-totals-label">{{ store.labels.pa_parcel_shipping }}&nbsp;</span>
					<span class="w-cart-totals-value strong" @click="openSalesmanShippingPrice(parcel)">
						<span class="w-shipping-price-edit" v-if="parcel?.shipping?.selected?.salesman_shipping_price_set"></span>
						<template v-if="parcel.shipping.selected && parcel.shipping.selected.shipping_price && parcel.shipping.selected.shipping_price > 0">
							<template v-if="parcel.shipping.selected.discount_shipping_price != null && parcel.shipping.selected.discount_shipping_price < parcel.shipping.selected.shipping_price && !parcel?.shipping?.selected?.salesman_shipping_price_set">
								<span class="line-through">{{ formatCurrency(parcel.shipping.selected.shipping_price) }}</span>&nbsp;
								<span v-if="parcel.shipping.selected.discount_shipping_price > 0" class="red price">{{ formatCurrency(parcel.shipping.selected.discount_shipping_price) }}</span>
								<span v-else class="green">{{ store.labels.free }}</span>
							</template>
							<template v-else
								><span class="price">{{ formatCurrency(parcel.shipping.selected.shipping_price) }}</span></template
							>
						</template>
						<template v-else
							><span class="green">{{ store.labels.free }}</span></template
						>
					</span>
				</div>
			</template>
			<div class="w-cart-content-bottom" v-if="webshopStore.cart.total">
				<div class="w-cart-message">
					<textarea v-model="cartMessage" maxlength="1000" name="pa_cart_message" type="text" @blur="submitMessageBlur" :placeholder="store.labels.pa_message" />
				</div>
				<div class="w-cart-total">
					<div v-if="webshopStore.cart.cart?.api_id" class="w-cart-total-item w-cart-total-item-id"><strong>ID:</strong>&nbsp;{{ webshopStore.cart.cart.api_id }}</div>
					<div v-if="total_items_total" class="w-cart-total-item">
						<span class="w-cart-total-label">{{ store.labels.pa_items_total }}</span> <strong>{{ formatCurrency(total_items_total) }}</strong>
					</div>
					<div v-if="discount" class="w-cart-total-item">
						<span class="w-cart-total-label">{{ store.labels.pa_total_discount }}</span> <strong class="red">{{ formatCurrency(discount) }}</strong>
					</div>
					<div class="w-cart-total-item">
						<span class="w-cart-total-label">{{ store.labels.pa_total_shipping }}</span>
						<strong v-if="shipping_price && shipping_price > 0">{{ formatCurrency(shipping_price) }}</strong>
						<strong v-else class="green">{{ store.labels.free }}</strong>
					</div>
					<div v-if="couponsList.meta_data" class="w-cart-total-coupons">
						<div v-for="(item, index) in couponsList.meta_data" :key="index" class="w-cart-total-coupon" :class="{'unactive': !item.applied}">
							<div class="title">
								<strong>"{{ item.code }}" </strong>
								<template v-if="item.applied">{{ store.labels.coupon_valid }}</template>
								<template v-else>{{ store.labels.coupon_not_valid }}</template>
							</div>
							<div class="btn-remove" @click="onRemove(item.code)">{{ store.labels.coupon_remove }}</div>
						</div>
					</div>
					<div class="w-cart-total-item w-cart-total-item-payment">
						<span class="w-cart-total-label">{{store.labels.pa_payment_type}}:</span>
						<strong @click="openFlyout({mode: 'payments'})">
							<template v-if="webshopStore.cart?.cart?.payments?.selected?.[0]">
								{{ webshopStore.cart?.cart?.payments?.selected?.[0].title }}
							</template>
							<template v-else>
								-
							</template>
						</strong>
					</div>
					<div v-if="total_regular" class="w-cart-total-item w-cart-total-current">
						<span class="w-cart-total-label">{{ store.labels.pa_total_to_pay }}</span> {{ formatCurrency(total_regular) }}
					</div>
				</div>
			</div>
		</template>
		<div v-else class="wp">
			{{ store.labels.no_products }}
		</div>
	</div>

	<div class="w-cart-bottom">
		<Coupon />
		<AddCard :cart="cart" />
		<div v-if="webshopStore?.cart?.cart?.cart_order_type?.selected == 'reservation_order' || webshopStore?.cart?.cart?.cart_order_type?.selected == 'retail_offer' || webshopStore?.cart?.cart?.cart_order_type?.selected == 'b2b_offer'" class="btn btn-large w-btn-finish cart-finish-shopping" :class="{'disabled': orderEnabled == false}" @click="submitReservation()">
			<template v-if="webshopStore?.cart?.cart?.cart_order_type?.selected == 'reservation_order'">{{ store.labels.pa_reservation_order }}</template>
			<template v-else>{{ store.labels.pa_retail_offer_order }}</template>
		</div>
		<div v-else class="btn btn-large w-btn-finish cart-finish-shopping" :class="{'disabled': orderEnabled == false, 'special': buttonDisabled}" @click="submitOrder()">{{ store.labels.pa_finish_shopping }}</div>
		<Modal :openValue="orderModal" class="modal-content-padding center" :confirm-buttons="true" @confirm="onOrderModalConfirm" @close="orderModal = false">
			<p><strong>{{ store.labels.pa_confirm_avans_sale }}</strong></p>
			<ul v-if="webshopStore?.cart?.cart?.indicators?.confirm_avans_item_pop_up_items?.length">
				<li v-for="item in webshopStore.cart.cart.indicators.confirm_avans_item_pop_up_items" :key="item.id">{{item.title}} ({{item.code}})</li>
			</ul>
		</Modal>
		<ShareCart />
	</div>
	<div class="w-cart-shipping-bar" :class="{'active-item': webshopStore.itemSelected.length, 'active': isFlyoutActive()}">
		<div class="w-cart-shipping-box">
			<div class="btn w-cart-shipping-choose" @click="openShippingOptions()">
				{{ store.labels.pa_choose_shipping }}&nbsp;<span id="choosenItems">({{ webshopStore.itemSelected.length }})</span>
			</div>
			<div v-if="showMergeButton" class="btn btn-white w-cart-parcel-merge" @click="openShippingOptions('merge')">
				<span>{{ store.labels.pa_merge_shipping }} ({{ webshopStore.itemSelected.length }})</span>
			</div>
			<!--
			<div v-if="showAvansForce" class="btn btn-white w-cart-parcel-avans" @click="openAvansForce()">
				<span>{{ store.labels.user_force_complete_prepayment_base_question }} ({{ showAvansForce }})</span>
			</div>
			-->
			<div class="w-cart-shipping-box-right">
				<div class="btn btn-white w-cart-shipping-remove" @click="removeProducts()">
					<span>{{ store.labels.pa_cart_remove_items }}</span>
				</div>
				<div class="btn btn-white w-cart-shipping-deselect" @click="deselectProducts()"></div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {computed, ref, watch, inject} from 'vue';
	import {onClickOutside} from '@vueuse/core';
	import {useForm} from 'vee-validate';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';
	import useHelpers from '@/composables/useHelpers';
	import useFlyout from '@/composables/useFlyout';
	import ShoppingCartProduct from '@/components/webshop/ShoppingCartProduct.vue';
	import Coupon from '@/components/webshop/widget/Coupon.vue';
	import ShareCart from '@/components/webshop/widget/ShareCart.vue';
	import SearchUser from '@/components/webshop/widget/SearchUser.vue';
	import AddCard from '@/components/webshop/widget/AddCard.vue';
	import Modal from '@/components/Modal.vue';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const ep = useEndpoints();
	const props = defineProps(['cart']);
	const {meta} = useForm();
	const {formatCurrency} = useHelpers();
	const {openFlyout, closeFlyout, appendFlyoutData, isFlyoutActive} = useFlyout();

	const total_items_total = computed(() => {
		return webshopStore.cart.total && webshopStore.cart.total.total_items_total ? webshopStore.cart.total.total_items_total : null;
	});
	const total_regular = computed(() => {
		return webshopStore.cart.total && webshopStore.cart.total.total_regular ? webshopStore.cart.total.total_regular : null;
	});
	const shipping_price = computed(() => {
		return webshopStore.cart.total && webshopStore.cart.total.shipping ? webshopStore.cart.total.shipping : null;
	});
	const discount = computed(() => {
		return webshopStore.cart.total && webshopStore.cart.total.discount ? webshopStore.cart.total.discount : null;
	});

	const orderEnabled = computed(() => {
		let value = false;
		if (webshopStore.cart.cart) {
			value = webshopStore.cart.cart.is_order_ready == true ? true : false;
		}
		return value;
	});

	//coupons
	const couponsList = computed(() => {
		return webshopStore?.cart?.total?.extraitems ? webshopStore?.cart?.total?.extraitems.find(el => el.type === 'coupon') : null;
	});

	async function onRemove(value) {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_coupon,
			method: 'DELETE',
			body: {'code': value},
		}).then(async res => {
			await webshopStore.fetchCartItems();
			store.loading = 0;
		});
	}

	//message
	const cartMessage = ref(webshopStore.cart.customer && webshopStore.cart.customer.message ? webshopStore.cart.customer.message : '');

	watch(
		() => webshopStore.cart.customer?.message,
		() => (cartMessage.value = webshopStore.cart.customer && webshopStore.cart.customer.message ? webshopStore.cart.customer.message : ''),
		{deep: true}
	);

	async function submitMessage() {
		store.loading = 1

		await useFetch({
			url: ep.endpoints.value._post_hapi_customer,
			method: 'POST',
			body: {message: cartMessage.value},
		}).then(res => webshopStore.fetchCarts());
	}

	function submitMessageBlur() {
		if(cartMessage.value != webshopStore.cart.customer.message) {
			submitMessage()
		}
	}

	// watch if form is valid
	watch(
		() => meta,
		() => (webshopStore.formValid = meta.value.valid ? 1 : 0),
		{deep: true}
	);

	//remove products
	async function removeProducts() {
		store.loading = 1;

		let shopping_cart_codes = [];
		webshopStore.itemSelected.forEach(el => {
			shopping_cart_codes.push({'shopping_cart_code': el.shopping_cart_code});
		});

		await useFetch({
			url: ep.endpoints.value._delete_hapi_webshop_product,
			method: 'DELETE',
			body: shopping_cart_codes,
		}).then(res => {
			webshopStore.fetchCartItems();
			webshopStore.itemSelected = [];
			webshopStore.unchecked = 1;
			setTimeout(() => {
				webshopStore.unchecked = 0;
			}, 1000);
		});
	}

	//deselect products
	let currentlySelectedItem = ref({});
	function deselectProducts() {
		webshopStore.itemSelected = [];
		webshopStore.unchecked = 1;
		setTimeout(() => {
			webshopStore.unchecked = 0;
		}, 1000);
	}

	//open shippinh options
	async function openShippingOptions(data) {
		let shopping_cart_codes = [];
		webshopStore.itemSelected.forEach(el => {
			shopping_cart_codes.push(el.shopping_cart_code);
		});

		const title = store.labels.pa_choose_shipping;
		const mode2 = (data && data == 'merge') ? 'merge' : null;

		openFlyout({
			mode: 'shipping',
			modeSecond: mode2,
			title: title,
		});

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_possible_delivery_list,
			method: 'POST',
			body: {shopping_cart_codes},
		}).then(res => {
			appendFlyoutData({
				shipping: res.data,
			});
		});
	}

	//open avans force
	async function openAvansForce() {
		openFlyout({
			mode: 'avans',
		});
	}

	//select products
	const allItemsSelected = (items) => {
		return items.every(item => webshopStore.itemSelected.some(el => el.id === item.id));
	}

	const selectAllItems = (items) => {
		if (allItemsSelected(items)) {
			items.forEach(item => {
				const index = webshopStore.itemSelected.findIndex(el => el.id === item.id);
				if (index !== -1) {
					webshopStore.itemSelected.splice(index, 1);
				}
			});
		} else {
			items.forEach(item => {
				if (!webshopStore.itemSelected.some(el => el.id === item.id)) {
					const data = {
						id: item.id,
						shopping_cart_code: item.shopping_cart_code,
						title: item.item.title,
						url: item.item.url,
						code: item.item.code,
						quantity: item.quantity,
						discount_amount: item.discount_amount,
						gross_amount: item.gross_amount,
						total: item.total,
						unit_price: item.unit_price,
						pickup_status: item.pickup_status,
						available_qty: item.available_qty,
						status: item.status,
						user_warehouse: item.user_warehouse,
						is_available: item.is_available,
						status: item.status,
						status2: item.status2,
						date_available: item.date_available,
						group_position: item.sort_group_position,
						avans_enabled: item.avans_enabled,
						force_avans: item.force_avans
					};
					webshopStore.itemSelected.push(data);
				}
			});
		}
	}

	//merge parcels
	const showMergeButton = computed(() => {
		const groupPositions = new Set(webshopStore.itemSelected.map(item => item.group_position));
		return groupPositions.size > 1;
	});

	//force avans
	const showAvansForce = computed(() => {
		const avansEnabled = webshopStore.itemSelected.filter(item => item?.avans_enabled == false && item?.force_avans);
		return avansEnabled.length;
	});

	// show confirmation dialog before order submit
	const orderModal = ref(false);
	async function onOrderModalConfirm(cartType) {
		orderModal.value = false;
		await submitOrder({confirmed: true});
	}

	let buttonDisabled = ref(false);
	//on submit
	async function submitOrder(data = {}) {
		// show confirmation dialog if cart.indicators.confirm_avans_item_pop_up is true and order has not been confirmed (only for personal pickup and if product is not available)
		if(!data?.confirmed && webshopStore?.cart?.cart?.indicators?.confirm_avans_item_pop_up) return orderModal.value = true;

		openFlyout({
			mode: 'order',
			title: store.labels.pa_order_title,
			selectedDate: null,
		})
		/*
		buttonDisabled.value = true;
		store.loading = 2;
		store.creation = true;
		localStorage.setItem('creation', JSON.stringify(store.creation));

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_order,
			method: 'POST',
			body: {'success': true, 'data': webshopStore.cart},
		}).then(async res => {
			if (res.success) {
				await webshopStore.fetchCarts();
				webshopStore.successMessage = res.data;
				
				store.creation = false;
				localStorage.setItem('creation', JSON.stringify(store.creation));
			} else {
				await webshopStore.fetchCarts();
				if(res.data.label_name === 'error_order_has_changed_price') {
					const label = store.labels[res.data.label_name];
					const formattedList = res.data.changed_price_list.map((item, index) => {
						const formattedItem = `${index + 1}. ${item.code} - nova cena: ${item.new_price} €, stara cena: ${item.old_price} €`;
						return formattedItem;
					});
					const replacedLabel = label.replace('%DATA%', formattedList.join('<br>\n'));
					webshopStore.cartError = replacedLabel;
					webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
				} else {
					webshopStore.cartError = store.labels[res.data.label_name] ? store.labels[res.data.label_name] : res.data.label_name;
					webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
				}
				store.loading = 0;
				buttonDisabled.value = false;

				store.creation = false;
				localStorage.setItem('creation', JSON.stringify(store.creation));
			}
		});
		*/
	}

	//on reservetion order
	function submitReservation() {
		openFlyout({
			mode: 'reservation',
			title: store.labels.pa_reservation_order_title,
			selectedDate: null,
		});
	}

	//set due date when error_a_limit_insufficient_total_for_payment appear
	async function setDueDateError() {
		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_cart_order_type_options,
			method: 'POST',
			body: {"specific_payment_due_date_days": 0}
		}).then(async res => {
			if(res.success) {
				await webshopStore.fetchCartItems();
			} else {
				webshopStore.cartError = store.labels[res.data.label_name] ? store.labels[res.data.label_name] : res.data.label_name;
				webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
			}
		});
	}

	//select recipient
	const openRecipient = inject('openRecipient');

	async function clearRecipient(data) {
		store.loading = 1;
		const shoppingCodes = data?.map(item => item.shopping_cart_code) || null;

		await useFetch({
			url: ep.endpoints.value._post_hapi_customer_select_parcel_user,
			method: 'POST',
			body: {shopping_cart_codes: shoppingCodes, address_id: null},
		}).then(res => webshopStore.fetchCarts());
	}

	//open salesman shipping price
	async function openSalesmanShippingPrice(parcel) {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._get_hapi_webshop_shipping_salesman_price_reasons,
			method: 'GET',
		}).then(async res => {
			webshopStore.itemSelected = [];
			parcel.items.forEach(item => {
				if (!webshopStore.itemSelected.some(el => el.id === item.id)) {
					const data = {
						id: item.id,
						shopping_cart_code: item.shopping_cart_code,
						title: item.item.title,
						url: item.item.url,
						code: item.item.code,
						quantity: item.quantity,
						discount_amount: item.discount_amount,
						gross_amount: item.gross_amount,
						total: item.total,
						unit_price: item.unit_price,
						pickup_status: item.pickup_status,
						available_qty: item.available_qty,
						status: item.status,
						user_warehouse: item.user_warehouse,
						is_available: item.is_available,
						status: item.status,
						status2: item.status2,
						date_available: item.date_available,
						group_position: item.sort_group_position,
						avans_enabled: item.avans_enabled,
						force_avans: item.force_avans,
						salesman_discount_price_type: 'shipping',
						salesman_discount_attachments: item.shipping_salesman.discount_attachments,
						salesman_discount_required_reason: item.shipping_salesman.discount_required_reason,
						salesman_discount_internal_reason_id: item.shipping_salesman.discount_internal_reason_id,
						salesman_discount_percent: item.shipping_salesman.discount_percent,
						salesman_discount_price: item.shipping_salesman.discount_price,
					};
					webshopStore.itemSelected.push(data);
				}
			});

			store.loading = 0;
			openFlyout({
				mode: 'salesmanPrice',
				sPriceReasons: res.data,
				shippingPrice: parcel.shipping?.selected?.shipping_price,
				customShippingPrice: parcel.shipping?.selected?.salesman_shipping_price_set,
			})
		});
	}
</script>

<style lang="less" scoped>
	.global-error {
		display: flex; align-items: center; width: calc(~'100% - -2px'); margin-left: -1px; margin-bottom: 0;
		.label{flex-grow: 1;}
		&.special{background: @blue url(/media/images/icons/danger-white.svg) no-repeat 20px 15px; background-size: 25px auto;}
		@media (max-width: @m) {
			width: 100%;
			margin: 0;
			&.special{background: @blue url(/media/images/icons/danger-white.svg) no-repeat 15px 12px; background-size: 20px auto;}
		}
	}
	.error-btn{display: flex; align-items: center; justify-content: center; height: 29px; margin-left: 25px; padding: 2px 15px; border-radius: 15px; background: @white; font-size: 12px; font-weight: bold; color: @textColor; cursor: pointer;}
	.w-cart-message{
		flex-grow: 1; margin-top: 33px;
		textarea{height: 100%; padding: 20px; .placeholder(@textColor, @borderColor);}
		@media (max-width: @m){
			margin-top: 0;
			textarea{height: 110px; padding: 15px; font-size: 13px; line-height: 1.4;}
		}
	}

	.w-cart-total {
		display: flex; flex-direction: column; align-items: flex-end; width: 380px; margin-left: 90px; text-align: right;
		@media (max-width: @t){margin-left: 50px;}
		@media (max-width: @m){width: 100%; margin: 20px 0 0;}
	}
	.w-cart-total-item {
		display: flex; align-items: center; width: 100%; margin-bottom: 5px; font-size: 14px; line-height: 1.4; letter-spacing: -0.3px;
		@media (max-width: @m) {
			font-size: 12px;
		}
	}
	.w-cart-total-item-payment{
		font-weight: bold;
		strong{text-decoration: underline; cursor: pointer;}
	}
	.w-cart-total-item-id{
		display: block; margin-bottom: 10px; padding-bottom: 15px; border-bottom: 1px solid @borderColor; font-size: 12px; line-height: 17px; text-align: right;
		@media (max-width: @m){padding-bottom: 10px;}
	}
	.w-cart-total-label { flex-grow: 1; padding-right: 15px; text-align: left; width: 120px; flex-shrink: 0; }
	.w-cart-total-current {
		margin: 5px 0 0; padding-top: 15px; border-top: 1px solid @borderColor; font-size: 18px; font-weight: bold;
		@media (max-width: @m) {
			padding-top: 10px; font-size: 14px; letter-spacing: -0.24px;
		}
	}

	//coupons
	.w-cart-total-coupons{width: 100%; margin-top: 5px; padding-top: 10px; border-top: 1px solid @gray; text-align: left;}
	.w-cart-total-coupon{
		display: flex; align-items: center; margin-bottom: 10px; font-size: 12px; color: @textColor;
		&.unactive .title{
			color: #b0b0b0;
			&:before{color: #b0b0b0;}
		}
		.title{
			flex-grow: 1; padding: 0 15px 0 30px; position: relative;
			&:before{.icon-coupon; font: 13px/1 @fonti; color: @red; font-weight: bold; position: absolute; top: 2px; left: 0;}
		}
		.btn-remove{
			color: @textColor; text-decoration: underline; text-underline-offset: 2px; cursor: pointer;
			&:hover{text-decoration: none;}
		}
	}

	//user recipient
	.w-cart-group-sw{
		display: flex; align-items: center; justify-content: space-between; width: 100%; padding: 17px 20px 17px 27px; border-bottom: 1px solid @borderColor; font-size: 14px; line-height: 1.4; list-style: -0.17px; cursor: pointer;
		.icon{
			display: flex; align-items: center; padding-left: 28px; position: relative;
			&:before{.icon-location; font: 22px/1 @fonti; color: @blue; position: absolute; top: -3px; left: 0; z-index: 1;}
		}
		.id{color: #7891a9; padding-left: 5px;}
		.mail{padding-left: 25px; font-size: 12px;}
		.empty{font-size: 12px; color: @blue;}

		@media (max-width: @m){
			padding: 15px; border-top: 1px solid @borderColor;
			&.active {width: 100%; flex-shrink: unset; cursor: unset;}
			.icon{
				padding-left: 30px;
				&:before{font-size: 20px; left: 1px;}
			}
		}
	}
	.w-cart-sw-btn{
		display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 30px; height: 30px; background: #eaf1f7; border-radius: 100%; z-index: 11; cursor: pointer;
		&:before{.icon-x; font: 10px/1 @fonti; color: @textColor; font-weight: bold; position: absolute; z-index: 1;}
		@media (min-width: @t) {
			&:hover{
				background: @red;
				&:before{color: @white;}
			}
		}

		@media (max-width: @m){
			width: 20px; height: 20px;
			&:before{font-size: 8px;}
		}
	}
</style>
