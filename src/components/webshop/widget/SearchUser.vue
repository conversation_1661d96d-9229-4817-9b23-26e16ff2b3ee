<template>
	<div class="w-cart-sw">
		<div class="w-cart-sw-col w-cart-sw-col1" :class="{'active': inputValue}" @click="openUser(cart)">
			<span v-if="inputValue" class="icon" :class="{'loyalty': cart.customer.loyalty}">
				<span v-if="cart.customer.first_name" class="name"
					><strong>{{ cart.customer.first_name }} <template v-if="cart.customer.first_name != cart.customer.last_name">{{ cart.customer.last_name }}</template></strong></span
				>
				<span v-if="cart.customer.api_code2" class="id"> ({{ cart.customer.api_code2 }})</span>
				<span v-if="cart.customer.phone && cart.is_phone_visible" class="mail">{{ cart.customer.phone }}</span>
				<span v-if="cart.customer.email && cart.is_email_visible" class="mail">{{ cart.customer.email }}</span>
				<span v-if="webshopStore.cart?.cart?.payment_due_days?.original" class="mail">
					<strong>{{ store.labels.pa_due_days }}:</strong> {{webshopStore.cart.cart.payment_due_days.original}} dni
				</span>
			</span>
			<span v-else class="icon">{{ store.labels.pa_enter_search_user }}</span>
			<div v-if="inputValue" class="w-cart-sw-btn w-cart-sw-update" @click.stop="updateUser()"></div>
			<div v-if="inputValue" class="w-cart-sw-btn w-cart-sw-clear" @click.stop="clearUser()"></div>
		</div>
		<!--
		<div v-if="inputValue" class="w-cart-sw-col w-cart-sw-col2" :class="{'active': cart.customer.is_specific_address_selected}" @click="openRecipient(cart)">
			<span v-if="cart.customer.is_specific_address_selected" class="icon">
				<span v-if="cart.customer.b_first_name" class="name"
					><strong>{{ cart.customer.b_first_name }} <template v-if="cart.customer.b_first_name != cart.customer.b_last_name">{{ cart.customer.b_last_name }}</template></strong></span
				>
				<span v-if="cart.customer.api_code2" class="id"> ({{ cart.customer.api_code2 }}) <br /></span>
				<span v-if="cart.customer.b_address" class="mail">{{ cart.customer.b_address }}, {{ cart.customer.b_zipcode }} {{ cart.customer.b_city }} <br /></span>
				<span v-if="cart.customer.b_phone" class="mail">{{ cart.customer.b_phone }} <br /></span>
				<span v-if="cart.customer.b_email" class="mail">{{ cart.customer.b_email }}</span>
			</span>
			<span v-else class="icon empty"
				><strong>{{ store.labels.pa_enter_recipient }}</strong></span
			>
			<div v-if="cart.customer.is_specific_address_selected" class="w-cart-sw-btn w-cart-sw-clear" @click.stop="clearRecipient()"></div>
		</div>
		-->
	</div>
</template>

<script setup>
	import {computed, inject} from 'vue';
	import {useToken} from '@/composables/useToken';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';

	const props = defineProps(['cart']);
	const token = useToken();
	const store = useStore();
	const webshopStore = useWebshopStore();
	const ep = useEndpoints();

	//open user window
	const openUser = inject('openUser');
	//const openRecipient = inject('openRecipient');

	// select cart
	async function selectCart() {
		store.loading = 2;
		await token.generateToken({
			tokenId: props.cart.token_id,
			tokenHash: props.cart.token_hash,
		});
		webshopStore.fetchCarts();
		store.loading = 0;
	}

	const inputValue = computed(() => {
		if (props.cart && props.cart.customer.is_specific_user_selected && props.cart.customer.first_name != '000' && props.cart.cart_reservation_for != null) {
			return true;
		} else {
			return false;
		}
	});

	async function updateUser() {
		store.loading = 1;

		const addressID = (webshopStore.cart.customer && webshopStore.cart.customer.specific_address_id) ? webshopStore.cart.customer.specific_address_id : null

		await useFetch({
			url: ep.endpoints.value._post_hapi_customer_select_user,
			method: 'POST',
			body: {api_code: props.cart.customer.api_code, address_id: addressID},
		}).then(res => webshopStore.fetchCarts());
	}

	/*
	async function clearRecipient() {
		if (props.cart.cart_active == false) {
			await selectCart();
		}

		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_customer_select_user,
			method: 'POST',
			body: {api_code: props.cart.customer.api_code, address_id: null},
		}).then(res => webshopStore.fetchCarts());
	}
	*/

	async function clearUser() {
		if (props.cart.cart_active == false) {
			await selectCart();
		}

		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._delete_hapi_customer_select_user,
			method: 'DELETE',
			body: [],
		}).then(res => webshopStore.fetchCarts());
	}
</script>

<style lang="less" scoped>
	.w-cart-sw {
		display: flex;
		width: 100%;
		background: @white;
		font-size: 14px;
		line-height: 1.4;
		letter-spacing: -0.17px;
		z-index: 11;

		@media (max-width: @m) {
			display: block;
			font-size: 12px;
		}
	}
	.w-cart-sw-col {
		.id {
			color: #7891a9;
		}
		.mail {
			font-size: 12px;
		}
	}
	.w-cart-sw-col1 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-grow: 1;
		padding: 17px 20px 17px 27px;
		cursor: pointer;
		&.active {
			cursor: unset;
		}
		.icon {
			display: flex;
			align-items: center;
			flex-grow: 1;
			height: 100%;
			padding-left: 33px;
			position: relative;
			&.loyalty:before {
				.icon-user-loyalty;
			}
			&:before {
				.icon-user-empty;
				font: 20px/1 @fonti;
				color: @blue;
				position: absolute;
				left: 1px;
				z-index: 1;
			}
		}
		.id{padding-left: 5px;}
		.mail{padding-left: 25px;}

		@media (max-width: @m) {
			align-items: flex-start; padding: 15px;
			.icon {
				display: block;
				padding-left: 30px;
				.id, .mail{display: block; padding: 2px 0 0 0;}

				&:before {
					font-size: 17px;
					left: 4px;
				}
			}
		}
	}
	.w-cart-sw-col2 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 17px 25px;
		border-left: 1px solid @borderColor;
		cursor: pointer;
		&.active {
			width: 50%;
			flex-shrink: 0;
			cursor: unset;
			.icon {
				height: 100%;
			}
		}
		.icon {
			padding-left: 28px;
			position: relative;
			&:before {
				.icon-location;
				font: 22px/1 @fonti;
				color: @blue;
				position: absolute;
				top: -3px;
				left: 0;
				z-index: 1;
			}
		}
		.empty {
			font-size: 12px;
			color: @blue;
		}

		@media (max-width: @m) {
			padding: 15px;
			border-top: 1px solid @borderColor;
			&.active {
				width: 100%;
				flex-shrink: unset;
				cursor: unset;
			}
			.icon {
				padding-left: 30px;
				&:before {
					font-size: 20px;
					left: 1px;
				}
			}
		}
	}

	.w-cart-sw-btn{
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 30px;
		height: 30px;
		border-radius: 100%;
		z-index: 11;
		cursor: pointer;

		@media (max-width: @m){
			width: 20px;
			height: 20px;
		}
	}
	.w-cart-sw-clear {
		background: #eaf1f7;
		&:before {
			.icon-x;
			font: 10px/1 @fonti;
			color: @textColor;
			font-weight: bold;
			position: absolute;
			z-index: 1;
		}
		@media (min-width: @t) {
			&:hover {
				background: @red;
				&:before {
					color: @white;
				}
			}
		}

		@media (max-width: @m) {
			&:before {
				font-size: 8px;
			}
		}
	}
	.w-cart-sw-update {
		background: @white;
		border: 1px solid @blue;
		margin-right: 15px;
		&:before {
			.icon-reset;
			font: 14px/1 @fonti;
			color: @blue;
			font-weight: 600;
			position: absolute;
			z-index: 1;
		}
		@media (min-width: @t) {
			&:hover {
				background: @blue;
				&:before {
					color: @white;
				}
			}
		}

		@media (max-width: @m) {
			margin-right: 10px;
			&:before {
				font-size: 10px;
			}
		}
	}
</style>
