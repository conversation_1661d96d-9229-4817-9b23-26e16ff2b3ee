<template>
	<div class="ww-coupons">
		<div class="ww-coupons-form">
			<div class="ww-coupons-add">
				<input @keyup.enter="submitCoupon('add')" type="text" v-model="couponCode" :placeholder="store.labels.coupon_enter_code" />
				<span class="ww-btn-add" v-if="couponCode" @click="submitCoupon('add')">{{ store.labels.coupon_btn_add }}</span>
				<div class="coupon_message" v-if="message">{{ message }}</div>
			</div>
		</div>

		<div class="ww-coupons-list-btn-container">
			<div class="ww-coupons-list-btn" @click="openCouponsList()">
				<span>{{ store.labels.pa_coupons }}</span>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {ref, computed} from 'vue';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useFetch} from '@/composables/useFetch';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {onClickOutside} from '@vueuse/core';
	import useFlyout from '@/composables/useFlyout';

	const ep = useEndpoints();
	const store = useStore();
	const webshopStore = useWebshopStore();
	const {openFlyout} = useFlyout();
	const couponCode = ref();
	const message = ref(null);
	const error = ref(0);
	let availableCoupons = ref(null);

	async function submitCoupon(action) {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_coupon,
			method: 'POST',
			body: {'code': [couponCode.value]},
		}).then(async res => {
			if(res.success) {
				await webshopStore.fetchCartItems();
				error.value = 0;
			} else {
				error.value = 1;
			}

			message.value = store.labels[res?.data?.label_name];
			couponCode.value = '';

			setTimeout(function () {
				message.value = '';
			}, 4000);

			store.loading = 0;
		});
	}

	//open coupons list
	async function openCouponsList() {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._get_hapi_webshop_coupon,
			method: 'GET',
		}).then(res => {
			if (res.success) {
				availableCoupons.value = res.data;
			} else {
				availableCoupons.value = store.labels.pa_coupons_empty;
			}
			store.loading = 0;
		});

		const title = store.labels.pa_coupons_list_title;
		
		const couponObject = webshopStore?.cart?.total?.extraitems ? webshopStore?.cart?.total?.extraitems?.find(obj => obj.type === 'coupon') : null;
		const couponsActive = couponObject ? couponObject?.meta_data?.map(meta => meta.code) : null;
		availableCoupons.value = couponsActive ? availableCoupons.value.filter(coupon => !couponsActive.includes(coupon.code)) : availableCoupons.value;

		openFlyout({
			mode: 'coupons',
			title: title,
			availableCoupons: availableCoupons,
		});
	}
</script>

<style lang="less">
	.w-cart-coupon {
		width: 50%;
		flex-grow: 1;
		border-right: 1px solid @borderColor;

		@media (max-width: @t) {
			width: 100%;
			border-right: none;
			border-bottom: 1px solid @borderColor;
		}
		@media (max-width: @m) {
			display: none;
			margin: 0 15px 7px;
			border: none;
			order: 1;
		}
	}

	.ww-coupons {
		display: flex;
		position: relative;
		flex-grow: 1;

		@media (max-width: @m) {
			display: none;
			align-items: flex-start;
			margin: 15px 15px 5px;
		}
	}
	.ww-coupons-add {
		width: 100%;
		position: relative;
		display: inline-block;
		input {
			width: 100%;
			height: 54px;
			border: none;
			padding: 0 55px;
			.placeholder(@textColor, @borderColor);
		}
		&:before {
			.icon-coupon;
			font: 15px/15px @fonti;
			font-weight: bold;
			color: @red;
			position: absolute;
			left: 20px;
			top: 19px;
			z-index: 1;
		}

		@media (max-width: @t) {
			input {
				border: none;
				border-radius: 0;
				padding: 0 65px;
			}
			&:before {
				left: 25px;
			}
		}
		@media (max-width: @m) {
			input {
				height: 40px;
				padding: 0 50px 0 45px;
				border: 1px solid @borderColor;
				border-radius: @borderRadius;
				font-size: 13px;
			}
			&:before {
				font-size: 13px;
				line-height: 13px;
				left: 15px;
				top: 14px;
			}
		}
	}
	.ww-btn-add {
		width: 30px;
		height: 30px;
		font-size: 0;
		line-height: 0;
		border-radius: @borderRadius;
		background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%);
		position: absolute;
		right: 10px;
		top: 13px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		&:before {
			.icon-check;
			font: 11px/11px @fonti;
			color: @white;
			z-index: 1;
		}

		@media (max-width: @t) {
			right: 25px;
		}
		@media (max-width: @m) {
			top: 5px;
			right: 7px;
		}
	}
	.ww-coupons-form {
		flex-grow: 1;
	}
	.ww-coupons {
		&.active {
			.ww-coupons-form,
			.ww-coupons-list {
				display: none;
			}
			.ww-coupons-active {
				display: flex;
				text-align: left;
			}
		}
	}
	.ww-coupons-active {
		display: none;
		flex-grow: 1;
		align-items: center;
		height: 54px;
		font-size: 14px;
		padding: 0 45px 0 20px;
		position: relative;

		@media (max-width: @t) {
			padding: 15px 65px 15px 25px;
		}
		@media (max-width: @m) {
			height: 40px;
			padding: 0 5px;
			font-size: 12px;
		}
	}
	.ww-coupons-title {
		p {
			padding-bottom: 0;
			display: inline;
		}
		span {
			font-weight: bold;
		}
	}
	.ww-coupon-delete {
		width: 30px;
		height: 30px;
		background: @red;
		border-radius: @borderRadius;
		font-size: 0;
		line-height: 0;
		position: absolute;
		top: 13px;
		right: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		.transition(all);
		&:before {
			.icon-x;
			font: 14px/14px @fonti;
			color: @white;
		}
		&:hover {
			background: @red / 1.3;
		}

		@media (max-width: @t) {
			right: 25px;
		}
		@media (max-width: @m) {
			top: 5px;
			right: 7px;
		}
	}
	.coupon_message {
		font-size: 12px;
		line-height: 15px;
		margin: 7px 0 0 20px;
		position: absolute;

		@media (max-width: @t) {
			margin: 7px 0 5px 25px;
		}
		@media (max-width: @m) {
			font-size: 11px;
			margin: 5px 0 7px 15px;
			position: relative;
		}
	}
	.coupon_message_response_error {
		color: @red;
	}
	.coupon_message_response_ok {
		color: @green;
	}

	.ww-coupons-list-btn-container {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 12px;
		border-left: 1px solid @borderColor;

		@media (max-width: @m) {
			margin-left: 5px;
			padding: 0;
			border: none;
		}
	}
	.ww-coupons-list-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 30px;
		padding: 0 12px;
		background: linear-gradient(63.43deg, #0050a0 0%, #0078b4 100%);
		border-radius: 2px;
		font-size: 12px;
		line-height: 1.2;
		font-weight: bold;
		letter-spacing: -0.24px;
		color: @white;
		cursor: pointer;
		.transition(opacity);
		&.disabled {
			pointer-events: none;
			opacity: 0.5;
		}
		span {
			padding-left: 18px;
			position: relative;
			&:before {
				.icon-search;
				font: 12px/1 @fonti;
				color: @white;
				font-weight: normal;
				position: absolute;
				left: 0;
				top: 1px;
			}
		}
		&:hover {
			opacity: 0.8;
		}

		@media (max-width: @m) {
			height: 40px;
			padding: 0 15px;
		}
	}
</style>
