<template>
	<div class="imei-row">
		<label :for="'imei-' + itemIndex">{{ store.labels.pa_imei_number }} {{ itemIndex + 1 }}</label>
		<input autocomplete="off" @blur="blur" @keyup="isTyping($event)" v-model="searchTerm" type="text" :id="'imei' + itemIndex" :placeholder="store.labels.pa_imei_number_placeholder" :class="{'imei-error': imeiError && searchTerm.length, 'fw-b': searchTerm}" />
		<span v-if="imeiError && searchTerm.length" class="error error-imei">{{ store.labels.pa_imei_error }}</span>
		<template v-if="warnings && imeiWarning == true">
			<span v-for="(warning, index) in warnings" :key="index" class="error error-imei">
				<template v-if="warning.label_name === 'warning_imei_number_invalid' && itemIndex === warning.index">{{ store.labels.warning_imei_number_invalid }}</template>
			</span>
		</template>
		<div class="ac special" v-if="autocompleteActive && searchTerm && autocompleteItems.length">
			<ul class="ac-items" v-if="autocompleteItems">
				<li class="ac-item" v-for="(item, index) in autocompleteItems" :key="item.id" @click="setSelectedItem(index), navigate()" :class="{active: dirty && selectedIndex == index}" :data-index="index">
					<div class="ac-cnt">{{ item }}</div>
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup>
	import {ref, onMounted, watch} from 'vue';
	import {useStore} from '@/stores';

	const store = useStore();
	const props = defineProps(['item', 'itemIndex', 'selectedImeis', 'scanedValue', 'warnings']);
	const emit = defineEmits(['input']);

	const searchTerm = ref();
	const autocompleteItems = ref([]);
	const selectedItem = ref({});
	const selectedIndex = ref(-1);
	const dirty = ref(0);
	const autocompleteActive = ref(0);
	const imeiError = ref(false);
	const imeiWarning = ref(false);

	onMounted(() => {
		if (props.selectedImeis && props.selectedImeis.length > 0) {
			searchTerm.value = props.selectedImeis[props.itemIndex];
			emit('input', searchTerm.value);
		}

		if (props.warnings) {
			const isIndex = props.warnings.some(item => item.index === props.itemIndex);
			if (isIndex) {
				imeiWarning.value = true;
			} else {
				imeiWarning.value = false;
			}
		}
	});

	watch(
		() => props.scanedValue,
		newValue => {
			if (newValue != null) {
				searchTerm.value = newValue;
				search();
				sendValue();
				autocompleteActive.value = 0;
			}
		}
	);

	async function isTyping(event) {
		if (event.key == 'ArrowDown') {
			dirty.value = 1;
			const totalItems = autocompleteItems.value.length - 1;
			selectedIndex.value = selectedIndex.value >= totalItems ? totalItems : selectedIndex.value + 1;
			setSelectedItem();
			return true;
		}

		if (event.key == 'ArrowUp') {
			dirty.value = 1;
			selectedIndex.value = selectedIndex.value <= 0 ? 0 : selectedIndex.value - 1;
			setSelectedItem();
			return true;
		}

		if (event.key == 'Enter') {
			if (dirty.value) {
				navigate();
			}
			return true;
		}

		if (event.key == 'Escape') {
			blur();
			return true;
		}

		imeiWarning.value = false;

		search();
		sendValue();
	}

	function navigate() {
		autocompleteItems.value = [];
		dirty.value = 0;
	}

	function setSelectedItem(index = selectedIndex.value) {
		searchTerm.value = autocompleteItems.value[index];
		imeiErrorCheck();
	}

	function reset() {
		dirty.value = 0;
		autocompleteItems.value = [];
		selectedItem.value = {};
		selectedIndex.value = -1;
		autocompleteActive.value = 0;
		sendValue();
	}

	function sendValue() {
		emit('input', searchTerm.value);
	}

	async function search() {
		let itemsValue = [];

		if (searchTerm.value && searchTerm.value.length) {
			let input = searchTerm.value;

			store.flyout.availableImeis.forEach(el => {
				if (el.includes(input)) {
					autocompleteActive.value = 1;
					itemsValue.push(el);
					selectedIndex.value = -1;
				}
			});
			autocompleteItems.value = itemsValue;
			imeiErrorCheck();
		} else {
			autocompleteItems.value = [];
			itemsValue = [];
			autocompleteActive.value = 0;
		}
	}

	function imeiErrorCheck() {
		if (autocompleteItems.value && searchTerm.value && autocompleteItems.value.includes(searchTerm.value)) {
			imeiError.value = false;
		} else {
			imeiError.value = true;
		}
	}

	function blur() {
		setTimeout(() => {
			reset();
		}, 100);
	}
</script>

<style lang="less" scoped>
	.imei-row {
		display: block;
		margin-bottom: 30px;
		position: relative;
		label {
			display: block;
			margin-bottom: 5px;
			padding: 0;
			font-size: 12px;
			line-height: 17px;
			letter-spacing: -0.24px;
			font-weight: bold;
		}
		input {
			color: @blue;
			.placeholder(@textColor, @borderColor);
		}
	}
	.imei-error {
		border-color: @red;
	}

	.flyout-autocomplete {
		flex-grow: 1;
		position: relative;
		&:before {
			.icon-search;
			font: 18px/1 @fonti;
			color: @blue;
			position: absolute;
			left: 20px;
			top: 16px;
			z-index: 1;
		}
	}
	.autocomplete-input {
		padding: 0 50px;
		box-shadow: 0 10px 20px 0 rgba(0, 34, 67, 0.03);
		.placeholder(@textColor, @borderColor);

		@media (max-width: @m) {
			width: calc(~'100% - 50px');
			height: 50px;
			box-shadow: none;
			border: none;
			border-right: 1px solid @borderColor;
		}
	}
	.autocomplete-clear-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
		background: @white;
		position: absolute;
		right: 5px;
		top: 5px;
		cursor: pointer;
		z-index: 1;
		&:before,
		&:after {
			.pseudo(14px,2px);
			background: @textColor;
			.rotate(-45deg);
		}
		&:after {
			.rotate(45deg);
		}

		@media (max-width: @m) {
			right: 55px;
		}
	}

	.ac-background {
		display: none;
		@media (max-width: @m) {
			display: block;
			position: fixed;
			top: 50px;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1;
			background-color: rgba(0, 24, 47, 0.6);
		}
	}
	.ac {
		position: absolute;
		top: 71px;
		left: 1px;
		right: 1px;
		background: #fff;
		z-index: 100;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
		font-size: 14px;

		@media (max-width: @m) {
			left: 0;
			right: 0;
		}
	}
	.ac-items {
		overflow: auto;
		max-height: 350px;
	}
	.ac-item {
		border-bottom: 1px solid @borderColor;
		&:hover,
		&.active {
			background: #f8f9fa;
			.ac-title {
				color: @blue;
			}
		}
	}
	.ac-cnt {
		display: flex;
		align-items: flex-start;
		width: 100%;
		padding: 10px 20px;
		border-bottom: 1px solid @borderColor;
		font-size: 14px;
		font-weight: bold;
		&:last-child {
			border: none;
		}
	}
</style>
