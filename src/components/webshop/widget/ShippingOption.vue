<template>
	<div class="field-shipping-row" v-for="shipping in data.shipping" :key="shipping.id">
		<span class="field radio-field">
			<input :disabled="!shipping.enabled" v-model="value" type="radio" name="shipping" :value="shipping.code" :id="shipping.code" @click="onShipping(shipping, null)" />
			<label :for="shipping.code">{{ shipping.title }}</label>
		</span>

		<div v-if="value == 'osobno_preuzimanje' && shipping.code == 'osobno_preuzimanje' && shipping.pickup_locations" class="field-shipping-subitem pickup-locations special">
			<div class="field-shipping-row" v-for="location in shipping.pickup_locations" :key="location.id">
				<span class="field radio-field">
					<input v-model="selectedLocation" type="radio" :id="'location-' + location.id" name="pickup_location" @click="onShipping(shipping, location)" :value="location.id" />
					<label :for="'location-' + location.id"
						><span :class="['location-item-label', {'unavailable': location.has_stock == false}]">{{ location.title }}</span></label
					>
				</span>
			</div>
		</div>

		<div v-if="value == shipping.code && shipping.delivery_date_changeable" class="field-shipping-subitem special">
			<Datepicker
				v-model="selectedDate"
				:clearable="false"
				:enable-time-picker="false"
				@update:modelValue="updateSelectedDate"
				auto-apply
				:start-date="shipping.min_date"
				:min-date="shipping.min_date"
				:max-date="shipping.max_date"
				:format="formatDate"
				:day-names="['Po', 'To', 'Sr', 'Če', 'Pe', 'So', 'Ne']" />
		</div>
	</div>
	<div v-if="data.shipping && !data.shipping.length">Načini pošiljanja niso na voljo</div>
</template>

<script setup>
	import {ref, watch} from 'vue';
	import {useStore} from '@/stores';
	import Datepicker from '@vuepic/vue-datepicker';
	import '@vuepic/vue-datepicker/dist/main.css';

	const store = useStore();
	const props = defineProps(['data']);
	const value = ref();
	const selectedDate = ref();
	const selectedLocation = ref();

	const formatDate = date => {
		const day = date.getDate();
		const month = date.getMonth() + 1;
		const year = date.getFullYear();

		return `${day}.${month}.${year}`;
	};

	function updateSelectedDate(modelData) {
		store.flyout.selectedDate = modelData.toISOString().split('T')[0];
	}

	function onShipping(shipping, location) {
		selectedDate.value = shipping.delivery_date_changeable && shipping.min_date ? shipping.min_date : '';
		store.flyout.selectedShippingId = shipping.id;
		store.flyout.selectedShippingCode = shipping.code;

		if (shipping.code == 'osobno_preuzimanje') {
			if (location) {
				store.flyout.selectedLocation = location.id;
			}

			if (!location) {
				const defaultShipping = shipping.pickup_locations[0].id;
				store.flyout.selectedLocation = selectedLocation.value ? selectedLocation.value : defaultShipping;
				if (!selectedLocation.value) {
					selectedLocation.value = defaultShipping;
				}
			}
		} else {
			store.flyout.selectedLocation = '';
		}

		store.flyout.selectedDate = selectedDate.value;
		store.flyout.validForm = 1;
	}
</script>

<style lang="less" scoped>
	.location-item-label {
		padding-left: 22px;
		position: relative;
		&:before {
			.icon-check;
			font: 11px/11px @fonti;
			color: @green;
			position: absolute;
			left: 0;
			top: 3px;
		}

		&.unavailable:before {
			.icon-x;
			font: 13px/13px @fonti;
			color: #a9afb4;
			top: 2px;
		}
	}
</style>
