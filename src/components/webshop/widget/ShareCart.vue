<template>
	<a class="w-cart-qr-share btn" @click="generateUrl(), generateQR(), (modalStatus = 1)">Share</a>
	<Teleport to="body">
		<Modal :openValue="modalStatus" @close="(modalStatus = 0), reset()">
			<div class="cart-share-cnt">
				<div class="cart-share-row cart-share-row-qr" v-if="cartQR">
					<div class="cart-share-subtitle">{{ store.labels.qr_code_for_customer }}</div>
					<img :src="cartQR" width="140" alt="" />
				</div>

				<div class="cart-share-row cart-share-row-url" v-if="cartUrl">
					<div class="cart-share-subtitle">{{ store.labels.url_code_for_pa }}</div>
					<a :href="cartUrl" target="_blank">{{ cartUrl }}</a>
				</div>

				<div class="cart-share-row cart-share-row-email">
					<div class="cart-share-subtitle">{{ store.labels.email_code_for_pa }}</div>
					<div class="cart-share-msg" v-if="msg">{{ store.labels[msg] }}</div>
					<p><input type="email" v-model="email" @keyup="validate($event)" :placeholder="store.labels.pa_enter_email" /></p>
					<button :disabled="!valid" class="cart-share-submit" @click="submit()">{{ store.labels.send }}</button>
				</div>
			</div>
		</Modal>
	</Teleport>
</template>

<script setup>
	import {ref} from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';
	import Modal from '@/components/Modal.vue';

	const store = useStore();
	const ep = useEndpoints();
	const webshopStore = useWebshopStore();
	const modalStatus = ref(0);
	const cartUrl = ref();
	const cartQR = ref();
	const email = ref();
	const valid = ref(0);
	const msg = ref();

	async function generateUrl() {
		const endpoint = ep.endpoints.value._get_hapi_generate_cart_url_to_web.replace('%APP_TOKEN_ID%', webshopStore.cart.cart.token_id).replace('%CODE%', webshopStore.cart.cart.quick_cart_code);
		return await useFetch({url: endpoint}).then(res => {
			cartUrl.value = res.data.public_url;
		});
	}

	async function generateQR() {
		const endpoint = ep.endpoints.value._get_hapi_generate_cart_qr_to_web.replace('%APP_TOKEN_ID%', webshopStore.cart.cart.token_id).replace('%CODE%', webshopStore.cart.cart.quick_cart_code);
		return await useFetch({url: endpoint}).then(res => {
			cartQR.value = res.data.public_url;
		});
	}

	function reset() {
		valid.value = 0;
		msg.value = '';
		email.value = '';
	}

	function validate(event) {
		valid.value = /(.+)@(.+){2,}\.(.+){2,}/.test(email.value) ? 1 : 0;

		if (event.key == 'Enter' && valid.value) {
			submit();
		}
	}

	async function submit() {
		store.loading = 1;
		await useFetch({
			url: ep.endpoints.value._post_hapi_email_cart_to_customer,
			method: 'POST',
			body: {
				email: email.value,
				url: cartUrl.value,
			},
		}).then(res => {
			msg.value = res.data.label_name;
			email.value = '';
			valid.value = 0;
			store.loading = 0;
		});
	}
</script>

<style lang="less" scoped>
	.cart-share-submit {
		width: 100%;
		min-height: 45px;
		font-size: 15px;
		&:disabled {
			opacity: 0.6;
		}
	}
	input {
		height: 45px;
	}
	.cart-share-cnt {
		padding: 30px;
		font-size: 15px;

		@media (max-width: @m) {
			padding: 20px;
		}
	}
	a {
		color: @blue;
	}
	.cart-share-row-url {
		padding: 5px 0 15px;
	}
	.cart-share-subtitle {
		font-weight: bold;
		padding-bottom: 5px;
	}
	.cart-share-msg {
		font-weight: bold;
		padding: 0 0 8px;
		color: @blue;
	}
</style>
