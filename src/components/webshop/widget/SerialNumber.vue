<template>
	<div class="wp-serial-number" v-if="item.serial_number_mandatory">
		<a class="wp-serial-number-btn" :class="{'active': item.serial_numbers.length >= item.quantity}" @click="openModal">{{ store.labels.pa_serial_number }}</a>
	</div>
	<Modal :openValue="modalStatus" @scan="onScan" @close="modalStatus = 0" :title="store.labels.scan_barcode + '<span class=\'counter\'>(' + item.quantity + ')</span>'" mode="scanner">
		<div class="wqr-scan-content">
			<div class="serial-row" v-for="(item, index) in item.quantity" :key="item">
				<label :for="'serial-' + item">{{ store.labels.pa_serial_number }} {{ item }}</label>
				<input @keyup="validate(0)" type="text" :id="'serial-' + item" placeholder="V<PERSON><PERSON> ali sken<PERSON>j barkod" ref="inputs" />
				<div v-if="duplicateSerials.includes(index)" class="error">{{ store.labels.pa_serial_number_duplicate_error }}</div>
			</div>
		</div>
		<div class="wqr-scan-content wqr-scan-footer">
			<button class="btn" :disabled="disabledButton" @click="submit">{{ store.labels.confirm }}</button>
		</div>
	</Modal>
</template>

<script setup>
	import Modal from '@/components/Modal.vue';
	import {ref, computed} from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	const props = defineProps(['item']);

	const store = useStore();
	const webshopStore = useWebshopStore();
	const modalStatus = ref();
	const inputs = ref([]);
	const disabledButton = ref(1);
	const serials = ref([]);
	const alphanumericRegex = /^[a-zA-Z0-9\s-]*$/;
	const msg = ref();
	const duplicateSerials = ref([]);

	function openModal() {
		modalStatus.value = 1;
		setTimeout(function () {
			populateFields();
			validate(1);
		}, 200);
	}

	function populateFields() {
		const s = props.item.serial_numbers;
		if (s) {
			for (let i = 0; i < props.item.quantity; i++) {
				if (s[i]) {
					inputs.value[i].value = s[i];
				}
			}
		}
	}

	function validate(focus = false) {
		const fields = inputs.value;
		let skip = false;
		let tempSerials = [];
		duplicateSerials.value = [];

		fields.forEach(el => {
			if (skip) return;

			if (!el.value) {
				if (focus) {
					el.focus();
				}
				skip = true;
				disabledButton.value = 1;
				return;
			} else {
				disabledButton.value = 0;
			}

			// Remove invalid characters from the input value
			el.value = el.value.replace(new RegExp(`[^a-zA-Z0-9\\s\\-]+`, 'g'), '');

			if (!alphanumericRegex.test(el.value)) {
				// If the input doesn't match the regex after removing invalid characters, handle it as needed
				if (focus) {
					el.focus();
				}
				skip = true;
				disabledButton.value = 1;

				return;
			}

			tempSerials.push(el.value);
		});

		const serialCounts = tempSerials.reduce((acc, serial, idx) => {
			if (serial in acc) {
				acc[serial].count += 1;
				acc[serial].indices.push(idx);
			} else {
				acc[serial] = { count: 1, indices: [idx] };
			}
			return acc;
		}, {});

		const duplicates = Object.values(serialCounts).filter(item => item.count > 1);
		if (duplicates.length) {
			duplicateSerials.value = duplicates.flatMap(item => item.indices);
			disabledButton.value = 1;
		} else {
			duplicateSerials.value = [];
		}

		serials.value = tempSerials;
	}

	function onScan(payload) {
		const activeElement = document.activeElement;
		activeElement.value = payload;
		validate(1);
	}

	async function submit() {
		await webshopStore
			.updateProduct({
				shopping_cart_code: props.item.shopping_cart_code,
				quantity: props.item.quantity,
				serial_numbers: serials.value,
			})
			.then(res => (modalStatus.value = 0));
	}
</script>

<style lang="less" scoped>
	label {
		font-size: 12px;
		line-height: 1.2;
		font-weight: bold;
	}
	input {
		height: 45px;
		&::-webkit-input-placeholder {
			color: #ccc;
		}
		&:-ms-input-placeholder {
			color: #ccc;
		}
		&::-moz-placeholder {
			color: #ccc;
		}
		&:focus {
			border-color: @blue;
			&::-webkit-input-placeholder {
				color: transparent;
			}
			&:-ms-input-placeholder {
				color: transparent;
			}
			&::-moz-placeholder {
				color: transparent;
			}
		}
	}
	.serial-row {
		padding-bottom: 10px;
	}
	.wp-serial-number-btn {
		border: 1px solid @borderColor;
		font-weight: bold;
		font-size: 12px;
		height: 44px;
		line-height: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 15px;
		cursor: pointer;
		position: relative;
		white-space: nowrap;
		&:before {
			.icon-barcode;
			font: 16px/1 @fonti;
			margin: 0 7px 0 0;
			color: @red;
		}
		&.active:before {
			color: @blue;
		}
		@media (max-width: @m) {
			height: 36px;
			padding: 0 10px;
		}
	}
	.wqr-scan-content {
		max-height: 250px;
		overflow: auto;
	}
	.wqr-scan-footer {
		border-top: 1px solid @borderColor;
	}
	.btn {
		width: 100%;
		min-height: 50px;
		font-size: 14px;
	}
	.wqr-scan-msg {
		color: @textColor;
		position: absolute;
		left: 0;
		top: 100%;
	}
</style>
