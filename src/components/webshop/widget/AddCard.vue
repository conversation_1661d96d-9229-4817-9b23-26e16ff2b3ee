<template>
	<div class="w-card-code" :class="{'disabled': webshopStore?.cart?.cart?.indicators?.manual_shopping_cart_code_required == false}" @click="(cart.previous_order_id) ? readonlyOnClick() : null">
		<template v-if="webshopStore?.cart?.cart?.indicators?.manual_shopping_cart_code_required">
			<div class="w-card-code-btn w-card-code-scan" v-if="!code && !cart.previous_order_id" @click="(modalStatus = 1), (camLoading = 1)"></div>
			<div class="w-card-code-btn w-card-code-confirm" v-if="code && code != cart.cart_code" @click="submit"></div>
			<div class="w-card-code-btn w-card-code-remove" v-if="code && code == cart.cart_code" @click="submit('remove')"></div>
		</template>
		<input v-if="cart.previous_order_id" readonly class="pa-cart-code w-card-code-input readonly" name="pa_cart_code" type="text" v-model="code" :placeholder="store.labels.pa_enter_cart_code" />
		<input v-else class="pa-cart-code w-card-code-input" name="pa_cart_code" type="text" v-model="code" :placeholder="store.labels.pa_enter_cart_code" maxlength="36" @input="onInput" />
		<span class="w-card-msg" :class="{error: isError}" v-if="cart.previous_order_id && msg">
			<span class="w-card-msg-close" v-show="isError" @click="clearMessage()">X</span>
			{{ msg }}
			<template v-if="msgDescription"><br>({{ msgDescription }})</template>
		</span>
		<span class="w-card-msg" :class="{error: isError}" v-if="msg">
			<span class="w-card-msg-close" v-show="isError" @click="clearMessage()">X</span>
			{{ (msg && store.labels?.[msg]) ? store.labels[msg] : msg }}
			<template v-if="msgDescription"><br>({{ msgDescription }})</template>
		</span>
	</div>

	<Modal :openValue="modalStatus" @close="modalStatus = 0" @scan="onScan" :title="store.labels.pa_enter_physical_cart_code" mode="scanner" />
</template>

<script setup>
	import {ref, watch} from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useEventBus} from '@/composables/useEventBus';
	import Modal from '@/components/Modal.vue';

	const props = defineProps(['cart']);
	const store = useStore();
	const webshopStore = useWebshopStore();
	const modalStatus = ref(0);
	const code = ref(props.cart.cart_code);
	const msg = ref();
	const isError = ref(false);
	const msgDescription = ref();
	const {bus} = useEventBus();

	function onScan(payload) {
		code.value = payload;
		modalStatus.value = 0;
		submit();
	}

	watch(() => webshopStore?.cart?.cart?.code, (newValue) => {
		if(newValue == null) {
			code.value = null;
		}
	});

	watch(
		() => bus.value.event,
		() => {
			if(bus?.value?.event == 'clearCartCode') {
				code.value = null;
				bus.value.event = null;
			}
		}
	);

	let msgTimeout;
	async function submit(mode) {
		clearMessage();
		if(msgTimeout) {
			clearTimeout(msgTimeout);
		}

		const cardCode = mode == 'remove' ? '' : code.value;
		await webshopStore.addCurrentCard(cardCode).then(res => {
			if (res.data.label_name == 'error_cart_with_given_cart_code_already_exists') {
				webshopStore.updateCart = [res.data, code.value];
			}

			if (res.data.label_name == 'error_order_with_given_cart_code_already_exists') {
				webshopStore.updateCart = [res.data, code.value];
			}

			if (mode == 'remove' || res.data.label_name == 'error_previous_order_does_not_exist') {
				code.value = '';
			}
			msg.value = res.data?.label_name ? res.data.label_name : null;
			let desc = res.data?.label_description ? res.data.label_description : null;

			const id = res.data?.internal_id ? res.data.internal_id : null;
			if(id) desc += ' ID: ' + id;
			
			msgDescription.value = desc ? desc : null;
			
			if(!res.success) {
				isError.value = true;
				return false;
			}

			msgTimeout = setTimeout(function () {
				clearMessage();
			}, 4000);
		});
	}

	function onInput(e) {
		const sanitizedValue = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
		code.value = sanitizedValue;
		clearMessage();
	}

	function clearMessage() {
		msg.value = '';
		msgDescription.value = '';
		isError.value = false;
	}

	let readonlyTimeout;
	function readonlyOnClick(){
		if(readonlyTimeout) {
			clearTimeout(readonlyTimeout);
		}
		if(props.cart.previous_order_id) {
			msg.value = store.labels.error_previous_order_id;
			readonlyTimeout = setTimeout(function () {
				clearMessage();
			}, 4000);
		}
	}
</script>

<style lang="less" scoped>
	.btn {
		width: 100%;
		background: linear-gradient(63.43deg, #0050a0 0%, #0078b4 100%);
	}
	.w-card-code {
		width: 50%; max-width: 400px; flex-grow: 1; position: relative; border-left: 1px solid @borderColor; margin-left: auto;
		&.disabled{pointer-events: none; opacity: 0.5;}
		input.readonly {
			pointer-events: none;
		}
		input {
			width: calc(~'100% - 1px'); height: 54px; padding: 0 90px 0 20px; border: none; .placeholder(@textColor, @borderColor); .transition(padding);
			&.field_error_input {
				outline: 1px solid @red;
			}
		}

		@media (max-width: @m) {
			display: none; width: 100%; max-width: unset; margin: 5px 15px 15px; border: none; border-radius: @borderRadius;
			input { height: 40px; padding: 0 80px 0 15px; border: 1px solid @borderColor; border-radius: @borderRadius; font-size: 13px; }
		}
	}
	.w-card-code-btn { width: 30px; height: 30px; border-radius: @borderRadius; position: absolute; top: 13px; display: flex; align-items: center; justify-content: center; cursor: pointer; .transition(all); z-index: 1; }
	.w-card-code-scan {
		background: linear-gradient(63.43deg, #0050a0 0%, #0078b4 100%); right: 10px;
		&:before { .icon-face-scan; font: 16px/16px @fonti; color: @white; z-index: 1; }

		@media (max-width: @m) {
			top: 5px; right: 7px;
		}
	}
	.w-card-code-confirm,
	.w-card-code-remove {
		background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%); right: 10px;
		&:before { .icon-check; font: 11px/11px @fonti; color: @white; z-index: 1; }

		@media (max-width: @m) {
			top: 5px; right: 7px;
		}
	}
	.w-card-code-remove {
		background: @red;
		&:before { .icon-x; font: 14px/14px @fonti; color: @white; }
	}
	.w-card-msg{
		color: @textColor; color: @green; position: absolute; left: 0; width: 100%; top: 100%; padding: 8px 20px; font-size: 12px;
		@media (max-width: @ms){position: relative;}
		&.error{background: @red; color: #fff; padding-right: 30px;}
	}
	.w-card-msg-close{
		position: absolute; top: 4px; right: 5px; width: 25px; height: 25px; display: flex; align-items: center; justify-content: center; cursor: pointer; color: #fff; font-size: 0;
		&:before{.icon-x; font: 12px/1 @fonti;}
	}
</style>