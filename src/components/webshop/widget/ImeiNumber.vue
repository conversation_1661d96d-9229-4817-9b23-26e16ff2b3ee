<template>
	<div class="wp-serial-number" v-if="item.imei_mandatory">
		<a class="wp-serial-number-btn" :class="{'active': item.imeis.length >= item.quantity}" @click="openImeiList(item.item.code)">{{ store.labels.pa_imei_number }}</a>
	</div>
</template>

<script setup>
	import {ref} from 'vue';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useFetch} from '@/composables/useFetch';
	import {useStore} from '@/stores';
	import useFlyout from '@/composables/useFlyout';

	const ep = useEndpoints();
	const store = useStore();
	const {openFlyout} = useFlyout();
	const props = defineProps(['item']);
	let availableImeis = ref(null);

	//open imei list
	async function openImeiList(code) {
		store.loading = 1;

		const imeiListValue = ep.endpoints.value._get_hapi_webshop_imei_list;
		const imeiListEp = imeiListValue.replace('%CODE%', code);
		let imeiInputsVisible = 3;

		await useFetch({
			url: imeiListEp,
			method: 'GET',
		}).then(res => {
			availableImeis.value = res.data.imei_numbers;
			if (props.item.imeis.length > 2) {
				imeiInputsVisible = props.item.imeis.length + 1;
			}

			store.loading = 0;

			openFlyout({
				mode: 'imei',
				availableImeis: availableImeis,
				selectedImeis: props.item.imeis,
				imeiProductWarnings: props.item.warnings,
				imeiProductCode: props.item.shopping_cart_code,
				imeiInputsVisible: imeiInputsVisible,
				imeiProductQty: props.item.quantity,
			});
		});
	}
</script>

<style lang="less" scoped>
	label {
		font-size: 12px;
		line-height: 1.2;
		font-weight: bold;
	}
	input {
		height: 45px;
		&::-webkit-input-placeholder {
			color: #ccc;
		}
		&:-ms-input-placeholder {
			color: #ccc;
		}
		&::-moz-placeholder {
			color: #ccc;
		}
		&:focus {
			border-color: @blue;
			&::-webkit-input-placeholder {
				color: transparent;
			}
			&:-ms-input-placeholder {
				color: transparent;
			}
			&::-moz-placeholder {
				color: transparent;
			}
		}
	}
	.serial-row {
		padding-bottom: 10px;
	}
	.wp-serial-number-btn {
		border: 1px solid @borderColor;
		font-weight: bold;
		font-size: 12px;
		height: 44px;
		line-height: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 15px;
		cursor: pointer;
		position: relative;
		white-space: nowrap;
		&:before {
			.icon-barcode;
			font: 16px/1 @fonti;
			margin: 0 7px 0 0;
			color: @red;
		}
		&.active:before {
			color: @blue;
		}
		@media (max-width: @m) {
			height: 36px;
			padding: 0 10px;
		}
	}
	.wqr-scan-content {
		max-height: 250px;
		overflow: auto;
	}
	.wqr-scan-footer {
		border-top: 1px solid @borderColor;
	}
	.btn {
		width: 100%;
		min-height: 50px;
		font-size: 14px;
	}
</style>
