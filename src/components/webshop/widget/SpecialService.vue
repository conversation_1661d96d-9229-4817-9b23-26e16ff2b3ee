<template>
	<div class="wp-extra-benefits" v-if="specialService">
		<div class="cd-extra-benefit-item wp-extra-benefit-item">
			<div class="cd-extra-benefit-row" @click="updateService()">
				<input :checked="specialService?.options?.selected_id" type="checkbox" />
				<label class="cd-extra-benefit">
					<div class="cd-extra-benefit-title">
						{{ store.labels.selected_assembly }}: <strong v-if="specialService.options">{{ specialService.options.selected_value }}</strong>
					</div>
				</label>
				<div class="cd-extra-benefit-price wp-extra-benefit-price special">
					<span>{{ specialService?.options?.selected_id ? store.labels.m_remove_product : store.labels.add_assembly }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {ref, computed, watch} from 'vue';
	const props = defineProps(['data']);
	const store = useStore();
	const webshopStore = useWebshopStore();
	const serviceId = ref();

	const specialService = computed(() => {
		if (props.data.services?.special[0]) {
			serviceId.value = props.data.services.special[0].id;
			return props.data.services.special[0];
		}
	});

	const updateService = async () => {
		let data = [];

		// get selected insurances
		if (props.data.insurances && props.data.insurances.selected) {
			props.data.insurances.selected.forEach(el => {
				data.push(el.id);
			});
		}

		// get selected services
		if (props.data.services && props.data.services.selected) {
			props.data.services.selected.forEach(el => {
				if (el.id != serviceId.value) {
					data.push(el.id);
				}
			});
		}

		// update product (delete current product and add a new one)
		const quantity = props.data.quantity;
		const selectedOffer = props.data.services.special[0].selected_offer_id;
		const replacedOffer = props.data.services.special[0].replaced_offer_id;

		await webshopStore.removeProduct({
			preventRefresh: true,
			shopping_cart_code: selectedOffer,
		});
		await webshopStore.addProduct({
			shopping_cart_code: replacedOffer,
			quantity: quantity,
			services: data,
		});

		data = [];
	};
</script>

<style lang="less" scoped>
	.wp-extra-benefit-price.special span {
		cursor: pointer;
		@media (min-width: @t) {
			&:hover {
				color: @blue;
			}
		}
	}
</style>
