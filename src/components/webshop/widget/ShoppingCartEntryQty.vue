<template>
	<div class="wp-qty">
		<span class="wp-btn-qty wp-btn-dec" @click="updateQty('-', item.shopping_cart_code)"></span>
		<input class="wp-input-qty product_qty_input" type="text" name="qty" @change="updateQty('*', item.shopping_cart_code)" v-model="productQty" />
		<span class="wp-btn-qty wp-btn-inc" @click="updateQty('+', item.shopping_cart_code)"></span>
		<span :class="['wp-message', qtyStatus]" v-if="qtyStatus">{{ store.labels[qtyStatus] }}</span>
	</div>
</template>

<script setup>
	import { ref, watch } from 'vue'
	import { useStore } from '@/stores'
	import { useWebshopStore } from '@/stores/webshop'
	const props = defineProps(['item'])
	const store = useStore()
	const webshopStore = useWebshopStore()
	const productQty = ref(props.item.quantity)
	const qtyStatus = ref()
	watch(
		() => props.item.quantity,
		() => productQty.value = props.item.quantity,
		{deep: true}
	)
	const updateQty = async (action, code) => {
		store.loading = 1
		let qty = productQty.value
		if(action == '+') qty++
		if(action == '*') qty = productQty.value
		if(action == '-') qty--
		if(qty <= 1) qty = 1
		productQty.value = qty
		await webshopStore.updateProduct({shopping_cart_code: code, quantity: qty}).then(res => {	
			const label = res.data.labels_name
			qtyStatus.value = label[Object.keys(label)[0]]
		})
		setTimeout(() => qtyStatus.value = '', 4000)
	}
</script>