<template>
	<div class="ww cart" :class="{'active': cartItems}">
		<router-link class="ww-items btn btn-green header-btn" to="/webshop/store/">
			<span class="ww-counter">
				<span class="value total-items ww-counter header-btn-count">{{ cartItems }}</span>
			</span>
		</router-link>
	</div>
</template>

<script setup>
	import {computed} from 'vue';
	import {useWebshopStore} from '@/stores/webshop';

	const webshopStore = useWebshopStore();
	const cartItems = computed(() => {
		if (webshopStore.carts == null) {
			return 0;
		} else {
			return webshopStore.carts.length;
		}
	});
</script>
