<template>
	<div class="flyout-autocomplete">
		<input class="autocomplete-input" autocomplete="off" @blur="blur" @keyup="isTyping($event)" v-model="searchTerm" type="text" :placeholder="store.labels.pa_enter_search_service" />
		<div v-if="searchTerm" class="autocomplete-clear-btn" @click="clearInput()"></div>

		<div v-if="mobileBp && autocompleteActive && searchTerm" class="ac-background"></div>
		<div class="ac" v-if="searchTerm && autocompleteItems.length">
			<ul class="ac-items" v-if="autocompleteItems">
				<li class="ac-item" v-for="(item, index) in autocompleteItems" :key="item.id" @click="setSelectedItem(index), navigate()" :class="{active: dirty && selectedIndex == index}" :data-index="index">
					<routerLink :to="item.url_without_domain">
						<div class="ac-cnt">
							<div class="ac-col1">
								<div class="ac-title">{{ item.title }}</div>
								<div class="ac-id">
									<strong>{{ store.labels.id }}: </strong>{{ item.code }}
								</div>
							</div>
							<div class="ac-col2">
							<div class="ac-price">
								<div class="ac-current-price">{{ formatCurrency(item.price_custom) }}</div>
								<div class="ac-btn-addtocart btn" @click.stop.prevent="addService(item.shopping_cart_code)">{{ store.labels.pa_coupon_select }}</div>
							</div>
							</div>
						</div>
					</routerLink>
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup>
	import {ref} from 'vue';
	import {useRouter} from 'vue-router';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {breakpoints} from '@/composables/useBreakpoints';
	import useHelpers from '@/composables/useHelpers';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const router = useRouter();
	const {formatCurrency} = useHelpers();
	const props = defineProps(['data']);
	const searchTerm = ref();
	const autocompleteItems = ref([]);
	const selectedItem = ref({});
	const selectedIndex = ref(-1);
	const dirty = ref(0);
	const autocompleteActive = ref(0);
	const mobileBp = breakpoints('m');
	const emit = defineEmits(['closeBox', 'serviceMessageUpdated']);

	async function isTyping(event) {
		if (event.key == 'ArrowDown') {
			dirty.value = 1;
			const totalItems = autocompleteItems.value.length - 1;
			selectedIndex.value = selectedIndex.value >= totalItems ? totalItems : selectedIndex.value + 1;
			setSelectedItem();
			return true;
		}

		if (event.key == 'ArrowUp') {
			dirty.value = 1;
			selectedIndex.value = selectedIndex.value <= 0 ? 0 : selectedIndex.value - 1;
			setSelectedItem();
			return true;
		}

		if (event.key == 'Enter') {
			if (dirty.value) {
				navigate();
			}
			return true;
		}

		if (event.key == 'Escape') {
			blur();
			return true;
		}

		search();
	}

	function navigate() {
		autocompleteItems.value = [];
		dirty.value = 0;
		return router.push(selectedItem.value);
	}

	function setSelectedItem(index = selectedIndex.value) {
		selectedItem.value = autocompleteItems.value[index].url_without_domain;
		searchTerm.value = autocompleteItems.value[index].title;
	}

	function reset() {
		dirty.value = 0;
		autocompleteItems.value = [];
		selectedItem.value = {};
		selectedIndex.value = -1;
		autocompleteActive.value = 0;
	}

	async function search() {
		let itemsValue = [];

		if (searchTerm.value && searchTerm.value.length > 2) {
			let input = searchTerm.value;

			props.data.forEach(el => {
				let title = el.title;
				let code = el.code;
				if (title.toLowerCase().includes(input.toLowerCase()) || code.toLowerCase().includes(input.toLowerCase())) {
					autocompleteActive.value = 1;
					itemsValue.push(el);
					selectedIndex.value = -1;
				}
			});
			autocompleteItems.value = itemsValue;
		} else {
			autocompleteItems.value = [];
			itemsValue = [];
			autocompleteActive.value = 0;
		}
	}

	function blur() {
		setTimeout(() => {
			reset();
		}, 100);
	}

	function clearInput() {
		setTimeout(() => {
			reset();
		}, 100);
		searchTerm.value = '';
	}

	//add service
	async function addService(value) {
		store.loading = 1;
		const cartCode = value;

		await webshopStore
			.addProduct({
				shopping_cart_code: cartCode,
			})
			.then(res => {
				if (res.success) {
					emit('closeBox');
				} else {
					emit('serviceMessageUpdated', res.data.label_name);
				}
				store.loading = 0;
			});
	}
</script>

<style lang="less" scoped>
	.flyout-autocomplete {
		flex-grow: 1;
		position: relative;
		&:before {
			.icon-search;
			font: 18px/1 @fonti;
			color: @blue;
			position: absolute;
			left: 20px;
			top: 16px;
			z-index: 1;
		}
	}
	.autocomplete-input {
		padding: 0 50px;
		box-shadow: 0 10px 20px 0 rgba(0, 34, 67, 0.03);
		.placeholder(@textColor, @borderColor);

		@media (max-width: @m) {
			width: calc(~'100% - 50px');
			height: 50px;
			box-shadow: none;
			border: none;
			border-right: 1px solid @borderColor;
		}
	}
	.autocomplete-clear-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
		background: @white;
		position: absolute;
		right: 5px;
		top: 5px;
		cursor: pointer;
		z-index: 1;
		&:before,
		&:after {
			.pseudo(14px,2px);
			background: @textColor;
			.rotate(-45deg);
		}
		&:after {
			.rotate(45deg);
		}

		@media (max-width: @m) {
			right: 55px;
		}
	}

	.ac-background {
		display: none;
		@media (max-width: @m) {
			display: block;
			position: fixed;
			top: 50px;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1;
			background-color: rgba(0, 24, 47, 0.6);
		}
	}
	.ac {
		position: absolute;
		top: 100%;
		left: 1px;
		right: 1px;
		background: #fff;
		z-index: 100;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
		font-size: 14px;

		@media (max-width: @m) {
			left: 0;
			right: 0;
		}
	}
	.ac-items {
		overflow: auto;
		max-height: 350px;
	}
	.ac-item {
		border-bottom: 1px solid @borderColor;
		&:hover,
		&.active {
			background: #f8f9fa;
			.ac-title {
				color: @blue;
			}
		}
	}
	.ac-cnt {
		display: flex;
		align-items: flex-start;
		width: 100%;
		padding: 15px 20px;
		border-bottom: 1px solid @borderColor;

		&:last-child {
			border: none;
		}
	}
	.ac-col1 {
		flex-grow: 1;
		padding-right: 20px;
	}
	.ac-title {
		font-size: 12px;
		font-weight: bold;
		letter-spacing: -0.2px;
	}
	.ac-id {
		font-size: 12px;
		margin-top: 2px;
	}
	.ac-col2{flex-shrink: 0;}
	.ac-price{font-size: 12px; font-weight: bold; text-align: right;}
	.ac-btn-addtocart{height: 30px; min-height: 30px; margin-top: 5px; padding: 0 11px; font-size: 12px; letter-spacing: -0.24px;}
</style>
