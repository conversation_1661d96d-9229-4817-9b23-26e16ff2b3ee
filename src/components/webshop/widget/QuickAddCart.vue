<template>
	<a class="btn-float btn-float-scan-card" @click="(modalStatus = 1), (camLoading = 1)">Nakupovalna kartica</a>

	<Modal @close="closeModal" :openValue="modalStatus" @scan="onScan" :title="store.labels.pa_enter_physical_cart_code" mode="scanner">
		<div class="wqr-scan-content">
			<div class="msg" v-if="msg">{{ store.labels[msg] }}</div>
			<input type="text" name="qrcode" v-model="code" :placeholder="store.labels.qr_code_card" required />
			<input class="btn wqr-scan-send" @click="submit" :disabled="!code" type="submit" :value="store.labels.coupon_btn_add" />
		</div>
	</Modal>
</template>

<script setup>
	import {ref} from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import Modal from '@/components/Modal.vue';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const modalStatus = ref();
	const code = ref();
	const msg = ref();

	function closeModal() {
		modalStatus.value = 0;
		code.value = '';
	}

	function onScan(payload) {
		code.value = payload;
	}

	async function submit() {
		await webshopStore.restoreCart(code.value).then(res => {
			if (res.success) {
				closeModal();
				msg.value = '';
			} else if (res.data.label_name == 'error_cart_with_given_cart_code_already_exists') {
				webshopStore.updateCart = [res.data, code.value];
				closeModal();
			} else {
				msg.value = res.data.label_name;
				setTimeout(function () {
					msg.value = '';
				}, 4000);
			}
		});
	}
</script>

<style lang="less" scoped>
	.btn {
		width: 100%;
		min-height: 0;
	}
	.msg {
		position: absolute;
		top: 7px;
		font-weight: bold;
		font-size: 12px;
		line-height: 1.2;
		color: @red;
	}
</style>
