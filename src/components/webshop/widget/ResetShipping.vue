<template>
	<div v-if="webshopStore.cart?.cart?.indicators?.reset_delivery_enabled" class="btn-float btn-float-reset-shipping" @click="webshopStore.resetShipping()">
		<span>{{ store.labels.pa_cart_reset }}</span>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';

	const store = useStore();
	const webshopStore = useWebshopStore();
</script>
