<template>
	<div v-if="typeof services !== 'undefined' && services.length > 0" :class="{'cd-extra-benefits cd-col2-box': mode == 'itemDetail', 'wp-extra-benefits': mode != 'itemDetail'}">
		<template v-if="mode == 'itemDetail'">
			<div class="cd-extra-benefits-header">
				<span class="cd-subtitle cd-extra-benefits-title">{{ store.labels.pa_services }}</span>
			</div>
		</template>
		<div v-for="(service, index) in services" :key="index" class="cd-extra-benefit-item" :class="{'active': service.desc_active, 'wp-extra-benefit-item': mode != 'itemDetail'}">
			<div class="cd-extra-benefit-row">
				<input v-if="service.category_type == 's'" @change="updateService" v-model="currentlySelectedInsurance" type="radio" :name="'service-' + item.shopping_cart_code + '[]'" :value="service.id" :id="'service-' + item.shopping_cart_code + '-' + service.id" />
				<input v-else @change="updateService" v-model="currentlySelected" type="checkbox" :name="'service-' + item.shopping_cart_code + '[]'" :value="service.id" :id="'service-' + item.shopping_cart_code + '-' + service.id" />
				<label class="cd-extra-benefit" :for="'service-' + item.shopping_cart_code + '-' + service.id">
					<div class="cd-extra-benefit-title">
						<span>{{ service.title }}</span>
					</div>
				</label>
				<div class="cd-extra-benefit-price" :class="{'wp-extra-benefit-price': mode != 'itemDetail'}">
					+ <span>{{ formatCurrency(service.price) }}</span>
				</div>
				<span class="cd-extra-benefit-icon" v-if="service.description" @click="service.desc_active = !service.desc_active"></span>
			</div>
			<div class="cd-extra-benefit-desc" v-if="service.description" v-html="service.description"></div>
			<div v-if="index === servicesRadioQty - 1 && insuranceItems" class="cd-extra-benefit-item" :class="{'wp-extra-benefit-item': mode != 'itemDetail'}">
				<div class="cd-extra-benefit-row">
					<input :checked="currentlySelectedInsurance == null" @change="updateService" v-model="currentlySelectedInsurance" type="radio" value="" :name="'service-' + item.shopping_cart_code + '[]'" :id="'service-' + item.shopping_cart_code + '-' + '0'" />
					<label class="cd-extra-benefit" :for="'service-' + item.shopping_cart_code + '-' + '0'">
						<div class="cd-extra-benefit-title">{{ store.labels.pa_no_warranty }}</div>
					</label>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useCatalogStore} from '@/stores/catalog';
	import {useWebshopStore} from '@/stores/webshop';
	import {ref, computed, watch} from 'vue';
	import useHelpers from '@/composables/useHelpers';

	const store = useStore();
	const catalogStore = useCatalogStore();
	const webshopStore = useWebshopStore();
	const {formatCurrency} = useHelpers();

	const props = defineProps(['item', 'mode']);
	const serviceItems = computed(() => {
		let serviceItemsValue = null;
		if (props.mode == 'itemDetail') {
			serviceItemsValue = props.item.service_simple;
		} else {
			serviceItemsValue = props.item.services;
		}
		return serviceItemsValue;
	});
	const insuranceItems = computed(() => {
		let insuranceItemsValue = null;
		if (props.mode == 'itemDetail') {
			insuranceItemsValue = props.item.insurance_simple;
		} else {
			insuranceItemsValue = props.item.insurances;
		}
		return insuranceItemsValue;
	});

	const services = computed(() => {
		let insurances = insuranceItems.value ? insuranceItems.value.available : [];
		let services = [];
		if (serviceItems.value && serviceItems.value.available) {
			serviceItems.value.available.forEach(el => {
				if (!el.options?.selected_value) {
					services.push(el);
				}
			});
		}

		const servicesMerge = services ? insurances.concat(services) : insurances;
		servicesRadioQty.value = servicesMerge.filter(item => item.category_type === 's').length;
		return servicesMerge;
	});

	const currentlySelectedInsurance = ref(null);
	const currentlySelected = ref([]);
	let servicesRadioQty = ref();

	watch(
		() => props.item,
		() => {
			setTimeout(() => {
				setServices();
			}, 100);
		},
		{deep: true}
	);

	function setServices() {
		// initialy selected insurance
		currentlySelectedInsurance.value = insuranceItems.value && insuranceItems.value.selected ? insuranceItems.value.selected[0].id : null;

		// initialy selected services
		if (serviceItems.value && serviceItems.value.selected) {
			let selectedServices = [];
			serviceItems.value.selected.forEach(el => {
				selectedServices.push(el.id);
			});
			currentlySelected.value = selectedServices;
		}
	}

	setServices();

	function updateService() {
		let data = [];
		if (currentlySelectedInsurance.value != null) data.push(currentlySelectedInsurance.value);

		currentlySelected.value.forEach(el => data.push(el));

		if (props.mode == 'itemDetail') {
			catalogStore.servicesSelected = data;
		} else {
			webshopStore.updateProduct({
				shopping_cart_code: props.item.shopping_cart_code,
				quantity: props.item.quantity,
				services: data.length ? data : [''],
			});
		}
	}
</script>

<style lang="less">
	.cd-extra-benefits .cd-extra-benefit-item {
		border-bottom: none;
		&:nth-child(2) {
			border-top: none;
		}
	}

	.wp-extra-benefits {
		margin: 0 150px 0 55px;
		.wp-extra-benefit-item {
			border-bottom: none;
			&:first-child {
				border-top: none;
			}
		}

		@media (max-width: @t) {
			margin: 0 0 10px;
		}
		@media (max-width: @m) {
			margin: 0 0 5px;
			.wp-extra-benefit-item:first-child .cd-extra-benefit-row {
				min-height: auto;
			}
		}
	}
</style>
