<template>
	<a class="w-cart-remove" :class="{'active': cart.cart_active}" @click="openValue = 1">{{ store.labels.pa_cart_remove }}</a>
	<Modal @close="openValue = 0" :openValue="openValue">
		<div class="w-cart-remove-tooltip">
			<PERSON><PERSON>, da ž<PERSON> izbrisati <span>“{{ cart.cart_title }}”</span>?
			<div class="w-cart-remove-tooltip-btns">
				<a href="javascript:void(0);" @click="openValue = 0" class="btn btn-modal btn-cancel">{{ store.labels.pa_cancel }}</a>
				<a href="javascript:void(0);" @click="removeCart" class="btn btn-modal btn-remove">{{ store.labels.pa_remove_cart }}</a>
			</div>
		</div>
	</Modal>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import Modal from '@/components/Modal.vue';
	import {ref} from 'vue';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const props = defineProps(['cart']);
	const openValue = ref(false);

	async function removeCart() {
		openValue.value = 0;
		await webshopStore.removeCart({
			token_id: props.cart.token_id,
			token_hash: props.cart.token_hash,
		});
	}
</script>
