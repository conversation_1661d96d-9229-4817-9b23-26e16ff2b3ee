<template>
	<div v-if="webshopStore?.cart?.cart?.cart_order_type" class="w-cart-type-container" ref="typeContainer">
		<div v-if="webshopStore?.cart?.cart?.cart_order_type?.selected" class="w-cart-type" :class="{'active': typeActive}" @click="typeToggle()">
			<span>{{ store.labels['order_type_' + webshopStore?.cart?.cart?.cart_order_type?.selected] }}</span>
		</div>
		<div class="w-cart-type-modal" :class="{'active': typeActive}">
			<span v-for="(item, index) in webshopStore?.cart?.cart?.cart_order_type?.available" :key="index" class="field radio-field">
				<input type="radio" :disabled="selected == item" v-model="selected" :value="item" name="cart-type" :id="item" />
				<label :for="item.code" @click="newCartType(item)">{{ store.labels['order_type_' + item] }}</label>
			</span>
		</div>
	</div>
</template>

<script setup>
	import {watch, ref} from 'vue';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useFetch} from '@/composables/useFetch';
	import {onClickOutside} from '@vueuse/core';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';

	const ep = useEndpoints();
	const store = useStore();
	const webshopStore = useWebshopStore();
	
	//toggle
	const typeActive = ref(false);
	function typeToggle() {
		typeActive.value = !typeActive.value;
	}

	//radio button
	const selected = ref(webshopStore?.cart?.cart?.cart_order_type?.selected || null);

	watch(() => webshopStore?.cart?.cart?.cart_order_type?.selected, (newValue) => {
		selected.value = newValue || null;
	});

	//select new cart type
	async function newCartType(value) {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_cart_order_type,
			method: 'POST',
			body: {'cart_order_type': value},
		}).then(async res => {
			await webshopStore.fetchCartItems();
			typeActive.value = false;
			store.loading = 0;
		});
	}

	// close modal on outside click and escape keyup
	const typeContainer = ref(null);
	onClickOutside(typeContainer, () => typeActive.value = false);
</script>

<style lang="less" scoped>
	.w-cart-type-container{
		display: flex; align-items: center; height: 72px; position: absolute; right: 67px; top: 0; z-index: 111;

		@media (max-width: @m){height: 48px; padding: 0 10px; background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%); border-top: 1px solid rgba(255,255,255,0.2); position: relative; right: unset; top: unset;}
	}
	.w-cart-type {
		display: flex; align-items: center; justify-content: center; width: auto; height: 29px; padding: 5px 15px; background: @white; border-radius: 15px; font-size: 12px; font-weight: bold; color: @textColor; cursor: pointer;
		span{
			padding-right: 15px; position: relative;
			&:before{.icon-arrow-down; font: 8px/1 @fonti; color: @blue; position: absolute; top: 5px; right: 0;}
		}

		&.active span:before{top: 4px; .rotate(180deg);}
	}

	.w-cart-type-modal{
		display: none; min-width: 200px; background: @white; border-radius: 2px; position: absolute; top: calc(~"100% - 10px"); right: 0; box-shadow: 0 0 35px 0 rgba(3,32,62,0.15); z-index: 111;
		&.active{display: block;}

		@media (max-width: @m){width: 100%; border-radius: 0; top: 100%;}
	}
	.radio-field{
		display: flex; align-items: center; border-bottom: 1px solid @gray;
		&:last-child{border: none;}
		input[type=radio]+label{
			display: flex; align-items: center; flex-grow: 1; min-height: 40px; padding: 0 15px 0 45px; white-space: nowrap;
			&:hover{background: #f8f9fa;}
		}
		input[type=radio]+label:before{top: unset; left: 15px;}
		input[type='radio']:checked + label{font-weight: bold;}
		input:disabled+label{color: @textColor; pointer-events: none;}
	}
</style>
