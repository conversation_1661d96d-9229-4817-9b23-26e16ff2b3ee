<template>
	<div class="reservation-form">
		<div class="field">
			<input type="checkbox" id="reservation_option_b2b_channel" name="reservation-option-b2b_channel" value="reservation_option_b2b_channel" v-model="b2bChannel" />
			<label @click="b2bChannel = !b2bChannel" for="reservation-option-b2b_channel">{{ store.labels.pa_reservation_option_b2b_channel }}</label>
		</div>

		<div v-if="reservationCartData?.payment_due_days?.original" class="field-reservation-row">
			<div class="subtitle">{{ store.labels.pa_reservation_order_due_days }}</div>
			<div class="field">
				<input name="reservation_due_value" type="number" @input="setMaxValue()" v-model="dueInput" :placeholder="store.labels.pa_reservation_order_due_days" />
			</div>
		</div>

		<div v-if="reservationCartData.indicators?.reservation?.min_expiration_date && reservationCartData?.cart_order_type?.selected != 'order'" class="field-reservation-row">
			<div class="subtitle">{{ store.labels.pa_reservation_option_date_title }}</div>
			<div class="field">
				<Datepicker
					v-model="selectedDate"
					:clearable="false"
					:enable-time-picker="false"
					@update:modelValue="updateSelectedDate"
					auto-apply
					:start-date="reservationCartData.indicators?.reservation?.selected_expiration_date"
					:min-date="reservationCartData.indicators?.reservation?.min_expiration_date"
					:max-date="reservationCartData.indicators?.reservation?.max_expiration_date"
					:format="formatDate"
					:label="'test'"
					:day-names="['Po', 'To', 'Sr', 'Če', 'Pe', 'So', 'Ne']" />
			</div>
		</div>

		<div v-if="reservationCustomerData?.additional_recipient_email_enabled" class="field">
			<input type="checkbox" id="reservation_option_email" name="reservation-option-email" value="reservation_option_email" v-model="selectedEmail" />
			<label @click="selectedEmail = !selectedEmail" for="reservation-option-email">{{ store.labels.pa_reservation_option_email }}</label>

			<div v-if="selectedEmail" class="subfield-row">
				<input @focus="handleFocus" name="reservation_email_value" type="email" v-model="emailInput" :placeholder="store.labels.email" />
			</div>
		</div>

		<div v-if="reservationCustomerData?.display_recipient_on_visualization_enabled" class="field">
			<input type="checkbox" id="reservation_option_email" name="reservation-option-email" value="reservation_option_visualization" v-model="selectedVisualization" />
			<label @click="selectedVisualization = !selectedVisualization;" for="reservation-option-email">{{ store.labels.pa_reservation_option_recipient_visualization }}</label>
		</div>

		<div v-if="reservationCustomerData?.customer_order_form_id_enabled" class="field-reservation-row">
			<div class="subtitle">{{ store.labels.pa_reservation_option_form_id_title }}</div>
			<div class="field">
				<input name="reservation_code_value" maxlength="50" type="text" v-model="codeInput" :placeholder="store.labels.pa_reservation_option_form_id" />
			</div>

			<div class="subtitle">{{ store.labels.pa_order_form_date }}:</div>
			<div class="field">
				<Datepicker
					v-model="codeDateInput"
					@update:modelValue="updateSelectedCodeDate"
					:clearable="false"
					:enable-time-picker="false"
					auto-apply
					:format="formatDate"
					:placeholder="store.labels.pa_select_date"
					:day-names="['Po', 'To', 'Sr', 'Če', 'Pe', 'So', 'Ne']" />
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, watch, watchEffect, onMounted } from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import Datepicker from '@vuepic/vue-datepicker';
	import '@vuepic/vue-datepicker/dist/main.css';
	const emits = defineEmits(['orderFormChanged']);

	const store = useStore();
	const webshopStore = useWebshopStore();
	const reservationCartData = ref(webshopStore?.cart?.cart);
	const reservationCustomerData = ref(webshopStore?.cart?.customer);

	//reservation b2b channel
	const b2bChannel = ref(reservationCustomerData?.value?.b2b_channel ? true : false);

	//reservation date
	const selectedDate = ref(webshopStore?.cart?.cart?.indicators?.reservation?.selected_expiration_date || webshopStore?.cart?.cart?.indicators?.reservation?.min_expiration_date);

	const formatDate = date => {
		const days = ['Ned', 'Pon', 'Tor', 'Sre', 'Čet', 'Pet', 'Sob'];
		
		const dayName = days[date.getDay()]; // Get the day name from the array
		const day = date.getDate();
		const month = date.getMonth() + 1;
		const year = date.getFullYear();

		return `${dayName}, ${day}.${month}.${year}`;
	};

	function updateSelectedDate(modelData) {
		const dateObject = new Date(modelData); // Parse the incoming date string to a Date object
		const year = dateObject.getFullYear();
		const month = (dateObject.getMonth() + 1).toString().padStart(2, '0'); // Add padding if necessary
		const day = dateObject.getDate().toString().padStart(2, '0'); // Add padding if necessary

		const currentDate = new Date(); // Get the current date and time
		const hours = currentDate.getHours().toString().padStart(2, '0'); // Extract hours and pad if necessary
		const minutes = currentDate.getMinutes().toString().padStart(2, '0'); // Extract minutes and pad if necessary
		const seconds = currentDate.getSeconds().toString().padStart(2, '0'); // Extract seconds and pad if necessary
		selectedDate.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	}

	//reservation email
	const selectedEmail = ref(reservationCustomerData?.value?.additional_recipient_email ? true : false);
	const emailInput = ref(reservationCustomerData?.value?.additional_recipient_email ? reservationCustomerData?.value?.additional_recipient_email : null);

	//reservation recipient visualization
	const selectedVisualization = ref(reservationCustomerData?.value?.display_recipient_on_visualization ? true : false);

	//reservation code
	const codeInput = ref(reservationCustomerData?.value?.customer_order_form_id ? reservationCustomerData?.value?.customer_order_form_id : null);
	
	// reservation code date
	const codeDateInput = ref(reservationCustomerData?.value?.customer_order_form_date ? reservationCustomerData?.value?.customer_order_form_date : null);
	function updateSelectedCodeDate(modelData) {
		const dateObject = new Date(modelData); // Parse the incoming date string to a Date object
		const year = dateObject.getFullYear();
		const month = (dateObject.getMonth() + 1).toString().padStart(2, '0'); // Add padding if necessary
		const day = dateObject.getDate().toString().padStart(2, '0'); // Add padding if necessary
		codeDateInput.value = `${year}-${month}-${day}`;
	}

	//reservation due date
	const dueInput = ref(null);
	function setMaxValue() {
		if(dueInput.value > reservationCartData?.value?.payment_due_days?.original || dueInput.value < 0) {
			dueInput.value = reservationCartData?.value?.payment_due_days?.original
		}
	}

	function setFormData() {
		return {
			b2b_channel: b2bChannel.value,
			specific_payment_due_date_days: dueInput.value,
			reservation_date: selectedDate.value,
			additional_recipient_email: emailInput.value,
			display_recipient_on_visualization: selectedVisualization.value,
			customer_order_form_id: codeInput.value,
			customer_order_form_date: codeDateInput.value
		}
	}
	
	//emit values
	watchEffect(() => {
		emits('orderFormChanged', setFormData());
	})

	onMounted(() => {
		if(reservationCartData?.value?.payment_due_days?.specific != null) {
			dueInput.value = reservationCartData?.value?.payment_due_days?.specific;
		}
		emits('orderFormChanged', setFormData());
	})
</script>

<style lang="less" scoped>
	.subtitle{display: block; margin-bottom: 6px; font-size: 14px; font-weight: bold;}
	.field-reservation-row{margin: 25px 0;}
	.field{margin-bottom: 20px;}
	.subfield-row{margin-top: 15px;}
	input{.placeholder(rgba(4,45,86,0.5), @borderColor);}
</style>

<style lang="less">
	.dp__input{border-radius: 2px; border-color: #DBE3EA; font-size: 14px;}
</style>