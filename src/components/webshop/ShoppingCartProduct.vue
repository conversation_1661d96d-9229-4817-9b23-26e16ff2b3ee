<template>
	<div class="wp">
		<div class="wp-row1">
			<div class="wp-order-numb"></div>
			<div class="wp-check-button wp-col1">
				<input @change="selectItem" v-model="currentlySelectedItem" type="checkbox" :id="'item_' + item.shopping_cart_code" :name="'item_' + item.shopping_cart_code" :value="'item-' + item.id" />
				<label :for="'item_' + item.shopping_cart_code"></label>
			</div>
			<div class="wp-warehouse-qty wp-col2">
				<div class="wp-warehouse-qty-box" :class="[availableQtyClass]"></div>
			</div>
			<div class="wp-cnt wp-col3">
				<h2 class="wp-title">
					<router-link :to="useUrl(item.item.url)">
						<template v-if="item.title_custom">{{ item.title_custom }}</template>
						<template v-else>{{ item.item.title }}</template>
					</router-link>
					<span v-if="item.can_change_title" class="wp-title-edit-container">
						<span @click="editItemTitle(item.shopping_cart_code)" class="wp-title-edit-btn wp-title-edit">edit</span>
						<span v-if="item.title_custom" @click="removeItemTitle(item.shopping_cart_code)" class="wp-title-edit-btn wp-title-remove">remove</span>
					</span>
					<span v-if="item.date_available" class="preorder-date"> ({{ formatDate(item.date_available, {short: true}) }})</span>
				</h2>
				<div class="wp-codes">
					<div class="wp-code">
						<strong>{{ store.labels.id }}: </strong>{{ item.item.code }}
					</div>

					<div v-if="item.condition && item.condition != 'n'"  class="wp-condition">
						<span v-html="store.labels['condition_' + item.condition]"></span>
					</div>

					<!--
					<div v-if="item.item.offer.seller.title" class="wp-seller">
						<span>{{ item.item.offer.seller.title }}</span>
					</div>
					-->

					<div class="wp-ean" v-if="item.ean_code">
						<span>{{ item.ean_code }}</span>
					</div>

					<template v-if="item.item.badges">
						<template v-for="badgeSpecial in item.item.badges" :key="badgeSpecial.code">
							<div v-if="badgeSpecial.category == 2 && badgeSpecial.label_title_hover" @mouseover="couponBadgeTooltip = true" @mouseleave="couponBadgeTooltip = false" @click="copyCoupon(badgeSpecial.label_title_hover)" class="cp-badge-coupon wp-badge-coupon">
								<span v-if="badgeSpecial.label_title" class="cp-badge-coupon-title"
									><span>{{ badgeSpecial.label_title }}</span></span
								>
								<div v-if="badgeSpecial.label_title_hover" class="cp-badge-coupon-info">
									<span class="title">{{ badgeSpecial.label_title_hover }}</span>
									<span class="icon">
										<span v-if="couponBadgeTooltip" class="cp-badge-coupon-tooltip">{{ couponBadgeTooltipText }}</span>
									</span>
								</div>
							</div>
						</template>
					</template>
				</div>
				<div class="w-cart-checkbox-section">
					<div class="w-cart-checkbox-item avans" v-if="webshopStore?.cart?.cart?.cart_order_type?.selected === 'order' && item?.user_force_avans != false && item.avans_enabled">
						<div class="w-cart-checkbox-avans">
							<input :checked="item.avans" type="checkbox" :id="'checkboxAvans-' + item.shopping_cart_code" name="checkbox-avans" @click="setOption('avans', $event)" />
							<label :for="'checkboxAvans-' + item.shopping_cart_code">{{ store.labels.avans_item }}</label>
						</div>
					</div>

					<div class="w-cart-checkbox-item" v-if="item.warehouse_pickup_enabled">
						<input :checked="item.warehouse_pickup" type="checkbox" :id="'checkboxWarehousePickup-' + item.shopping_cart_code" name="checkbox-warehouse" @click="webshopStore.setShippingOption({product: item.shopping_cart_code, status: $event.target.checked})" />
						<label :for="'checkboxWarehousePickup-' + item.shopping_cart_code">{{ store.labels.pa_shipping_warehouse }}</label>
					</div>

					<div class="w-cart-checkbox-item" v-if="webshopStore?.cart?.cart?.cart_order_type?.selected === 'order' && item?.user_force_avans != false && item.force_avans && !item.avans_enabled">
						<input checked disabled type="checkbox" id="avansItem" name="avans-item" />
						<label for="avansItem">{{ store.labels.pa_avans_item }}</label>
					</div>

					<div class="w-cart-checkbox-item" v-if="item?.user_force_avans == false">
						<input checked disabled type="checkbox" id="avansForceItem" name="avans-item" />
						<label for="avansForceItem">{{ store.labels.pa_avans_force_item }}</label>
					</div>
				</div>
				<template v-if="item.errors">
					<div class="wp-errors">
						<div class="wp-error" v-for="error in item.errors" :key="error">
							{{ store.labels[error.label_name] ? store.labels[error.label_name] : error.label_name }}
							<template v-if="error.label_name == 'error_available_qty' || error.label_name == 'error_min_order_qty' || error.label_name == 'error_max_order_qty'">{{ error.qty }}</template>
						</div>
					</div>
				</template>
				<template v-if="item.warnings">
					<div class="wp-errors">
						<div class="wp-error" :class="{'warning': warning.label_name != 'warning_shipping_method_invalid' && warning.label_name != 'warning_user_force_complete_prepayment_pa_invalid'}" v-for="warning in item.warnings" :key="warning">
							{{ store.labels[warning.label_name] ? store.labels[warning.label_name] : warning.label_name }}
							<template v-if="warning.label_name == 'warning_available_qty' || warning.label_name == 'warning_min_order_qty' || warning.label_name == 'warning_max_order_qty'">{{ warning.qty }}</template>
						</div>
					</div>
				</template>
			</div>
			<div class="wp-col4">
				<SerialNumber v-if="item && item.serial_number_mandatory" :item="item" />
				<ImeiNumber v-if="item && item.imei_mandatory" :item="item" />
				<ShoppingCartEntryQty :item="item" />
			</div>

			<div class="wp-total wp-col5">
				<div class="wp-price-container">
					<div class="wp-price-old line-through" v-if="item.discount_amount">{{ formatCurrency(item.total_basic) }}</div>
					<div v-if="item.salesman_price_definition" class="wp-price-current underline" @click="openSalesmanPrice({mode: 'salesmanPriceDefinition'})">{{ formatCurrency(item.total) }}</div>
					<div v-else class="wp-price-current" :class="{'red': item.salesman_discount_percent, 'underline': store.info && (store.info.salesman_price_enable == '1' && item.salesman_discount_price_enabled)}" @click="store.info && (store.info.salesman_price_enable == '1' && item.salesman_discount_price_enabled) ?  openSalesmanPrice() : null">{{ formatCurrency(item.total) }}</div>
					<div v-if="item.salesman_discount_percent" class="wp-price-salesman" @click="store.info && store.info.salesman_price_enable == '1' ? openSalesmanPrice() : null"><span>-{{ item.salesman_discount_percent }}%</span></div>
					<div class="wp-qty-count wp-price-old" v-if="item.quantity > 1">
						<span class="product_qty product_qty_special">{{ item.quantity }}</span> x <span>{{ formatCurrency(item.unit_price) }}</span>
					</div>
				</div>
			</div>
		</div>
		<div class="wp-row2">
			<ShoppingCartEntryServices :item="item" />
			<SpecialService :data="item" />
		</div>
	</div>
</template>

<script setup>
	import ShoppingCartEntryServices from '@/components/webshop/widget/ShoppingCartEntryServices.vue';
	import SpecialService from '@/components/webshop/widget/SpecialService.vue';
	import ShoppingCartEntryQty from '@/components/webshop/widget/ShoppingCartEntryQty.vue';
	import SerialNumber from '@/components/webshop/widget/SerialNumber.vue';
	import ImeiNumber from '@/components/webshop/widget/ImeiNumber.vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {ref, watch, onMounted, computed} from 'vue';
	import {useUrl} from '@/composables/useUrl';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useFetch} from '@/composables/useFetch';
	import useHelpers from '@/composables/useHelpers';
	import useFlyout from '@/composables/useFlyout';

	const ep = useEndpoints();
	const store = useStore();
	const webshopStore = useWebshopStore();
	const {formatCurrency, formatDate} = useHelpers();
	const {openFlyout, closeFlyout} = useFlyout();
	const props = defineProps(['item']);

	console.log(props.item);

	//selected item
	let currentlySelectedItem = ref(false);
	
	watch(
		() => webshopStore.itemSelected.some(obj => obj.id === props.item.item.id),
		(newValue) => {
			currentlySelectedItem.value = newValue;
		}
	);

	watch(
		() => webshopStore.unchecked,
		() => (currentlySelectedItem.value = webshopStore.unchecked == 1 ? false : ''),
		{deep: true}
	);

	//title edit
	const editTitle = ref(false);

	async function editItemTitle(code) {
		openFlyout({
			mode: 'itemTitleEdit',
			shopping_cart_code: code,
		});
	}

	async function removeItemTitle(code) {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_customer,
			method: 'POST',
			body: {
				"title_item_custom": {
					"code": code,
					"title": null
				}
			}
		}).then(res => webshopStore.fetchCarts());
	}

	//open salesman price
	async function openSalesmanPrice(options) {
		const data = {
			shopping_cart_code: props.item.shopping_cart_code,
			id: props.item.id,
			title: props.item.item.title,
			title_custom: props.item.title_custom,
			url: props.item.item.url,
			code: props.item.item.code,
			quantity: props.item.quantity,
			discount_amount: props.item.discount_amount,
			gross_amount: props.item.gross_amount,
			total: props.item.total,
			total_basic: props.item.total_basic || 0,
			base_unit_price: props.item.base_unit_price,
			unit_price: props.item.unit_price,
			pickup_status: props.item.pickup_status,
			available_qty: props.item.available_qty,
			status: props.item.status,
			user_warehouse: props.item.user_warehouse,
			is_available: props.item.is_available,
			status: props.item.status,
			status2: props.item.status2,
			date_available: props.item.date_available,
			group_position: props.item.sort_group_position,
			salesman_discount_price_type: props.item.salesman_discount_price_type,
			salesman_discount_required_reason: props.item.salesman_discount_required_reason,
			salesman_discount_attachments: props.item.salesman_discount_attachments,
			salesman_discount_internal_reason_id: props.item.salesman_discount_internal_reason_id,
			salesman_discount_percent: props.item.salesman_discount_percent,
			salesman_discount_price: props.item.salesman_discount_price,
			salesman_original_price: props.item.salesman_original_price,
			salesman_price_definition: props.item.salesman_price_definition || false
		};
		webshopStore.itemSelected.push(data);

		if(options?.mode == 'salesmanPriceDefinition') {
			openFlyout({
				mode: 'salesmanPriceDefinition',
			});
		} else {
			store.loading = 1;
			let salesmanMaxDiscount = store.info ? store.info.salesman_max_discount_percent : 0;
			await useFetch({
				url: `${ep.endpoints.value._get_hapi_webshop_salesman_price_reasons}?product_code=${props.item.code}`,
				method: 'GET',
			}).then(async res => {
				store.loading = 0;
				openFlyout({
					mode: 'salesmanPrice',
					salesmanMaxPercent: salesmanMaxDiscount,
					sPriceReasons: res.data,
				});
			});
		}
	}

	watch(() => webshopStore.salesmanPriceItem, (newValue, oldValue) => {
		webshopStore.salesmanPriceItem = null
		if(newValue == props.item.shopping_cart_code) {
			openSalesmanPrice();
		}
	});

	function selectItem() {
		webshopStore.unchecked = 0;
		if (currentlySelectedItem.value) {
			const data = {
				id: props.item.id,
				shopping_cart_code: props.item.shopping_cart_code,
				title: props.item.item.title,
				url: props.item.item.url,
				code: props.item.item.code,
				quantity: props.item.quantity,
				discount_amount: props.item.discount_amount,
				gross_amount: props.item.gross_amount,
				total: props.item.total,
				unit_price: props.item.unit_price,
				pickup_status: props.item.pickup_status,
				available_qty: props.item.available_qty,
				status: props.item.status,
				user_warehouse: props.item.user_warehouse,
				is_available: props.item.is_available,
				status: props.item.status,
				status2: props.item.status2,
				date_available: props.item.date_available,
				group_position: props.item.sort_group_position,
				avans_enabled: props.item.avans_enabled,
				force_avans: props.item.force_avans,
				salesman_price_required_reason: props.item.salesman_price_required_reason,
			};
			webshopStore.itemSelected.push(data);
		} else {
			let index = webshopStore.itemSelected.findIndex(el => el.shopping_cart_code == props.item.shopping_cart_code);
			webshopStore.itemSelected.splice(index, 1);
		}
	}

	function setOption(action, event) {
		event.preventDefault();
		let options = {
			shopping_cart_code: props.item.shopping_cart_code,
			quantity: props.item.quantity,
		};
		if (action == 'warehouse') options.warehouse_pickup = event.target.checked;
		if (action == 'avans'){
			if(props.item.user_force_avans === false) {
				options.user_force_avans_item = null;
			} else {
				options.avans = event.target.checked;
			}
		}

		webshopStore.updateProduct(options);
	}

	// breakpoints
	import {breakpoints} from '@/composables/useBreakpoints';
	import {useRoute} from 'vue-router';
	const mobileBp = breakpoints('m');
	const tabletBp = breakpoints('t');
	const route = useRoute();

	watch(
		() => [mobileBp.value, route.path],
		([tbp]) => {
			const productContainers = document.querySelectorAll('.wp');
			productContainers.forEach(container => {
				_moveElement(mobileBp.value, container);
			});
		}
	);
	onMounted(() => {
		const productContainers = document.querySelectorAll('.wp');
		productContainers.forEach(container => {
			_moveElement(mobileBp.value, container);
		});
	});

	function _moveElement(bp, container) {
		const wpErrors = container.querySelector('.wp-errors');
		const cntCol3 = container.querySelector('.wp-col3');
		const cntRow2 = container.querySelector('.wp-row2');
		const cntCol4 = container.querySelector('.wp-col4');

		if (bp) {
			cntRow2.prepend(wpErrors);
			cntRow2.before(cntCol4);
		} else {
			cntCol3.append(wpErrors);
			cntCol4.before(cntCol3);
		}
	}

	//qty class
	const availableQtyClass = computed(() => {
		let itemClass;
		if(props.item.status == '9' || props.item.status2 == '4') {
			itemClass = 'not-available';
		} else if(props.item.status == '2') {
			itemClass = 'supplier';
		} else if(props.item.status == '7') {
			itemClass = 'not-available-in-store';
		} else if(props.item.status == '5') {
			itemClass = 'preorder';
		}

		return itemClass;
	});

	//coupon tooltip text
	let couponBadgeTooltip = ref(false);
	let couponBadgeTooltipText = ref(store.labels.pa_coupon_copy ? store.labels.pa_coupon_copy : 'Kopiraj kodo');

	//copy coupon
	function copyCoupon(value) {
		navigator.clipboard.writeText(value);

		if (tabletBp.value == true) {
			couponBadgeTooltipText.value = store.labels.pa_coupon_copied ? store.labels.pa_coupon_copied : 'Koda je kopirana';
			setTimeout(() => {
				couponBadgeTooltip.value = false;
			}, 2000);
		} else {
			couponBadgeTooltipText.value = store.labels.pa_coupon_copied ? store.labels.pa_coupon_copied : 'Koda je kopirana';
			setTimeout(() => {
				couponBadgeTooltipText.value = store.labels.pa_coupon_copy ? store.labels.pa_coupon_copy : 'Kopiraj kodo';
			}, 2000);
		}
	}
</script>

<style lang="less" scoped>
	.wp-errors {
		padding: 3px 0 0;
		@media (max-width: @m) {
			padding: 6px 0 0;
		}
	}
	.wp-error {
		color: @red;
		background: #fbe8e8;
		margin: 0 0 7px;
		padding: 7px 10px;
		font-size: 12px;
		line-height: 1.4;
		position: relative;
		display: flex;
		align-items: center;
		&:before {
			.icon-danger-red;
			font: 15px/1 @fonti;
			margin-right: 6px;
			position: relative;
			margin-top: -1px;
		}

		&.warning {
			color: @blue;
			background: rgba(0, 120, 186, 0.1);
		}
	}
</style>
