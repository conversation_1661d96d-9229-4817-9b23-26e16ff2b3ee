<template>
	<div :class="['w-cart-item', {'active': cart.cart_active, 'details-active': cart.cart_active && activeContent}]">
		<div class="w-cart-header" @click="selectCart">
			<div class="w-cart-name-container" :class="{'not-allowed': cart?.cart_title_rename_allowed == false}">
				<span class="button" :class="{'active': editTitle}" @click.stop="(editTitle = !editTitle), focus(cart.id)"></span>
				<span v-if="cart?.cart_title_rename_allowed == false && cart.first_order_id" class="title"><span v-if="cart?.cart_order_type">{{ store.labels['order_type_' + cart.cart_order_type] }}</span> {{ cart.first_order_id }}</span>
				<template v-else>
					<span v-if="!editTitle" class="title">{{ cartTitle }}</span>
					<input v-if="editTitle" class="w-cart-name" type="code" @click.stop="" @blur="blur" v-model="cartTitle" :id="'code_' + cart.id" :name="'code_' + cart.id" />
				</template>
				<span v-if="(!cart.cart_active || !activeContent) && cart.total_price" class="price">{{ formatCurrency(cart.total_price) }}</span>
			</div>
		</div>
		<CartType v-if="cart?.cart_active && activeContent && cart?.cart_title_rename_allowed" />
		<OrderType v-if="cart?.cart_active && activeContent && !cart?.cart_title_rename_allowed" />
		<RemoveCart :cart="cart" />
		<div class="w-cart-body">
			<ShoppingCartContentInactive :cart="cart" v-if="!cart.cart_active" />
			<ShoppingCartContent :cart="cart" v-else />
		</div>
	</div>
</template>

<script setup>
	import {ref} from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useToken} from '@/composables/useToken';
	import useHelpers from '@/composables/useHelpers';
	import ShoppingCartContentInactive from '@/components/webshop/ShoppingCartContentInactive.vue';
	import ShoppingCartContent from '@/components/webshop/ShoppingCartContent.vue';
	import RemoveCart from '@/components/webshop/widget/RemoveCart.vue';
	import CartType from '@/components/webshop/widget/CartType.vue';
	import OrderType from '@/components/webshop/widget/OrderType.vue';

	const props = defineProps(['cart', 'index']);
	const store = useStore();
	const webshopStore = useWebshopStore();
	const token = useToken();
	const {formatCurrency} = useHelpers();
	const activeContent = ref(true);
	let editTitle = ref(false);

	// select cart
	const selectCart = async () => {
		if (props.cart.cart_active == false || activeContent.value == false) {
			store.loading = 2;
			await token.generateToken({
				tokenId: props.cart.token_id,
				tokenHash: props.cart.token_hash,
			});
			webshopStore.fetchCarts();
			activeContent.value = true;
			store.loading = 0;
		} else {
			activeContent.value = false;
		}
	};

	// update cart title
	const cartTitle = ref(props.cart.cart_title);

	function blur() {
		setTimeout(() => {
			updateCartTitle();
		}, 100);
	}

	async function updateCartTitle() {
		await webshopStore.updateCartTitle({
			cart_token_id: props.cart.token_id,
			cart_token_hash: props.cart.token_hash,
			cart_title: cartTitle.value,
		});
		webshopStore.carts[props.index].cart_title = cartTitle.value;
		editTitle.value = false;
	}

	function focus(value) {
		setTimeout(() => {
			const el = document.getElementById('code_' + value);
			el.focus();
		}, 500);
	}
</script>

<style lang="less">
	.w-cart-item {
		flex-grow: 1;
		background: @white;
		border: 1px solid @borderColor;
		border-radius: @borderRadius;
		margin-bottom: 35px;
		box-shadow: 0 10px 20px 0 rgba(0, 34, 67, 0.03);
		position: relative;
		&.active {
			.w-cart-header:after {
				opacity: 1;
			}
			.w-cart-name {
				color: @white;
				font-weight: bold;
				.placeholder(@white, @borderColor);
			}
			.w-cart-name-container {
				.title,
				.price {
					color: @white;
					font-weight: bold;
				}
				.button {
					background: @white;
					&.active {
						background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%);
					}
				}
			}
		}
		&.details-active {
			.w-cart-name-container{margin-right: 250px;}
			.w-cart-sw {
				width: 100%;
				border-bottom: 1px solid @borderColor;
			}
			.w-cart-content {
				display: block;
				margin-top: -1px;
			}
		}

		@media (max-width: @t) {
			&.details-active .w-cart-select {
				display: flex;
			}
		}
		@media (max-width: @m) {
			margin: 15px;
			box-shadow: none;
			&.details-active {
				box-shadow: 0 10px 20px 0 rgba(0, 34, 67, 0.03);
				.w-cart-name-container{margin-right: 30px;}
				.w-cart-bottom {
					padding-top: 0;
				}
				.ww-coupons {
					display: flex;
				}
				.w-cart-coupon,
				.w-card-code,
				.w-cart-content-bottom {
					display: block;
				}
			}
		}
	}

	.w-cart-header {
		display: flex;
		align-items: center;
		flex-grow: 1;
		min-height: 72px;
		padding: 15px 0 15px 25px;
		.transition(all);
		position: relative;
		cursor: pointer;
		&:after {
			.pseudo(auto, auto);
			left: -1px;
			top: -1px;
			bottom: 0;
			right: -1px;
			background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%);
			opacity: 0;
			z-index: 0;
			.transition(opacity);
		}

		@media (max-width: @m) {
			min-height: 46px;
			padding: 10px 15px;
		}
	}

	.w-cart-name-container {
		display: flex;
		align-items: center;
		margin-right: 54px;
		padding-right: 30px;
		position: relative;
		flex-grow: 1;
		z-index: 1;
		&.not-allowed .button{pointer-events: none; opacity: 0.4;}
		.title {
			flex-grow: 1;
			font-size: 18px;
			line-height: 1.3;
			letter-spacing: -0.4px;
		}
		.price {
			margin-left: 15px;
			font-size: 18px;
			line-height: 1.3;
			letter-spacing: -0.4px;
			font-weight: bold;
		}
		.button {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
			width: 30px;
			height: 30px;
			margin: -3px 12px 0 0;
			border: 1px solid transparent;
			border-radius: 100%;
			background: #eaf1f7;
			cursor: pointer;
			.transition(all);
			z-index: 1;
			&:before {
				.icon-edit;
				font: 14px/1 @fonti;
				color: @blue;
				z-index: 1;
			}
			&.active {
				background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%);
				border-color: @white;
				&:before {
					.icon-check;
					font-size: 12px;
					color: @white;
				}
			}
		}

		@media (max-width: @m) {
			margin-right: 30px;
			padding-right: 0;
			.title,
			.price {
				font-size: 14px;
				letter-spacing: -0.3px;
			}
			.button {
				width: 26px;
				height: 26px;
				margin-right: 10px;
				&:before {
					font-size: 12px;
				}
				&.active:before {
					font-size: 9px;
				}
			}
		}
	}

	.w-cart-name {
		flex-grow: 1;
		width: auto;
		height: auto;
		background: transparent;
		padding: 0;
		border: none;
		border-radius: 0;
		font-size: 18px;
		line-height: 1.3;
		letter-spacing: -0.4px;
		.placeholder(@textColor, @borderColor);
		.transition(color);

		@media (max-width: @m) {
			font-size: 14px;
			letter-spacing: -0.3px;
		}
	}

	/*
	.w-cart-name-confirm {
		opacity: 0;
		z-index: -11;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 30px;
		height: 30px;
		font-size: 0;
		line-height: 0;
		border-radius: @borderRadius;
		flex-shrink: 0;
		background: linear-gradient(225deg, #01aaba 0%, #0092a0 100%);
		position: absolute;
		top: -5px;
		right: 0;
		&:before {
			.icon-check;
			font: 9px/9px @fonti;
			color: @white;
			position: absolute;
		}

		@media (max-width: @m) {
			width: 24px;
			height: 24px;
			top: -3px;
			right: -8px;
		}
	}
	*/

	.w-cart-body {
		position: relative;
		flex-grow: 1;
		border-top: 1px solid @borderColor;
	}
</style>
