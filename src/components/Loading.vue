<template>
	<div v-if="store.loading" class="form-loading" :class="{'special': store.loading == 2}">
		<span>{{ store.labels.waiting_messages }}</span>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';

	const store = useStore();
</script>

<style lang="less" scoped>
	@keyframes visibility {
		0% {
			visibility: hidden;
		}
		99% {
			visibility: hidden;
		}
		100% {
			visibility: visible;
		}
	}

	.form-loading {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999999999;
		color: #fff;
		font-size: 15px;
		animation-name: visibility;
		animation-duration: 1s;
		&.special {
			animation-name: none;
			animation-duration: unset;
		}
	}
	.form-loading span {
		position: absolute;
		padding: 110px 20px 20px 20px;
		width: 230px;
		left: 50%;
		margin-left: -115px;
		top: 40%;
		text-align: center;
		box-shadow: 0 0 30px 0 rgba(0, 0, 0, 0.3);
		color: #000;
		background: #fff url(/media/images/loader.svg) no-repeat center 20px;
		background-size: 75px;
	}
	.form-loading:before {
		content: '';
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		position: absolute;
		background: rgba(32, 42, 51, 0.6);
	}
</style>
