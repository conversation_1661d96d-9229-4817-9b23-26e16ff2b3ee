<template>
	<div class="flyout" v-if="isFlyoutActive()" ref="modal">
		<div v-if="!mobileBp || (mobileBp && flyout.mode != 'shipping')" class="flyout-close-btn" @click="closeFlyout()"></div>
		<div class="flyout-top" :class="{'special': flyout.mode == 'coupons', 'extra': flyout.mode == 'reservation' && webshopStore?.cart?.cart?.payment_due_days?.original}">
			<!-- change payment type -->
			<template v-if="flyout.mode == 'payments'">
				<div class="flyout-row1">
					<div class="flyout-title special flyout-title-single">{{store.labels.pa_payment_type}}</div>
				</div>
				<div class="flyout-row2">
					<div class="field-shipping-row" v-for="payment in webshopStore?.cart?.cart?.payments?.available" :key="payment.id">
						<input type="radio" name="payment" :value="payment.id" :id="`payment-${payment.id}`" :checked="payment.id == selectedPayment.id" @click="selectedPayment = payment">
						<label :for="`payment-${payment.id}`">{{payment.title}}</label>
					</div>
				</div>
				<div class="flyout-bottom">
					<template v-if="!orderErrors">
						<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
						<a class="btn btn-green" @click="submitPayment()">
							<span>Potrdi</span>
						</a>
					</template>
				</div>
			</template>

			<!-- flyout header -->
			<div v-if="['shipping', 'avans', 'salesmanPrice', 'imei', 'salesmanPriceDefinition'].includes(flyout.mode) || (flyout.mode == 'services' && flyout.availableServices.data.items && flyout.availableServices.data.items.length)" class="flyout-row1" :class="{'active': flyoutItemToggle}">
				<!-- IMEI title -->
				<template v-if="flyout.mode == 'imei'">
					<div class="flyout-title imei special">
						{{ store.labels.pa_imei_number }} <span v-if="flyout.selectedImeis && flyout.selectedImeis.length > 0" class="qty-item blue">({{ flyout.selectedImeis.length }})</span>
					</div>
					<div v-if="flyout.availableImeis.length" class="flyout-scan-camera">
						<div class="flyout-scan-camera-box">
							<div class="flyout-camera-loading" v-if="camLoading">{{ store.labels.waiting_messages }}</div>
							<StreamBarcodeReader @decode="onDecode" @loaded="camLoading = 0" />
						</div>
					</div>
				</template>

				<!-- shipping products -->
				<template v-if="['shipping', 'avans', 'salesmanPrice', 'salesmanPriceDefinition'].includes(flyout.mode)">
					<div class="flyout-title special">
						<template v-if="flyout.mode == 'shipping'">
							{{ store.labels.pa_chosen_shipping_items }} <span v-if="webshopStore.itemSelected.length" class="qty-item">({{ webshopStore.itemSelected.length }})</span> <span class="btn-more" @click="flyoutItemToggle = !flyoutItemToggle"></span>
						</template>
						<template v-if="['salesmanPrice', 'salesmanPriceDefinition'].includes(flyout.mode)">
							{{ store.labels['pa_salesman_' + webshopStore.itemSelected[0].salesman_discount_price_type] }}
						</template>
						<template v-if="flyout.mode == 'avans'">
							{{ store.labels.user_force_complete_prepayment_base_question }}
						</template>
					</div>
					<div class="wp-row1" v-for="(item, index) in productList" :key="item.id">
						<div v-if="flyout.mode == 'shipping'" class="wp-order-numb">{{ index + 1 }}.</div>
						<div class="wp-warehouse-qty wp-col2">
							<div
								class="wp-warehouse-qty-box"
								:class="
									(function () {
										if(item.status == '9' || item.status2 == '4') {
											return 'not-available';
										} else if(item.status == '2') {
											return 'supplier';
										} else if(item.status == '7') {
											return 'not-available-in-store';
										} else if(item.status == '5') {
											return 'preorder';
										}
									})()
								"></div>
						</div>
						<div class="wp-cnt wp-col3">
							<h2 class="wp-title">
								<a :href="item.url">
									<template v-if="item?.title_custom">{{ item.title_custom }}</template>
									<template v-else>{{ item.title }}</template>
								</a>
							</h2>
							<div class="wp-codes">
								<div class="wp-code">
									<strong>{{ store.labels.id }}: </strong>{{ item.code }}
								</div>
								<div class="wp-ean" v-if="item.ean_code">
									<span>{{ item.ean_code }}</span>
								</div>
							</div>
						</div>
						<div class="wp-total wp-col5">
							<div class="wp-price-container">
								<div class="wp-qty-count-tooltip">
									<span class="product_qty">{{ item.quantity }}</span> kom
								</div>
								<div class="wp-price-old line-through" v-if="item.discount_amount">{{ formatCurrency(item.gross_amount * item.quantity) }}</div>
								<div class="wp-price-discount wp-price-current">{{ formatCurrency(item.total) }}</div>
								<div class="wp-qty-count wp-price-old" v-if="item.quantity > 1">
									<span class="product_qty product_qty_special">{{ item.quantity }}</span> x <span>{{ formatCurrency(item.unit_price) }}</span>
								</div>
							</div>
						</div>
					</div>
				</template>

				<!-- services autocomplete -->
				<template v-if="flyout.mode == 'services'">
					<ServiceSearch :data="flyout.availableServices.data.items" @closeBox="closeFlyout()" @serviceMessageUpdated="handleServiceMessageUpdate" />
				</template>
			</div>

			<div class="flyout-row2">
				<!-- flyout title -->
				<div v-if="flyout.mode == 'coupons'" class="flyout-header-row2">
					<div class="flyout-title">
						{{ flyout.title }}<span v-if="flyout.availableCoupons.length" class="flyout-title-info"> ({{ flyout.availableCoupons.length }})</span>
					</div>
					<div v-if="flyout.availableCoupons.length" class="flyout-coupon-info" v-html="store.labels.pa_coupons_list_info.replace('%n%', selectedCoupons.length + '/' + flyout.availableCoupons.length)"></div>
				</div>

				<!-- imei inputs -->
				<template v-else-if="flyout.mode == 'imei'">
					<template v-if="flyout.availableImeis.length">
						<InputImei v-for="(item, index) in flyout.availableImeis.slice(0, vissibleFields)" :key="index" :item="item" :warnings="flyout.imeiProductWarnings" :itemIndex="index" :selectedImeis="flyout.selectedImeis" :scanedValue="scanedValueIndex == index ? scanedValue : null" @input="updateSearchTerm(index, $event)" />
						<div v-if="flyout.availableImeis.length > vissibleFields" class="imei-btn-more">
							<span @click="addField()">{{ store.labels.pa_add_imei }}</span>
						</div>
					</template>
					<template v-else>
						<span v-html="store.labels.empty_available_imei"></span>
					</template>
				</template>

				<div v-else class="flyout-title">{{ flyout.title }}</div>
				<div class="flyout-content" v-html="flyout.content" />

				<!-- shipping options -->
				<template v-if="flyout.mode == 'shipping'">
					<ShippingOption :data="store.flyout" />
					<Loader v-if="!store.flyout.shipping" />
				</template>

				<!-- item title edit -->
				<template v-if="flyout.mode == 'itemTitleEdit'">
					<div class="sp-title">{{ store.labels.pa_product_title_edit }}</div>
					<input v-model="itemTitle" ref="inputTitleField" name="item-title" id="field-item-title" type="text" autofocus>
				</template>

				<!-- coupons list -->
				<template v-if="flyout.mode == 'coupons' && flyout.availableCoupons">
					<div v-if="flyout.availableCoupons && flyout.availableCoupons.length" class="flyout-coupons-list">
						<div v-if="couponMessage" class="flyout-coupons-list-error red">{{ store.labels[couponMessage] }}</div>
						<div v-for="item in flyout.availableCoupons" :key="item.code" class="flyout-coupons-list-item">
							<div class="flyout-coupons-list-item-content">
								<div class="flyout-coupons-list-item-title">{{ item.code }}</div>
								<div v-if="item.description" class="flyout-coupons-list-item-desc">{{ item.description }}</div>
							</div>
							<div class="flyout-coupons-list-item-btn">
								<input @change="selectCoupon(item.code)" type="checkbox" :id="'item_' + item.code" :name="'item_' + item.code" :value="'item-' + item.code" />
								<label :for="'item_' + item.code">{{ store.labels.pa_coupon_select }}</label>
							</div>
						</div>
					</div>
					<div v-else class="flyout-coupons-list-empty">{{ store.labels.pa_coupons_empty }}</div>
				</template>

				<!-- avans force -->
				<template v-if="flyout.mode == 'avans'">
					<div class="flyout-avans">
						<textarea v-model="avansForceMessage" @input="validateAvansMessage" @blur="blurValidateAvansMessage" maxlength="200" name="field_avans_force" type="text" :placeholder="store.labels.user_force_complete_prepayment_reason_question" />
						<span v-if="avansForceMessageError" class="error">{{ avansForceMessageError }}</span>
					</div>
				</template>

				<!-- services list -->
				<template v-if="flyout.mode == 'services' && flyout.availableServices">
					<div v-if="flyout.availableServices.data.items && flyout.availableServices.data.items.length > 0" class="flyout-services-list">
						<div v-if="serviceMessage" class="flyout-services-list-error red" v-html="store.labels[serviceMessage] ? store.labels[serviceMessage] : serviceMessage"></div>
						<div v-for="item in flyout.availableServices.data.items" :key="item.code" class="flyout-services-list-item">
							<div class="flyout-services-list-item-col1">
								<router-link :to="item.url_without_domain" class="flyout-services-list-item-title">{{ item.title }}</router-link>
								<div v-if="item.code" class="flyout-services-list-item-desc">
									<strong>{{ store.labels.id }}: </strong>{{ item.code }}
								</div>
							</div>
							<div class="flyout-services-list-item-col2">
								<div class="flyout-services-list-item-price">
									<template v-if="item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom">
										<div class="old-price line-through">{{ formatCurrency(item.basic_price_custom) }}</div>
										<div class="current-price discount-price red">(-{{ item.discount_percent_custom }}%) {{ formatCurrency(item.price_custom) }}</div>
									</template>
									<div v-else class="current-price">{{ formatCurrency(item.price_custom) }}</div>
								</div>
								<button @click="addService(item.shopping_cart_code)" class="flyout-services-list-item-btn">{{ store.labels.pa_coupon_select }}</button>
							</div>
						</div>
					</div>
					<div v-else class="flyout-services-list-empty">{{ store.labels.pa_services_empty }}</div>
				</template>

				<!-- salesman price definition -->
				<template v-if="flyout.mode == 'salesmanPriceDefinition'">
					<div class="global-error" v-if="spdError">{{ store.labels[spdError] || spdError }}</div>
					<div class="sp-field-container">
						<div class="sp-title special">
							<span>{{store.labels.pa_salesman_basic_price}}</span>
						</div>
						<span class="sp-filed-number">
							<label for="field-salesman-price-definition-basic">
								<input v-model="spdBasic" @input="maskPrice($event.target.value, (v) => spdBasic = v)" id="field-salesman-price-definition-basic" type="text">
								<span class="text">€</span>
							</label>
						</span>
					</div>
					<div class="sp-field-container">
						<div class="sp-title special">
							<span>{{store.labels.pa_salesman_discount_price}}</span>
						</div>
						<span class="sp-filed-number">
							<label for="field-salesman-price-definition-discount">
								<input v-model="spdDiscount" @input="maskPrice($event.target.value, (v) => spdDiscount = v)" id="field-salesman-price-definition-discount" type="text">
								<span class="text">€</span>
							</label>
						</span>
					</div>
				</template>

				<!-- salesman price -->
				<template v-if="flyout.mode == 'salesmanPrice'">
					<!-- shipping -->
					<template v-if="webshopStore.itemSelected[0].salesman_discount_price_type == 'shipping'">
						<div class="sp-field-container">
							<div class="sp-title">{{ store.labels.pa_salesman_price_reason_title }}</div>
							<select name="reason" id="field-salesman-price-reason" v-model="spReasonShipping">
								<option disabled value="">{{ store.labels.pa_salesman_price_select  }}</option>
								<option v-for="item in flyout.sPriceReasons" :key="item.id" :value="item.title" v-html="item.title"></option>
							</select>
						</div>
						<template v-if="spReasonShipping">
							<div class="sp-field-container">
								<div class="sp-title special">
									<span>{{ store.labels.pa_salesman_price_add_price_title }}</span>
									<div v-if="flyout.customShippingPrice" @click="removeSalesmanShippingPrice" class="sp-btn-remove">{{ store.labels.pa_salesman_price_remove }}</div>
								</div>
								<span class="sp-filed-number">
									<label for="field-salesman-price-number">
										<input v-model="spNumberShipping" @input="validateSpNumberShipping" name="salesman-price-number" id="field-salesman-price-number" type="text">
										<span class="text">€</span>
									</label>
								</span>
								<div v-if="spPercentError" class="sp-field-error error">{{ store.labels.pa_salesman_price_max_percentage_error }} {{ flyout.salesmanMaxPercent }}%</div>
							</div>
							<div class="sp-field-container">
								<div class="sp-title">{{ store.labels.pa_salesman_price_attachment_title }}</div>
								<span class="sp-filed-attachment">
									<label for="field-salesman-price-attachment">
										{{ store.labels.pa_salesman_price_input_attachment }}<span class="icon"></span>
										<input multiple ref="fileInput" @change="handleFileUpload" type="file" id="field-salesman-price-attachment" name="salesman-price-attachment">
									</label>
								</span>

								<div v-if="spAttachmentError" class="sp-field-error error">{{ spAttachmentError }}</div>

								<div class="sp-files">
									<div class="sp-file" v-for="(file, index) in spAttachment" :key="file.name">
										<span class="title">{{ file.name }}</span>
										<!--<span v-if="webshopStore.itemSelected && webshopStore.itemSelected[0].salesman_discount_attachments" class="delete" @click="deleteExistingFile(index)"></span>-->
										<span class="delete" @click="deleteFile(index)"></span>
									</div>
								</div>
							</div>
						</template>
					</template>
					<!-- service -->
					<template v-if="webshopStore.itemSelected[0].salesman_discount_price_type == 'service'">
						<div class="sp-field-container">
							<div class="sp-title">{{ store.labels.pa_salesman_price_add_price_title }}</div>
							<span class="sp-filed-number">
								<label for="field-salesman-price-number">
									<input v-model="spNumberLight" @input="validateSpNumberLight" name="salesman-price-number" id="field-salesman-price-number" type="text">
									<span class="text">€</span>
								</label>
							</span>
							<div v-if="spPercentError" class="sp-field-error error">{{ store.labels.pa_salesman_price_max_percentage_error }} {{ flyout.salesmanMaxPercent }}%</div>
						</div>
					</template>
					<!-- default -->
					<template v-if="webshopStore.itemSelected[0].salesman_discount_price_type == 'default'">
						<div v-if="webshopStore.itemSelected[0].salesman_discount_required_reason" class="sp-field-container">
							<div class="sp-title">{{ store.labels.pa_salesman_price_reason_title }}</div>
							<select name="reason" id="field-salesman-price-reason" v-model="spReason" @change="setSalesmanMaxDiscountPercent($event.target.value)">
								<option disabled value="">{{ store.labels.pa_salesman_price_select }}</option>
								<template v-for="item in flyout.sPriceReasons" :key="item.id">
									<option v-if="item.enabled" :value="item.title" v-html="item.title"></option>
								</template>
							</select>
						</div>
						<template v-if="webshopStore.itemSelected[0].salesman_discount_required_reason == false || (spReason || webshopStore.itemSelected[0].salesman_discount_internal_reason_id)">
							<div class="sp-field-container">
								<div class="sp-title special">
									<span>{{ store.labels.pa_salesman_price_add_title }}</span>
									<div v-if="webshopStore.itemSelected[0].salesman_discount_internal_reason_id" @click="removeSalesmanPrice" class="sp-btn-remove">{{ store.labels.pa_salesman_price_remove }}</div>
								</div>
								<div class="sp-fileds-row">
									<span class="sp-filed-percent">
										<label for="field-salesman-price-percent">
											<input v-model="spPercent" @input="validateSpPercentage(selectedSalesmanPriceReason?.max_discount_percent)" @blur="blurValidatePercentage(selectedSalesmanPriceReason?.max_discount_percent)" name="salesman-price-percent" id="field-salesman-price-percent" type="number">
											<span class="text">%</span>
										</label>
									</span>
									<span class="sp-filed-number" :class="{'special': spNumber == webshopStore.itemSelected[0].unit_price}">
										<label for="field-salesman-price-number">
											<span class="text">{{ store.labels.pa_salesman_price_input_number }}</span>
											<input v-model="spNumber" @input="validateSpNumber(selectedSalesmanPriceReason?.max_discount_percent)" name="salesman-price-number" id="field-salesman-price-number" type="number">
											<span class="text">€</span>
										</label>
									</span>
								</div>
								<div v-if="selectedSalesmanPriceReason" class="sp-field-error error">{{ store.labels.pa_salesman_price_max_percentage_error }} {{selectedSalesmanPriceReason?.max_discount_percent}}%</div>
							</div>
							<div class="sp-field-container">
								<div class="sp-title">{{ store.labels.pa_salesman_price_attachment_title }}</div>
								<span class="sp-filed-attachment">
									<label for="field-salesman-price-attachment">
										{{ store.labels.pa_salesman_price_input_attachment }}<span class="icon"></span>
										<input multiple ref="fileInput" @change="handleFileUpload" type="file" id="field-salesman-price-attachment" name="salesman-price-attachment">
									</label>
								</span>

								<div v-if="spAttachmentError" class="sp-field-error error">{{ spAttachmentError }}</div>

								<div class="sp-files">
									<div class="sp-file" v-for="(file, index) in spAttachment" :key="file.name">
										<span class="title">{{ file.name }}</span>
										<!--<span v-if="webshopStore.itemSelected && webshopStore.itemSelected[0].salesman_discount_attachments" class="delete" @click="deleteExistingFile(index)"></span>-->
										<span class="delete" @click="deleteFile(index)"></span>
									</div>
								</div>
							</div>
						</template>
					</template>
				</template>

				<!-- reservation -->
				<template v-if="flyout.mode == 'reservation'">
					<div v-if="webshopStore?.cart?.customer" class="reservation-info">
						<div v-if="webshopStore?.cart?.customer?.first_name" class="name">{{ webshopStore?.cart?.customer?.first_name }} {{ webshopStore?.cart?.customer?.last_name }}</div>
						<div v-if="webshopStore?.cart?.customer?.email" class="mail">{{ webshopStore?.cart?.customer?.email }}</div>
						<div v-if="webshopStore?.cart?.cart?.payment_due_days?.original" class="date">
							{{ store.labels.pa_reservation_customer_due_days }}
							<span class="fw-b">{{ webshopStore?.cart?.cart?.payment_due_days?.original }} dni</span>
						</div>
					</div>

					<OrderOptions @orderFormChanged="handleOrderFormChanged" />
				</template>

				<!-- order -->
				<template v-if="flyout.mode == 'order'">
					<div v-if="webshopStore?.cart?.customer" class="reservation-info">
						<div v-if="webshopStore?.cart?.customer?.first_name" class="name">{{ webshopStore?.cart?.customer?.first_name }} {{ webshopStore?.cart?.customer?.last_name }}</div>
						<div v-if="webshopStore?.cart?.customer?.email" class="mail">{{ webshopStore?.cart?.customer?.email }}</div>
						<div v-if="webshopStore?.cart?.cart?.payment_due_days?.original" class="date">
							{{ store.labels.pa_reservation_customer_due_days }}
							<span class="fw-b">{{ webshopStore?.cart?.cart?.payment_due_days?.original }} dni</span>
						</div>
					</div>

					<OrderOptions @orderFormChanged="handleOrderFormChanged" />
				</template>
			</div>
		</div>
		
		<!-- Flyout bottom -->
		<div v-if="flyout.mode == 'order'" class="flyout-bottom">
			<template v-if="!orderErrors">
				<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
				<template v-if="webshopStore?.cart?.cart?.indicators?.cart_title_rename_allowed">
					<a v-if="webshopStore?.cart?.cart?.cart_order_type?.selected" @click="submitOrder(webshopStore?.cart?.cart?.cart_order_type?.selected)" class="btn btn-green" :class="{'disabled': buttonDisabled}">
						<span>{{ store.labels['flyout_button_' + webshopStore?.cart?.cart?.cart_order_type?.selected] }}</span>
					</a>
				</template>
				<template v-else>
					<a v-if="webshopStore?.cart?.cart?.order_type?.selected" @click="submitOrder(webshopStore?.cart?.cart?.order_type?.selected)" class="btn btn-green" :class="{'disabled': buttonDisabled}">
						<span>{{ store.labels['flyout_button_' + webshopStore?.cart?.cart?.order_type?.selected] }}</span>
					</a>
				</template>
			</template>
		</div>
		<div v-if="flyout.mode == 'reservation'" class="flyout-bottom">
			<template v-if="!reservationErrors">
				<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
				<template v-if="webshopStore?.cart?.cart?.indicators?.cart_title_rename_allowed">
					<a v-if="webshopStore?.cart?.cart?.cart_order_type?.selected" @click="submitOrder(webshopStore?.cart?.cart?.cart_order_type?.selected)" class="btn btn-green" :class="{'disabled': buttonDisabled}">
						<span>{{ store.labels['flyout_button_' + webshopStore?.cart?.cart?.cart_order_type?.selected] }}</span>
					</a>
				</template>
				<template v-else>
					<a v-if="webshopStore?.cart?.cart?.order_type?.selected" @click="submitOrder(webshopStore?.cart?.cart?.order_type?.selected)" class="btn btn-green" :class="{'disabled': buttonDisabled}">
						<span>{{ store.labels['flyout_button_' + webshopStore?.cart?.cart?.order_type?.selected] }}</span>
					</a>
				</template>
			</template>
		</div>
		<div v-if="flyout.mode == 'shipping'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
			<a @click="webshopStore.setShippingOption(flyout.modeSecond && flyout.modeSecond == 'merge' ? 'merge' : ''), closeFlyout('shippingMode')" class="btn" v-if="store.flyout?.validForm" :class="{'disabled': !store.flyout?.validForm}">
				<span>{{ store.labels.pa_save_shipping }}</span>
			</a>
		</div>
		<div v-if="flyout.mode == 'avans'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
			<div @click="setAvansForce()" class="btn" v-if="avansForceMessage" :class="{'disabled': !avansForceMessage}">
				<span>{{ store.labels.user_force_complete_prepayment_base_question }}</span>
			</div>
		</div>
		<div v-if="flyout.mode == 'salesmanPrice'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
			<a @click="setSalesmanShippingPrice()" v-if="webshopStore.itemSelected[0].salesman_discount_price_type == 'shipping' && spReasonShipping && ((selectedShippingOption && !selectedShippingOption.attachment_required) || spAttachment.length || spAttachmentUpdate)" class="btn">
				<span>{{ store.labels.pa_save_salesman_shipping_price }}</span>
			</a>
			<a @click="setUniversalProductPrice()" v-if="webshopStore.itemSelected[0].salesman_discount_price_type == 'service'" class="btn">
				<span>{{ store.labels.pa_save_salesman_service_price }}</span>
			</a>
			<a @click="setSalesmanPrice()" v-if="webshopStore.itemSelected[0].salesman_discount_price_type == 'default' && spReason && spPercent && ((selectedOption && !selectedOption.attachment_required) || spAttachment.length || spAttachmentUpdate) && (spAttachmentUpdate || spNumber != webshopStore.itemSelected[0].unit_price)" class="btn">
				<span>{{ store.labels.pa_save_salesman_price }}</span>
			</a>
		</div>
		<div v-if="flyout.mode == 'salesmanPriceDefinition'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
			<a @click="setSalesmanPriceDefinition()" class="btn">
				<span>{{ store.labels.pa_save_salesman_shipping_price }}</span>
			</a>
		</div>
		<div v-if="flyout.mode == 'imei' && errorImei == false && (searchTermsEmpty.length || flyout.selectedImeis.length)" class="flyout-bottom">
			<div @click="setImeis()" class="btn full">
				<span>{{ store.labels.pa_save_imei }}</span>
			</div>
		</div>
		<div v-if="flyout.mode == 'coupons'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
			<div v-if="selectedCoupons.length" @click="addCoupons()" class="btn">
				<span>{{ store.labels.flyout_close_confirm }}</span>
			</div>
		</div>
		<div v-if="flyout.mode == 'itemTitleEdit'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.cancel }}</div>
			<div v-if="itemTitle" @click="editTitle()" class="btn">
				<span>{{ store.labels.flyout_close_confirm }}</span>
			</div>
		</div>
		<div v-if="flyout.mode == 'condition'" class="flyout-bottom">
			<div class="btn btn-white" @click="closeFlyout()">{{ store.labels.close }}</div>
		</div>
	</div>
</template>

<script setup>
	import {computed, ref, onUnmounted, watch, onMounted} from 'vue';
	import {useRouter} from 'vue-router';
	import { useToken } from '@/composables/useToken'
	import { useConfig } from '@/composables/useConfig'
	import {useEventBus} from '@/composables/useEventBus';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useFetch} from '@/composables/useFetch';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {onClickOutside} from '@vueuse/core';
	import {breakpoints} from '@/composables/useBreakpoints';
	import ShippingOption from '@/components/webshop/widget/ShippingOption.vue';
	import OrderOptions from '@/components/webshop/widget/OrderOptions.vue';
	import ServiceSearch from '@/components/webshop/widget/ServiceSearch.vue';
	import InputImei from '@/components/webshop/widget/InputImei.vue';
	import {StreamBarcodeReader} from 'vue-barcode-reader';
	import Loader from '@/components/Loader.vue';
	import useHelpers from '@/composables/useHelpers';
	import useFlyout from '@/composables/useFlyout';
	import useDom from '@/composables/useDom';

	const ep = useEndpoints();
	const { authToken } = useToken()
	const { baseUrl } = useConfig()
	const {emit} = useEventBus();
	const store = useStore();
	const webshopStore = useWebshopStore();
	const router = useRouter();
	const {formatCurrency, maskPrice, unmaskPrice} = useHelpers();
	const {closeFlyout: closeFlyoutPanel, isFlyoutActive} = useFlyout();
	const {removeBodyClass, scrollTo} = useDom();
	const props = defineProps(['flyoutItemToggle', 'openValue']);

	const flyout = computed(() => {
		return store.flyout;
	});

	const mobileBp = breakpoints('m');
	const couponMessage = ref(null);
	const serviceMessage = ref(null);
	const searchTerms = ref([]);

	//flyout close
	const closeFlyout = (value) => {
		closeFlyoutPanel();

		couponMessage.value = '';
		webshopStore.itemSelected = [];
		webshopStore.unchecked = 1;
		setTimeout(() => {
			webshopStore.unchecked = 0;
			if(value != 'shippingMode') {
				store.loading = 0;
			}
		}, 1000);

		searchTerms.value = [];

		spReason.value = '';
	 	spPercent.value = 0;
		spNumber.value = '';
		spNumberShipping.value = '';
		spReasonShipping.value = '',
		spNumberLight.value = '0,00';
		spAttachment.value = [];
		spdError.value = null;

		serviceMessage.value = null

		avansForceMessage.value = null;
		avansForceMessageError.value = null;

		selectedCoupons.value = [];

		itemTitle.value = null;
		spAttachmentUpdate.value = false;
	};

	//product list
	const productList = computed(() => {
		if(flyout.value.mode == 'avans') {
			return webshopStore.itemSelected.filter(item => item.avans_enabled === false);
		} else {
			return webshopStore.itemSelected;
		}
	});

	// close modal on outside click and escape keyup
	const modal = ref(null);
	onClickOutside(modal, () => closeFlyout());

	const onKeyUp = e => {
		if (e.key == 'Escape') closeFlyout();
	};
	window.addEventListener('keyup', onKeyUp);
	onUnmounted(() => window.removeEventListener('keyup', onKeyUp));

	//item title edit
	const itemTitle = ref(null);
	const inputTitleField = ref();

	async function editTitle(){
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_customer,
			method: 'POST',
			body: {
				"title_item_custom": {
					"code": flyout.value.shopping_cart_code,
					"title": itemTitle.value
				}
			}
		}).then(res => webshopStore.fetchCarts());

		store.loading = 0;
		closeFlyout();
	}

	const focusInput = () => {
		inputTitleField.value.focus();
	};
	watch(
		() => flyout.value.mode,
		newValue => {
			if(flyout.value.mode == 'itemTitleEdit') {
				setTimeout(() => {
					focusInput()
				}, 100);
			}

			selectedPayment.value = webshopStore.cart?.cart?.payments?.selected?.[0] || null;
		}
	);

	// payments
	const selectedPayment = ref();
	async function submitPayment() {
		await webshopStore.updatePayment({
			payment: selectedPayment.value.id,
			cc_installments: selectedPayment.value?.installments || 1,
			payment_option_id: selectedPayment.value?.payment_option_id || 0
		});
		closeFlyout();
	}	

	//coupons
	const selectedCoupons = ref([]);
	function selectCoupon(value) {
		if (!selectedCoupons.value.includes(value)) {
			selectedCoupons.value.push(value);
		} else {
			selectedCoupons.value = selectedCoupons.value.filter(coupon => coupon !== value);
		}
	}

	async function addCoupons() {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_coupon,
			method: 'POST',
			body: {'code': selectedCoupons.value},
		}).then(async res => {
			if (res.success) {
				await webshopStore.fetchCartItems();
				closeFlyout();
			} else {
				couponMessage.value = res.data.label_name;
				selectedCoupons.value = [];
			}
			store.loading = 0;
		});
	}

	//avans force
	let avansForceMessage = ref(null);
	let avansForceMessageError = ref(null);

	function validateAvansMessage() {
		if(avansForceMessage.value && avansForceMessage.value.length > 199) {
			avansForceMessageError.value = store.labels.error_max_length + ' 200';
		} else {
			avansForceMessageError.value = '';
		}
	}

	function blurValidateAvansMessage() {
		if(!avansForceMessage.value) {
			avansForceMessageError.value = store.labels.error_not_empty;
		} else {
			avansForceMessageError.value = '';
		}
	}

	async function setAvansForce() {
		store.loading = 1;
		let options = productList.value.map(item => ({
			shopping_cart_code: item.shopping_cart_code,
			quantity: item.quantity,
			user_force_avans_item: false,
			user_force_avans_item_reason: avansForceMessage.value
		}));

		await useFetch({
			url: ep.endpoints.value._put_hapi_webshop_product,
			method: 'PUT',
			body: options,
		}).then(async res => {
			await webshopStore.fetchCartItems();
			store.loading = 0;
			closeFlyout();
		});
	}

	//salesman price
	let spReason = ref('');
	let spPercent = ref(0);
	let spNumber = ref('');
	let itemTotal = ref(0);
	let spPercentError = ref(false);
	let spAttachment = ref([]);
	let spAttachmentError = ref(false);

	watch(() => webshopStore.itemSelected.length && webshopStore.itemSelected[0].salesman_discount_internal_reason_id ? flyout.value.sPriceReasons.find(item => item.id === webshopStore.itemSelected[0].salesman_discount_internal_reason_id)?.title : '', (newReason) => {
		spReason.value = newReason;
		spReasonShipping.value = newReason;
	});

	watch(() => webshopStore.itemSelected.length && webshopStore.itemSelected[0].salesman_discount_percent ? webshopStore.itemSelected[0].salesman_discount_percent : 0, (newDiscount) => {
		spPercent.value = newDiscount;
	});

	watch(() => webshopStore.itemSelected.length ? webshopStore.itemSelected[0].unit_price : 0, (newTotal) => {
		spNumber.value = newTotal;
		spNumber.value = parseFloat(spNumber.value.toFixed(2));
		itemTotal.value = newTotal;
	});

	watch(() => webshopStore.itemSelected.length ? webshopStore.itemSelected[0].salesman_discount_attachments : [], (newFiles) => {
		spAttachment.value = newFiles;
	});

	const selectedOption = computed(() => {
		return flyout.value.sPriceReasons.find((item) => item.title === spReason.value);
	});

	const selectedShippingOption = computed(() => {
		return flyout.value.sPriceReasons.find((item) => item.title === spReasonShipping.value);
	});

	const reasonId = computed(() => {
		return flyout.value.sPriceReasons.find((item) => item.title === spReason.value);
	});

	// Get max discount percent for selected reason
	const selectedSalesmanPriceReason = ref(null);
	function setSalesmanMaxDiscountPercent(event) {
		// Reset input values
		spPercent.value = 0;
		spNumber.value = webshopStore.itemSelected.length ? webshopStore.itemSelected[0].unit_price : '';
		
		// Set selected reason
		const selectedReason = flyout.value.sPriceReasons.find((item) => item.title === event);
		selectedSalesmanPriceReason.value = selectedReason ? selectedReason : null;
	}

	function validateSpPercentage(maxPercent) {
		if (+spPercent.value < 0) {
			spPercent.value = 0;
		} else if (+spPercent.value > +maxPercent) {
			spPercent.value = maxPercent;
		} 
		if (spPercent.value != '' && (+spPercent.value < +maxPercent)) {
			spPercent.value = parseFloat(spPercent.value.toFixed(2));
		}

		let discount = spPercent.value / 100;
		let amount = webshopStore.itemSelected[0].base_unit_price * discount;
		spNumber.value = webshopStore.itemSelected[0].base_unit_price - amount;
		spNumber.value = parseFloat(spNumber.value.toFixed(2));
	}

	function blurValidatePercentage(maxPercent) {
		if(spNumber.value == itemTotal.value) {
			spPercent.value = webshopStore.itemSelected.length && maxPercent ? maxPercent : 0;
		}
	}

	let validateSpNumberTimeout = ref(null);
	function validateSpNumber(maxPercent) {
		// Clear any existing timeout
		if (validateSpNumberTimeout.value) {
			clearTimeout(validateSpNumberTimeout.value);
		}
		
		// Set a new timeout to validate after user stops typing (300ms)
		validateSpNumberTimeout.value = setTimeout(() => {
			// Format the number to have 2 decimal places
			if (spNumber.value != '') {
				spNumber.value = parseFloat(spNumber.value.toFixed(2));
			}

			// Get the base price
			const base_unit_price = webshopStore.itemSelected[0].base_unit_price;
			
			// Calculate the minimum allowed price based on maxPercent
			const minAllowedPrice = base_unit_price * (1 - (maxPercent / 100));
			
			// Check if entered price is less than the minimum allowed price (exceeds max discount percentage)
			if (spNumber.value < minAllowedPrice) {
				// If price exceeds allowed percentage, reset to original price
				spNumber.value = base_unit_price;
			}
			
			// Check if price is higher than the base price
			if (spNumber.value > base_unit_price) {
				spNumber.value = itemTotal.value;
			}

			// Calculate discount percentage
			if (spNumber.value > 0 || spNumber.value > itemTotal.value) {
				const discount = base_unit_price - spNumber.value;
				spPercent.value = (discount / base_unit_price) * 100;
				spPercent.value = parseFloat(spPercent.value.toFixed(2));
			}
			
			validateSpNumberTimeout.value = null;
		}, 500);
	}

	const handleFileUpload = (event) => {
		const files = event.target.files;
		for (let i = 0; i < files.length; i++) {
			const file = files[i];
			if (file.size <= 10 * 1024 * 1024) {
				if (file.type === 'application/pdf' || file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png') {
					const reader = new FileReader();
					reader.onload = () => {
						spAttachment.value.push(file);
					};
					reader.readAsDataURL(file);
				} else {
					spAttachmentError.value = store.labels.pa_salesman_price_file_error;
					setTimeout(() => {
						spAttachmentError.value = false;
					}, 2000);
				}
			} else {
				spAttachmentError.value = store.labels.pa_salesman_price_file_size_error;
				setTimeout(() => {
					spAttachmentError.value = false;
				}, 2000);
			}
		}

		spAttachmentUpdate.value = true;
	}

	//salesman price service
	const spNumberLight = ref('0,00');

	const validateSpNumberLight = (event) => {
		const input = event.target.value.replace(/[^\d]/g, '');
		let paddedInput = input.padStart(3, '0');

		const integerPart = paddedInput.slice(0, -2);
		const decimalPart = paddedInput.slice(-2);
		let formattedIntegerPart = integerPart.replace(/^0+(?=\d)/, '');
		if (formattedIntegerPart === '') {
			formattedIntegerPart = '0';
		}

		const formattedInteger = formattedIntegerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
		const formattedValue = `${formattedInteger},${decimalPart}`;
		spNumberLight.value = formattedValue;
	};

	//salesman price shipping
	let spReasonShipping = ref('');
	const reasonShippingId = computed(() => {
		return flyout.value.sPriceReasons.find((item) => item.title === spReasonShipping.value);
	});

	const spNumberShipping = ref(flyout.value.shippingPrice ? flyout.value.shippingPrice : 0);
	watch(flyout, (newTotal) => {
		spNumberShipping.value = newTotal.shippingPrice;
		if(spNumberShipping.value) {
			validateSpNumberShipping();
		}
	});

	function validateSpNumberShipping(event) {
		let input;
		if (event && event.target) {
			input = event.target.value.replace(/[^\d]/g, '');
		} else {
			if(!spNumberShipping.value.toString().includes('.')) {
				spNumberShipping.value = parseFloat(spNumberShipping.value).toFixed(2);
			}
			input = spNumberShipping.value.toString().replace(/[^\d]/g, '');
		}
		let paddedInput = input.padStart(3, '0');
		const integerPart = paddedInput.slice(0, -2);
		const decimalPart = paddedInput.slice(-2);
		let formattedIntegerPart = integerPart.replace(/^0+(?=\d)/, '');
		if (formattedIntegerPart === '') {
			formattedIntegerPart = '0';
		}

		const formattedInteger = formattedIntegerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
		const formattedValue = `${formattedInteger},${decimalPart}`;
		spNumberShipping.value = formattedValue;
	};

	/*
	async function deleteExistingFile(index) {
		store.loading = 1;

		await useFetch({
			url: ep.endpoints.value._delete_hapi_webshop_salesman_price_attachment,
			method: 'DELETE',
			body: {"shopping_cart_code": webshopStore.itemSelected[0].shopping_cart_code, 'delete_index': index}
		}).then(async res => {
			if (res.success) {
				await webshopStore.fetchCartItems();
				closeFlyout();
			}
			store.loading = 0;
		});
		spAttachment.value.splice(index, 1);
	};
	*/
	let spAttachmentUpdate = ref(false);
	const deleteFile = (index) => {
		spAttachment.value.splice(index, 1);
		spAttachmentUpdate.value = true;
	};

	async function removeSalesmanPrice() {
		store.loading = 1;
		await useFetch({
			url: ep.endpoints.value._delete_hapi_webshop_salesman_price,
			method: 'DELETE',
			body: {"shopping_cart_code": webshopStore.itemSelected[0].shopping_cart_code}
		}).then(async res => {
			if (res.success) {
				await webshopStore.fetchCartItems();
				closeFlyout();
			}
			store.loading = 0;
		});
	}

	async function removeSalesmanShippingPrice() {
		store.loading = 1;
		await useFetch({
			url: ep.endpoints.value._delete_hapi_webshop_shipping_salesman_price,
			method: 'DELETE',
			body: {"shopping_cart_code": webshopStore.itemSelected[0].shopping_cart_code}
		}).then(async res => {
			if (res.success) {
				await webshopStore.fetchCartItems();
				closeFlyout();
			}
			store.loading = 0;
		});
	}

	async function setSalesmanPrice() {
		store.loading = 1;
		let code = webshopStore.itemSelected[0].shopping_cart_code;

		const formData = new FormData();
		formData.append('shopping_cart_code', webshopStore.itemSelected[0].shopping_cart_code);
		formData.append('discount_reason_id', reasonId.value.id);
		formData.append('discount_percent', spPercent.value);
		formData.append('discount_price', spNumber.value);
		formData.append('original_price', webshopStore.itemSelected[0].base_unit_price);
		spAttachment.value.forEach((attachment, index) => {
			formData.append(`attachment_${index + 1}`, attachment);
		});

		const requestOptions = {
			method: 'POST',
			body: formData
		};
		fetch(`${baseUrl}${ep.endpoints.value._post_hapi_webshop_salesman_price}`, requestOptions)
		.then(async response => {
			if (response.status == 200) {
				store.loading = 0;
				await webshopStore.fetchCartItems();
				closeFlyout();
			} else {
				closeFlyout();
				store.loading = 0;
				response.json().then((data) => {
					emit('salesmanPriceModal', {item: data.data, 'shopping_cart_code': code});
				});
			}
		});
	}

	async function setUniversalProductPrice() {
		store.loading = 1;
		let code = webshopStore.itemSelected[0].shopping_cart_code;
		let cleanedString = spNumberLight.value.replace(/[^\d,-]/g, '');
		let normalizedString = cleanedString.replace(',', '.');
		let floatValue = parseFloat(normalizedString);

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_salesman_price,
			method: 'POST',
			body: {
				'shopping_cart_code': webshopStore.itemSelected[0].shopping_cart_code,
				'discount_reason_id': null,
				'discount_percent': null,
				'discount_price': floatValue,
				'original_price': null
			}
		}).then(res => {
			if (res.success) {
				store.loading = 0;
				webshopStore.fetchCartItems();
				closeFlyout();
			} else {
				closeFlyout();
				store.loading = 0;
				emit('salesmanPriceModal', {item: res.data, 'shopping_cart_code': code});
			}
		});
	}

	async function setSalesmanShippingPrice() {
		store.loading = 1;
		const code = webshopStore.itemSelected.map(item => item.shopping_cart_code);
		let cleanedString = spNumberShipping.value.replace(/[^\d,-]/g, '');
		let normalizedString = cleanedString.replace(',', '.');
		let floatValue = parseFloat(normalizedString);

		const formData = new FormData();
		formData.append('shopping_cart_code', code);
		formData.append('discount_reason_id', reasonShippingId.value.id);
		formData.append('discount_percent', null);
		formData.append('discount_price', floatValue);
		formData.append('original_price', flyout.value.shippingPrice);
		spAttachment.value.forEach((attachment, index) => {
			formData.append(`attachment_${index + 1}`, attachment);
		});

		const requestOptions = {
			method: 'POST',
			body: formData
		};

		fetch(`${baseUrl}${ep.endpoints.value._post_hapi_webshop_shipping_salesman_price}`, requestOptions)
		.then(async response => {
			if (response.status == 200) {
				store.loading = 0;
				await webshopStore.fetchCartItems();
				closeFlyout();
			} else {
				closeFlyout();
				store.loading = 0;
				response.json().then((data) => {
					emit('salesmanPriceModal', {item: data.data, 'shopping_cart_code': code});
				});
			}
		});
	}

	// salesman price definition (testni proizvod, SKU: 22123141, usluga ID: 343603)
	const spdBasic = ref(0);
	const spdDiscount = ref(0);
	const spdError = ref(null);

	watch(flyout, flyoutData =>{
		if(flyoutData?.mode == 'salesmanPriceDefinition') {
			const selectedItems = webshopStore.itemSelected;
			if(!selectedItems.length) return;
			spdBasic.value = maskPrice(selectedItems[0].salesman_original_price || 0, null, {init: true});
			spdDiscount.value = maskPrice(selectedItems[0].salesman_discount_price || 0, null, {init: true});
		}
	})

	async function setSalesmanPriceDefinition() {
		spdError.value = null;
		if(!webshopStore.itemSelected?.length) return;
		const selectedItem = webshopStore.itemSelected[0];
		store.loading = 1;

		const res = await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_salesman_definition_price,
			method: 'POST',
			body: {
				shopping_cart_code: selectedItem.shopping_cart_code,
				original_price: unmaskPrice(spdBasic.value),
				discount_price: unmaskPrice(spdDiscount.value)
			}
		});
		
		if(res.data?.label_name?.startsWith('error_')) {
			let error = res.data.label_name;
			
			// Label name changed because of admin code field limit
			if(error == 'error_discount_price_must_be_less_than_original_price') {
				error = 'error_salesperson_price_regular_invalid'
			}
			spdError.value = error;
			store.loading = 0;
			return;
		}

		await webshopStore.fetchCarts();
		closeFlyout();
		store.loading = 0;
	}	

	//add service
	async function addService(value) {
		store.loading = 1;
		const cartCode = value;

		await webshopStore
			.addProduct({
				shopping_cart_code: cartCode,
			})
			.then(res => {
				if (res.success) {
					closeFlyout();
				} else {
					serviceMessage.value = res.data.label_name;
				}
				store.loading = 0;
			});
	}

	function handleServiceMessageUpdate(labelName) {
		serviceMessage.value = labelName;
	}

	//add imei field
	const vissibleFields = computed(() => {
		return flyout.value.imeiInputsVisible;
	});
	function addField() {
		flyout.value.imeiInputsVisible = flyout.value.imeiInputsVisible + 1;
	}

	//send imeis
	let searchTermsEmpty = ref([]);
	let searchTermsVisibleFields = ref();
	let errorImeis = ref();
	let errorImei = ref(false);

	const updateSearchTerm = (index, searchTerm) => {
		searchTerms.value[index] = searchTerm;
		searchTermsEmpty.value = searchTerms.value.filter(searchTerm => searchTerm !== '' && searchTerm != null);
		searchTermsVisibleFields.value = searchTerms.value.filter(searchTerm => searchTerm != undefined);

		setTimeout(() => {
			errorImeis.value = document.querySelectorAll('.error-imei');
			if (errorImeis.value.length > 0) {
				errorImei.value = true;
			} else {
				errorImei.value = false;
			}
		}, 500);

		if (searchTermsVisibleFields.value.length == flyout.value.imeiInputsVisible) {
			addField();
		}
	};

	async function setImeis() {
		store.loading = 1;
		setTimeout(async () => {
			const imeiValue = searchTerms.value.filter(searchTerm => searchTerm !== '' && searchTerm != null);

			await useFetch({
				url: ep.endpoints.value._put_hapi_webshop_product,
				method: 'PUT',
				body: [{'shopping_cart_code': flyout.value.imeiProductCode, 'quantity': flyout.value.imeiProductQty, 'imeis': imeiValue}],
			}).then(async res => {
				webshopStore.fetchCarts();
				closeFlyout();
				store.loading = 0;
			});
		}, 500);
	}

	//scaner
	const camLoading = ref(1);

	watch(
		() => props.openValue,
		newValue => {
			if (!newValue) camLoading.value = 1;
		}
	);

	let scanedValue = ref();
	let scanedValueIndex = ref();

	function findLastNonEmptyIndex(arr) {
		for (let i = arr.length - 1; i >= 0; i--) {
			if (arr[i] !== '' && arr[i] != undefined && arr[i] != null) {
				return i + 1;
			}
		}
		return 0;
	}

	function onDecode(payload) {
		scanedValueIndex.value = findLastNonEmptyIndex(searchTerms.value);
		scanedValue.value = payload;

		setTimeout(() => {
			errorImeis.value = document.querySelectorAll('.error-imei');
			if (errorImeis.value.length > 0) {
				errorImei.value = true;
			} else {
				errorImei.value = false;
			}
		}, 500);
	}

	//order
	let buttonDisabled = ref(false);
	let orderErrors = ref();

	let orderOptionData = ref();
	function handleOrderFormChanged(value) {
		orderOptionData.value = value;
	}

	//reservation
	let reservationErrors = ref();

	async function submitOrder(type) {
		buttonDisabled.value = true;
		store.loading = 1;
		let isOrderReady = false;

		await useFetch({
			url: ep.endpoints.value._post_hapi_webshop_order_type,
			method: 'POST',
			body: {'order_type': type},
		}).then(async res => {
			if(res.success) {
				await useFetch({
					url: ep.endpoints.value._post_hapi_webshop_cart_order_type_options,
					method: 'POST',
					body: orderOptionData.value,
				}).then(async res => {
					if(res.success) {
						await webshopStore.fetchCartItems().then(async response => {
							if(response.cart.is_order_ready == false) {
								await useFetch({
									url: ep.endpoints.value._post_hapi_webshop_cart_order_type,
									method: 'POST',
									body: {'cart_order_type': type},
								}).then(async res => {
									if(res.success) {
										await webshopStore.fetchCartItems();
									} else {
										webshopStore.cartError = store.labels[res.data.label_name] ? store.labels[res.data.label_name] : res.data.label_name;
										webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
									}
								});
								closeFlyout();
								scrollTo('.global-error', {offset: 100});
							} else if(response.cart.is_order_ready == true) {
								isOrderReady = true;
							}
						})
					} else {
						webshopStore.cartError = store.labels[res.data.label_name] ? store.labels[res.data.label_name] : res.data.label_name;
						webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
						closeFlyout();
						scrollTo('.global-error', {offset: 100});
					}
				});
			} else {
				webshopStore.cartError = store.labels[res.data.label_name] ? store.labels[res.data.label_name] : res.data.label_name;
				webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
				closeFlyout();
				scrollTo('.global-error', {offset: 100});
			}
		});

		if(isOrderReady) {
			store.loading = 2;
			store.creation = true;
			localStorage.setItem('creation', JSON.stringify(store.creation));

			await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_order,
				method: 'POST',
				body: {'success': true, 'data': webshopStore.cart},
			}).then(async res => {
				if (res.success) {
					await webshopStore.fetchCarts();
					webshopStore.successMessage = res.data;
					
					store.creation = false;
					localStorage.setItem('creation', JSON.stringify(store.creation));
				} else {
					await webshopStore.fetchCarts();
					if(res.data.label_name === 'error_order_has_changed_price') {
						const label = store.labels[res.data.label_name];
						const formattedList = res.data.changed_price_list.map((item, index) => {
							const formattedItem = `${index + 1}. ${item.code} - nova cena: ${item.new_price} €, stara cena: ${item.old_price} €`;
							return formattedItem;
						});
						const replacedLabel = label.replace('%DATA%', formattedList.join('<br>\n'));
						webshopStore.cartError = replacedLabel;
						webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
					} else {
						webshopStore.cartError = store.labels[res.data.label_name] ? store.labels[res.data.label_name] : res.data.label_name;
						webshopStore.cartErrorInfo = res.data.errors ? res.data.errors : null;
					}
					store.loading = 0;
					store.creation = false;
					localStorage.setItem('creation', JSON.stringify(store.creation));
				}
			});

			closeFlyout();
		}

		store.loading = 0;
		buttonDisabled.value = false;
	}
</script>

<style lang="less" scoped>
	.flyout {
		width: 620px;
		background: @white;
		position: fixed;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 11111;
		box-shadow: 0 20px 30px 0 rgba(0, 34, 68, 0.3);
		&.active {
			display: block;
		}

		@media (max-width: @m) {
			width: 100%;
			padding: 0;
			box-shadow: none;
			input[type='checkbox'] + label,
			input[type='radio'] + label {
				min-height: 20px;
				padding: 1px 0 0 30px;
			}
			input[type='checkbox'] + label:before,
			input[type='radio'] + label:before {
				width: 18px;
				height: 18px;
			}
			input[type='radio']:checked + label:before {
				box-shadow: inset 0 0 0 3px #fff;
			}
		}
	}
	.flyout-close-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 54px;
		height: 54px;
		background: linear-gradient(226.06deg, #e6473a 0%, #c9221b 100%);
		border: 1px solid @white;
		position: absolute;
		left: -54px;
		top: 0;
		cursor: pointer;
		.transition(opacity);
		&:before {
			.icon-x;
			font: 20px/1 @fonti;
			color: @white;
		}
		&:hover {
			opacity: 0.8;
		}

		@media (max-width: @m) {
			width: 34px;
			height: 34px;
			border: none;
			border-radius: 2px;
			top: 8px;
			right: 8px;
			left: unset;
			z-index: 11;
			&:before {
				font-size: 12px;
			}
		}
	}
	.flyout-top {
		height: 100%;
		padding-bottom: 130px;
		overflow-x: hidden;
		overflow-y: auto;
		&.extra{padding-bottom: 170px;}
		&.special {
			padding-bottom: 30px;
			&::-webkit-scrollbar-track-piece {
				margin: 0;
			}
			&::-webkit-scrollbar-track {
				border: none;
			}
		}
		&::-webkit-scrollbar-track-piece {
			margin-bottom: 130px;
		}
		&::-webkit-scrollbar-track {
			border-bottom: 130px solid @white;
		}

		@media (max-width: @m) {
			padding-bottom: 65px;
			&.special {
				padding: 50px 0 0;
				&::-webkit-scrollbar-track-piece {
					margin-top: 65px;
					margin-bottom: 15px;
				}
				&::-webkit-scrollbar-track {
					border-top: 65px solid @white;
					border-bottom: 15px solid @white;
				}
			}
			&::-webkit-scrollbar-track-piece {
				margin-bottom: 65px;
			}
			&::-webkit-scrollbar-track {
				border-bottom: 65px solid @white;
			}
		}
	}
	.flyout-title {
		font-size: 18px;
		line-height: 1.4;
		letter-spacing: -0.36px;
		font-weight: 600;
		padding-bottom: 15px;
		flex-grow: 1;
		&.imei {
			padding-left: 33px;
			position: relative;
			&:before {
				.icon-barcode;
				font: 20px/1 @fonti;
				color: @blue;
				position: absolute;
				left: 0;
				top: 1px;
			}
			.qty-item {
				display: inline-block;
			}
		}
		.btn-more,
		.qty-item {
			display: none;
		}

		@media (max-width: @m) {
			font-size: 14px;
			&.special {
				padding: 15px;
				position: relative;
				.qty-item {
					display: inline-block;
					color: @blue;
				}
				.btn-more {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 28px;
					height: 28px;
					background: linear-gradient(63.43deg, #0050a0 0%, #0078b4 100%);
					border-radius: @borderRadius;
					position: absolute;
					right: 15px;
					top: 11px;
					&:before {
						.icon-arrow-down;
						font: 8px/9px @fonti;
						color: @white;
						.transition(all);
					}
				}
			}
			&.imei {
				padding-left: 45px;
				&:before {
					left: 15px;
					top: 12px;
				}
			}
		}
	}
	@media (min-width: @m){
		.flyout-title-single{padding: 0;}
	}
	//title
	.flyout-title-info {
		color: @blue;
	}
	.flyout-content{
		font-size: 15px; line-height: 1.5;
		&>*:first-child{padding-top: 0;}
		h2,h3,h4,h5{padding: 20px 0 10px; font-size: 24px;}
		ol{margin: 0 0 15px 15px;}
		img{width: auto; height: auto; max-width: 100%; max-height: 100%;}

		@media (max-width: @m){
			font-size: 13px;
			h2,h3,h4,h5{padding: 15px 0 10px; font-size: 17px;}
		}
	}

	.flyout-row1 {
		padding: 30px 80px;
		background: #f8f9fa;
		border-bottom: 1px solid @borderColor;
		.wp-row1 {
			margin-bottom: -1px;
			padding: 15px 20px;
			background: @white;
			border: 1px solid @borderColor;
			&:first-child {
				border-radius: 2px 2px 0 0;
			}
			&:last-child {
				border-radius: 0 0 2px 2px;
			}
		}
		.wp-order-numb {
			display: block;
			margin-right: 8px;
			font-size: 14px;
			line-height: 1.6;
			letter-spacing: -0.23px;
			font-weight: 600;
		}
		.wp-cnt {
			padding: 3px 0 0 0;
		}
		.wp-total {
			width: 120px;
			min-height: unset;
			padding-top: 4px;
		}
		.wp-qty-count-tooltip {
			display: block;
			padding-bottom: 2px;
			font-size: 12px;
			line-height: 1.4;
			letter-spacing: -0.2px;
			opacity: 0.7;
		}
		.wp-price-current {
			color: @textColor;
			text-decoration: none;
		}
		.wp-check-button,
		.wp-title .barcode,
		.w-cart-checkbox-section,
		.wp-col4,
		.wp-qty-count,
		.wp-installments-tooltip {
			display: none;
		}

		@media (max-width: @m) {
			padding: 0;
			.wp-row1 {
				display: none;
				margin: 0 15px -1px;
				padding: 10px 15px;
				&:last-child {
					margin-bottom: 15px;
				}
			}
			.wp-order-numb {
				margin-right: 7px;
				font-size: 12px;
			}
			.wp-cnt {
				padding: 2px 0 0 0;
			}
			.wp-total {
				width: 80px;
				padding-top: 2px;
			}
			&.active {
				.w-cart-shipping-tooltip-title.special .btn-more {
					background: linear-gradient(270deg, #00b0de 0%, #0085cf 100%);
					&:before {
						.rotate(180deg);
					}
				}
				.wp-row1 {
					display: flex;
				}
			}
		}
	}
	.flyout-row2 {
		padding: 30px 80px 0;

		@media (max-width: @m) {
			padding: 15px;
		}
	}
	.flyout-header-row2 {
		@media (max-width: @m) {
			display: flex;
			align-items: center;
			height: 50px;
			padding: 8px 50px 8px 15px;
			background: @white;
			border-bottom: 1px solid @borderColor;
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			z-index: 1;

			.flyout-title {
				padding: 0;
			}
		}
	}

	//scaner
	.flyout-scan-camera {
		position: relative;
	}
	.flyout-camera-loading {
		color: @white;
		position: absolute;
		z-index: 1;
	}
	.flyout-scan-camera-box {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100% !important;
		height: 200px;
		background: #000;
		border-radius: @borderRadius;
		overflow: hidden;
	}
	video {
		display: block;
	}

	//imei
	.imei-btn-more {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		margin-top: 30px;
		font-size: 14px;
		letter-spacing: -0.28px;
		font-weight: bold;
		color: @blue;
		cursor: pointer;
		&:hover {
			text-decoration: underline;
		}
		span {
			padding-left: 20px;
			position: relative;
			&:before,
			&:after {
				.pseudo(12px,2px);
				background: @blue;
				left: 0;
				top: 8px;
			}
			&:after {
				width: 2px;
				height: 12px;
				left: 5px;
				top: 3px;
			}
		}
	}

	//coupons
	.flyout-coupon-info{
		display: block; margin-bottom: 20px; font-size: 14px;
		:deep(span){color: @blue; font-weight: bold;}

		@media (max-width: @m){display: none;}
	}
	.flyout-coupons-list {
		flex-grow: 1;
	}
	.flyout-coupons-list-error {
		font-size: 14px;
		margin: 0 0 10px;
	}
	.flyout-coupons-list-item {
		display: flex;
		align-items: center;
		width: 100%;
		margin-bottom: -1px;
		padding: 20px 20px 20px 50px;
		border: 1px solid @borderColor;
		position: relative;
		.transition(all);
		&:hover {
			border-color: @blue;
			box-shadow: 0 10px 20px 0 rgba(0, 34, 67, 0.05);
			z-index: 1;
		}

		@media (max-width: @m) {
			padding: 20px 15px 20px 45px;
		}
	}
	.flyout-coupons-list-item-content {
		flex-grow: 1;
		padding-right: 30px;
	}
	.flyout-coupons-list-item-title {
		font-size: 14px;
		line-height: 1.4;
		font-weight: bold;
		color: @textColor;
		position: relative;
		&:before {
			.icon-coupon;
			font: 13px/13px @fonti;
			color: @red;
			font-weight: bold;
			position: absolute;
			left: -30px;
			top: 3px;
		}
	}
	.flyout-coupons-list-item-desc {
		font-size: 12px;
		line-height: 1.4;
		margin-top: 3px;
	}
	.flyout-coupons-list-item-btn {
		input[type=checkbox]+label{
			display: flex;
			align-items: center;
			justify-content: center;
			height: 37px;
			padding: 0 12px 0 42px;
			border: 1px solid @borderColor;
			border-radius: 2px;
			font-size: 12px;
			line-height: 1;
			letter-spacing: -0.24px;
			font-weight: bold;
			.transition(none);
		}
		input[type=checkbox]+label:before{top: unset; left: 8px; .transition(none);}
		input[type=checkbox]:checked+label{color: @blue; border-color: @blue; background: #eaf4f9;}
		input[type=checkbox]:checked+label:before{background: white; border-color: @blue; color: @blue;}
	}

	.flyout-coupons-list-empty {
		font-size: 16px;

		@media (max-width: @m) {
			font-size: 14px;
		}
	}

	//avans
	.flyout-avans{
		flex-grow: 1;
		textarea{padding: 20px; .placeholder(@textColor, @borderColor);}
	}

	//services
	.flyout-services-list {
		flex-grow: 1;
	}
	.flyout-services-list-error {
		font-size: 14px;
		margin: 0 0 10px;
	}
	.flyout-services-list-item {
		display: flex;
		align-items: flex-start;
		width: 100%;
		margin-bottom: -1px;
		padding: 20px;
		border: 1px solid @borderColor;
		position: relative;
		.transition(all);
		&:hover {
			border-color: @blue;
			box-shadow: 0 10px 20px 0 rgba(0, 34, 67, 0.05);
			z-index: 1;
		}

		@media (max-width: @m) {
			padding: 15px;
		}
	}
	.flyout-services-list-item-col1 {
		flex-grow: 1;
		padding-right: 30px;
	}
	.flyout-services-list-item-title {
		font-size: 14px;
		line-height: 1.4;
		font-weight: bold;
		color: @textColor;
		position: relative;
		.transition(color);
		&:hover {
			color: @blue;
		}

		@media (max-width: @m) {
			font-size: 12px;
			line-height: 1.3;
		}
	}
	.flyout-services-list-item-desc {
		font-size: 12px;
		line-height: 1.4;
		margin-top: 5px;
	}
	.flyout-services-list-item-col2 {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		flex-shrink: 0;
	}
	.flyout-services-list-item-price {
		text-align: right;
		.old-price {
			font-size: 12px;
			padding-bottom: 2px;
		}
		.current-price {
			font-size: 14px;
			font-weight: bold;
		}
	}
	.flyout-services-list-item-btn {
		height: 30px;
		min-height: 30px;
		margin-top: 10px;
		padding: 0 11px;
		font-size: 12px;
		letter-spacing: -0.24px;
	}

	.flyout-services-list-empty {
		font-size: 16px;

		@media (max-width: @m) {
			font-size: 14px;
		}
	}

	//salesman price
	.sp-field-container{
		margin-bottom: 25px;
		input{width: 100%; height: unset; flex-grow: 1; padding: 0; border: none;}
		label{
			display: flex; align-items: center; flex-grow: 1; height: 50px; padding: 0 20px; background: @white; border: 1px solid @borderColor; border-radius: 2px; font-size: 14px; line-height: 1.4; color: @textColor; cursor: pointer; .transition(border-color);
			&:hover{border-color: #a8afb4;}
		}
		.text{flex-shrink: 0;}
	}
	.sp-title{
		font-size: 14px; line-height: 1.5; font-weight: bold; margin-bottom: 8px;
		&.special{
			display: flex; align-items: center;
			span{flex-grow: 1;}
		}
	}
	.sp-fileds-row{display: flex;}
	.sp-filed-percent{
		width: 25%; margin-right: 20px;
	}
	.sp-filed-number{
		flex-grow: 1;
		label, input{background: #F6F7F8;}
		input{padding-left: 5px; font-weight: bold; color: @red;}

		&.special input{color: #ADBAC4;}
	}
	.sp-filed-attachment{
		width: 100%;
		label{
			justify-content: center; padding: 0; background-color: rgba(0,120,186,0.1); border: 1px dashed @blue; border-radius: 2px; font-weight: bold; color: @blue;
			&:hover{border-color: @blue * 0.3;}
		}
		input{display: none;}
		.icon{
			display: flex; align-items: center; justify-content: center; width: 18px; height: 20px; margin-left: 10px; position: relative;
			&:before{.icon-upload2; font: 17px/1 @fonti; color: @blue; font-weight: bold; position: absolute; top: 1px;}
		}
	}
	.sp-files{margin-top: 15px;}
	.sp-file{
		display: flex; align-items: center; width: 100%; min-height: 42px; margin: 0 0 -1px; padding: 11px 15px; background: @white; border: 1px solid @borderColor; font-size: 12px; font-weight: bold; position: relative; .transition(all);
		&:hover{border-color: @blue; z-index: 11;}

		.title{
			flex-grow: 1; padding-left: 28px; position: relative;
			&:before{.icon-upload2; font: 18px/1 @fonti; color: @blue; position: absolute; top: -1px; left: 0;}
		}
		.delete{
			width: 20px; height: 16px; flex-shrink: 0; position: relative; cursor: pointer;
			&:before{.icon-trash; font: 16px/1 @fonti; color: @textColor; position: absolute; .transition(color);}

			&:hover:before{color: @red;}
		}
	}
	.sp-btn-remove{
		display: flex; align-items: center; padding-left: 16px; font-size: 12px; font-weight: normal; text-decoration: underline; text-underline-offset: 2px; position: relative; cursor: pointer;
		&:before{.icon-x; font: 13px/1 @fonti; color: @red; position: absolute; top: 2px; left: 0;}
		&:hover{text-decoration: none;}
	}

	//reservation
	.reservation-info{
		width: 100%; margin-bottom: 25px; padding: 20px 15px; background: #F8F9FA; border: 1px solid #DBE3EA; border-radius: 2px; font-size: 14px;
		.name, .mail{padding-left: 30px; position: relative;}
		.name{
			margin-bottom: 8px; font-weight: bold;
			&:before{.icon-user-empty; font: 19px/1 @fonti; color: @blue; position: absolute; top: -1px; left: 1px;}
		}
		.mail:before{.icon-mail; font: 13px/1 @fonti; color: @blue; position: absolute; top: 3px; left: 0;}
		.date{width: calc(~"100% - -30px"); margin: 20px 0 -20px -15px; padding: 15px; border-top: 1px solid @gray;}
	}

	//flyout bottom
	.flyout-bottom {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 620px;
		padding: 25px 80px;
		background: @white;
		border-top: 1px solid @borderColor;
		position: fixed;
		right: 0;
		bottom: 0;
		z-index: 11;
		button,
		.btn {
			width: calc(~'50% - 5px');
			min-height: 50px;
			font-size: 14px;
			line-height: 19px;
			letter-spacing: -0.17px;
			cursor: pointer;
			&.disabled {
				pointer-events: none;
				box-shadow: none;
				& > span {
					opacity: 0.5;
				}
			}
		}
		.btn-white {
			background: #f2f5f8;
		}
		.btn.full {
			width: 100%;
		}

		@media (max-width: @m) {
			width: 100%;
			padding: 0;
			border: none;
			box-shadow: 0 0 20px 0 rgba(0, 34, 67, 0.08);
			button,
			.btn {
				width: 50%;
				border-radius: 0;
			}
			.btn-white {
				border: none;
				border-top: 1px solid @borderColor;
			}
		}
	}
</style>
