<template>
	<div class="card-code">
		<div href="#" class="card-code-btn card-code-scan"></div>
		<div class="card-code-btn card-code-confirm"></div>
		<input class="cart-code card-code-input" name="pa_cart_code" id="id_cart_hash" type="text" :placeholder="store.labels.pa_enter_cart_code" />
	</div>
</template>

<script setup>
	import { useStore } from '@/stores'

	const store = useStore()
</script>

<style lang="less" scoped>
	.card-code{
		width: 50%; flex-grow: 1; position: relative;
		input{
			width: calc(~"100% - 1px"); height: 54px; padding: 0 90px 0 20px; border: none; .placeholder(@textColor, @borderColor); .transition(padding);
			&.field_error_input{outline: 1px solid @red;}
		}
	}
	.card-code-btn{width: 30px; height: 30px; border-radius: @borderRadius; position: absolute; top: 13px; display: flex; align-items: center; justify-content: center; cursor: pointer; .transition(all); z-index: 1;}
	.card-code-scan{
		background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); right: 10px;
		&:before{.icon-face-scan; font: 16px/16px @fonti; color: @white; z-index: 1;}
	}
	.card-code-confirm{
		display: none; background: linear-gradient(225deg, #01AABA 0%, #0092A0 100%); right: 10px;
		&:before{.icon-check; font: 11px/11px @fonti; color: @white; z-index: 1;}
	}
</style>