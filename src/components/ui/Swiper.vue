<template>
	<div class="swiper-container">
		<div class="swiper" ref="swiperContainer">
			<div class="swiper-wrapper">
				<slot />
			</div>
		</div>
		<slot name="navigation">
			<div class="swiper-navigation" :class="[props.name ? `${props.name}-swiper-navigation` : '', navigation?.wrapperClass, {'swiper-navigation-lock': totalSlides <= swiper?.slidesPerViewDynamic()}]" v-if="navigation?.enabled">
				<div ref="navPrev" class="swiper-button swiper-button-prev" :class="[props.name ? `${props.name}-swiper-button-prev` : '', navigation?.prevElClass]">
					<svg width="133" height="241" viewBox="0 0 133 241" fill="black" xmlns="http://www.w3.org/2000/svg">
						<path d="M3.63348 129.006L111.93 237.267C116.682 242.007 124.381 242.007 129.145 237.267C133.897 232.527 133.897 224.828 129.145 220.088L29.4385 120.417L129.133 20.746C133.885 16.006 133.885 8.307 129.133 3.555C124.381 -1.185 116.67 -1.185 111.918 3.555L3.62147 111.816C-1.05853 116.507 -1.05852 124.327 3.63348 129.006Z"/>
					</svg>
				</div>
				<div ref="navNext" class="swiper-button swiper-button-next" :class="[props.name ? `${props.name}-swiper-button-next` : '', navigation?.nextElClass]">
					<svg width="133" height="241" viewBox="0 0 133 241" fill="black" xmlns="http://www.w3.org/2000/svg">
						<path d="M129.189 111.816L20.892 3.555C16.14 -1.185 8.44098 -1.185 3.67698 3.555C-1.07502 8.295 -1.07502 15.994 3.67698 20.734L103.384 120.405L3.68898 220.076C-1.06302 224.816 -1.06302 232.515 3.68898 237.267C8.44098 242.007 16.152 242.007 20.904 237.267L129.201 129.006C133.881 124.315 133.881 116.495 129.189 111.816Z"/>
					</svg>
				</div>
			</div>
		</slot>
		<slot name="pagination">
			<div ref="paginationEl" class="swiper-pagination" :class="[props.name ? `${props.name}-swiper-pagination` : '', pagination?.wrapperClass]" v-if="pagination?.enabled" />
		</slot>
	</div>
</template>

<script setup>
	import {ref, onMounted, onUpdated, onBeforeUnmount, computed, useAttrs, defineEmits} from 'vue';
	import Swiper from 'swiper';
	import {Navigation, Pagination, EffectFade, Autoplay, Thumbs, Zoom, Keyboard} from 'swiper/modules';
	import 'swiper/css';
	import 'swiper/css/effect-fade';
	import 'swiper/css/autoplay';
	import 'swiper/css/thumbs';

	const attrs = useAttrs();
	const props = defineProps({
		name: String,
		options: Object,
		totalItems: Number,
		thumbsSwiper: Object,
		initTimeout: {
			type: Number,
			default: 100,
		},
	});

	const totalSlides = computed(() => (props.totalItems ? props.totalItems : swiper.value?.slides?.length));
	const swiperContainer = ref(null);
	const swiper = ref(null);
	const emit = defineEmits(['init']);

	// set default navigation options and extend with user options
	const navPrev = ref(null);
	const navNext = ref(null);
	const navigation = ref({
		enabled: true,
		nextEl: navNext,
		prevEl: navPrev,
		...props.options?.navigation,
	});

	// set default pagination options and extend with user options
	const paginationEl = ref(null);
	const pagination = ref({
		enabled: false,
		el: paginationEl,
		...props.options?.pagination,
	});

	let initTimeout;
	onMounted(() => {
		if (initTimeout) clearTimeout(initTimeout);
		initTimeout = setTimeout(() => {
			const swiperOptions = {
				modules: [Navigation, Pagination, EffectFade, Autoplay, Thumbs, Zoom, Keyboard],
				resizeObserver: false,
				...props.options,
				navigation: navigation.value,
				pagination: pagination.value,
			};
			swiper.value = new Swiper(swiperContainer.value, swiperOptions);
			emit('init', swiper.value);
		}, props.initTimeout);
	});

	onUpdated(() => {
		if (!swiper.value) return;

		// init thumbs connection once thumbs swiper is ready
		if (props.thumbsSwiper && !props.thumbsSwiper.destroyed && swiper.value.thumbs) {
			swiper.value.thumbs.swiper = props.thumbsSwiper;
			swiper.value.thumbs.init();
			swiper.value.thumbs.update(true);
		}
	});

	onBeforeUnmount(() => {
		if (swiper.value) {
			swiper.value.destroy(true, true);
		}
	});
</script>
