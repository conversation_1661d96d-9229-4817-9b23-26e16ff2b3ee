<template>
	<Teleport to="body">
		<Transition name="fade">
			<div class="modal-body" :class="[props.class, {'modal-gallery': props.mode == 'gallery'}]" v-if="openValue">
				<div class="modal-content" :class="{'special': mode == 'energy'}">
					<div class="modal-toolbar">
						<span v-if="mode != 'updateCartView'" title="Close" @click="closeModal" class="fancybox-item fancybox-close modal-close"></span>
						<div v-if="props.mode == 'gallery'" class="toolbar-item toolbar-zoom-in" :class="{'disabled': disabledZoomIn}" @click="zoom('in')">
							<svg fill="#000000" width="21px" height="21px" viewBox="0 0 21 21">
								<g transform="translate(-1860.000000, -99.000000)" fill-rule="nonzero">
									<g transform="translate(1840.000000, 20.000000)">
										<g transform="translate(0.000000, 59.000000)">
											<g transform="translate(20.000000, 20.000000)">
												<path
													d="M20.7596895,19.5993574 L14.8349004,13.6745684 C17.5343027,10.3902422 17.3506348,5.51475586 14.2831992,2.44736133 C11.0200371,-0.815800781 5.71052344,-0.815800781 2.44736133,2.44736133 C-0.815800781,5.71052344 -0.815800781,11.0200371 2.44736133,14.2831992 C5.51393555,17.3497734 10.3888066,17.5354512 13.6745684,14.8349004 L19.5993574,20.7596895 C19.9197715,21.0801035 20.4392344,21.0801035 20.7596484,20.7596895 C21.0801035,20.4392344 21.0801035,19.9197715 20.7596895,19.5993574 Z M13.1229082,13.1228672 C10.4995488,15.7462266 6.23105273,15.7461855 3.60769336,13.1228672 C0.984333984,10.4995078 0.984333984,6.23101172 3.60769336,3.60765234 C6.2309707,0.984416016 10.4994668,0.984210937 13.1229082,3.60765234 C15.7462676,6.23101172 15.7462676,10.4995078 13.1229082,13.1228672 Z"></path>
												<path
													d="M12.3824941,7.5447832 L9.18573633,7.5447832 L9.18573633,4.34802539 C9.18573633,3.89488477 8.81840039,3.52754883 8.36525977,3.52754883 C7.91211914,3.52754883 7.5447832,3.89488477 7.5447832,4.34802539 L7.5447832,7.5447832 L4.34802539,7.5447832 C3.89488477,7.5447832 3.52754883,7.91211914 3.52754883,8.36525977 C3.52754883,8.81840039 3.89488477,9.18573633 4.34802539,9.18573633 L7.5447832,9.18573633 L7.5447832,12.3824941 C7.5447832,12.8356348 7.91211914,13.2029707 8.36525977,13.2029707 C8.81840039,13.2029707 9.18573633,12.8356348 9.18573633,12.3824941 L9.18573633,9.18573633 L12.3824941,9.18573633 C12.8356348,9.18573633 13.2029707,8.81840039 13.2029707,8.36525977 C13.2029707,7.91211914 12.8356348,7.5447832 12.3824941,7.5447832 Z"></path>
											</g>
										</g>
									</g>
								</g>
							</svg>
						</div>
						<div v-if="props.mode == 'gallery'" class="toolbar-item toolbar-zoom-out" :class="{'disabled': disabledZoomOut}" @click="zoom('out')">
							<svg fill="#000000" width="21px" height="21px" viewBox="0 0 21 21">
								<g transform="translate(-1860.000000, -158.000000)" fill-rule="nonzero">
									<g transform="translate(1840.000000, 20.000000)">
										<g transform="translate(0.000000, 118.000000)">
											<g transform="translate(20.000000, 20.000000)">
												<path
													d="M20.7596895,19.5993574 L14.8349004,13.6745684 C17.5343027,10.3902422 17.3506348,5.51475586 14.2831992,2.44736133 C11.0200371,-0.815800781 5.71052344,-0.815800781 2.44736133,2.44736133 C-0.815800781,5.71052344 -0.815800781,11.0200371 2.44736133,14.2831992 C5.51393555,17.3497734 10.3888066,17.5354512 13.6745684,14.8349004 L19.5993574,20.7596895 C19.9197715,21.0801035 20.4392344,21.0801035 20.7596484,20.7596895 C21.0801035,20.4392344 21.0801035,19.9197715 20.7596895,19.5993574 Z M13.1229082,13.1228672 C10.4995488,15.7462266 6.23105273,15.7461855 3.60769336,13.1228672 C0.984333984,10.4995078 0.984333984,6.23101172 3.60769336,3.60765234 C6.2309707,0.984416016 10.4994668,0.984210937 13.1229082,3.60765234 C15.7462676,6.23101172 15.7462676,10.4995078 13.1229082,13.1228672 Z"></path>
												<path
													d="M12.3824941,7.5447832 L9.18573633,7.5447832 L7.5447832,7.5447832 L4.34802539,7.5447832 C3.89488477,7.5447832 3.52754883,7.91211914 3.52754883,8.36525977 C3.52754883,8.81840039 3.89488477,9.18573633 4.34802539,9.18573633 L7.5447832,9.18573633 L9.18573633,9.18573633 L12.3824941,9.18573633 C12.8356348,9.18573633 13.2029707,8.81840039 13.2029707,8.36525977 C13.2029707,7.91211914 12.8356348,7.5447832 12.3824941,7.5447832 Z"></path>
											</g>
										</g>
									</g>
								</g>
							</svg>
						</div>
					</div>

					<div class="modal-cnt">
						<template v-if="mode == 'scanner'">
							<div class="wqr-scan-camera">
								<div class="wqr-scan-info scan-qrcode" v-html="title" />
								<div class="wqr-scan-camera-box">
									<div class="camera-loading" v-if="camLoading">{{ store.labels.waiting_messages }}</div>
									<StreamBarcodeReader v-if="openValue" @decode="onDecode" @loaded="camLoading = 0" />
								</div>
							</div>
						</template>

						<template v-if="mode == 'energy'">
							<div class="cd-energy-container">
								<div class="cd-energy-container-title">{{ store.labels.energy_title }}</div>
								<div class="cd-energy-container-img">
									<Image v-if="item.energy_image_upload_path" :src="item.energy_image_upload_path" :width="300" :height="585" default="/media/images/no-image-100.jpg" :alt="store.labels.energy_title" />
								</div>
							</div>
						</template>
						<slot />
					</div>

					<template v-if="mode == 'gallery' && props.images">
						<div class="modal-gallery-body">
							<div class="modal-gallery-main" :class="{'single': props.images?.length <= 1}" @wheel="onWheel">
								<Swiper
									:options="{
										initialSlide: store.modal.index || 0,
										zoom: {maxRatio: 3, toggle: false},
										effect: 'fade',
										fadeEffect: {crossFade: true},
										keyboard: true,
										on: {
											zoomChange: onZoom,
											slideChange: onSlideChange,
										},
									}"
									@init="setMainSwiper">
									<SwiperSlide v-for="(image, index) in props.images" :key="index">
										<div class="swiper-zoom-container">
											<Image v-if="image?.url" :src="image.url" default="/media/images/no-image-490.jpg" :alt="title" />
											<img v-else :src="image" alt="" loading="lazy">
										</div>
									</SwiperSlide>
								</Swiper>
							</div>
							<div class="modal-gallery-thumbs" v-if="props.images?.length > 1">
								<div class="modal-gallery-thumbs-items">
									<div v-for="(image, index) in props.images" :key="index" class="modal-gallery-thumbs-item" :class="{'active': index == activeSlideIndex}" @click="mainSwiper.slideTo(index)">
										<Image v-if="image?.url" :src="image.url" default="/media/images/no-image-490.jpg" :alt="title" />
										<img v-else :src="image" alt="" loading="lazy">
									</div>
								</div>
							</div>
						</div>
					</template>

					<div class="w-cart-remove-tooltip-btns" v-if="props.confirmButtons">
						<a href="javascript:void(0);" @click="confirm" class="btn btn-modal btn-cancel">DA</a>
						<a href="javascript:void(0);" @click="closeModal" class="btn btn-modal btn-remove">NE</a>
					</div>
				</div>
				<div class="modal-overlay" @click="closeModal"></div>
			</div>
		</Transition>
	</Teleport>
</template>

<script setup>
	import {StreamBarcodeReader} from 'vue-barcode-reader';
	import {ref, watch, computed, onMounted, onBeforeUnmount} from 'vue';
	import {useStore} from '@/stores';
	import Image from '@/components/Image.vue';
	import Swiper from '@/components/ui/Swiper.vue';
	import SwiperSlide from '@/components/ui/SwiperSlide.vue';

	const props = defineProps({
		openValue: [Boolean, Number, String],
		title: String,
		mode: String,
		item: Object,
		images: Array,
		class: String,
		confirmButtons: {
			type: Boolean,
			default: false
		}
	})
	const emit = defineEmits(['confirm', 'close', 'scan']);

	const store = useStore();
	const camLoading = ref(1);

	watch(
		() => props.openValue,
		newValue => {
			(props.mode == 'gallery' && newValue) ? document.body.classList.add('gallery-modal-active') : document.body.classList.remove('gallery-modal-active');;
			if (!newValue) camLoading.value = 1;
		}
	);

	function onDecode(payload) {
		emit('scan', payload);
	}

	function confirm() {
		emit('confirm');
	}

	function closeModal() {
		emit('close');
		store.modal = {}
	}

	// main swiper instance
	const mainSwiper = ref(null);
	const setMainSwiper = swiper => {
		mainSwiper.value = swiper;
	};

	// image zoom
	function isMobile() {
		return /Mobi|Android|iPhone|iPad|iPod/i.test(navigator.userAgent);
	}

	// zoom buttons
	const zoomLevel = ref(1);
	const zoomIncrement = props.zoom?.increment || 1;
	const maxZoom = ref(props.zoom?.max || 3);
	function zoom(mode) {
		console.log(mainSwiper.value);
		
		if (!mainSwiper.value) return;
		if (mode == 'in' && zoomLevel.value < maxZoom.value) {
			mainSwiper.value.zoom.in(zoomLevel.value + zoomIncrement);
		}
		if (mode == 'out' && zoomLevel.value > 1) {
			mainSwiper.value.zoom.in(zoomLevel.value - zoomIncrement);
		}
	}

	// update current zoom level on zoom change
	function onZoom(swiper, scale, imageEl, slideEl) {
		zoomLevel.value = scale;
		if (isMobile()) {
			mainSwiper.value.allowTouchMove = scale <= 1 ? true : false;
		}
	}

	// zoom on wheel scroll
	function onWheel(e) {
		e.deltaY > 0 ? zoom('out') : zoom('in');
	}

	const activeSlideIndex = ref(store.modal?.index || 0);
	function onSlideChange(e) {
		activeSlideIndex.value = e.activeIndex;
	}	

	const disabledZoomIn = computed(() => zoomLevel.value >= maxZoom.value);
	const disabledZoomOut = computed(() => zoomLevel.value <= 1);

	// close modal window when Esc key is pressed
	function closeOnEscape(evt) {
		if (evt.keyCode === 27) {
			closeModal();
		}
	}

	onMounted(() => {
		window.addEventListener('keyup', closeOnEscape);
	});

	onBeforeUnmount(() => {
		window.removeEventListener('keyup', closeOnEscape);
	})
</script>

<style lang="less">
	.gallery-modal-active { overflow: hidden; }
</style>

<style lang="less" scoped>
	.modal-overlay { background: rgba(0, 24, 47, 0.6); position: absolute; top: 0; right: 0; bottom: 0; left: 0; }
	.modal-body { position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 99999; display: flex; align-items: center; justify-content: center; }
	.modal-content {
		background: #fff; width: 550px; max-width: 580px; border-radius: 2px; box-shadow: 0 20px 30px #0022434d; position: relative; z-index: 1; word-break: break-word;
		&.special { display: flex; flex-direction: column; }

		@media (max-width: @m) {
			width: 80%;
		}
	}
	.modal-cnt { overflow: auto; max-height: 85vh; }
	.modal-close { position: absolute; top: -20px; right: -20px; cursor: pointer; z-index: 1; }
	.btn-modal { font-size: 16px; border-radius: 2px; }
	.btn-remove { color: #fff; border: 0; }

	.fade-enter-active,
	.fade-leave-active { transition: opacity 0.3s ease; }

	.fade-enter-from,
	.fade-leave-to { opacity: 0; }

	.cd-energy-container {
		padding: 25px; text-align: center; overflow-y: auto; position: relative;

		@media (max-width: @m) {
			font-size: 15px;
		}
	}
	.cd-energy-container-title {
		display: block; margin-bottom: 20px; font-size: 22px; letter-spacing: -0.3px; font-weight: 600; text-align: center;

		@media (max-width: @m) {
			margin-bottom: 15px;
			font-size: 16px;
		}
	}
	.cd-energy-container-img {
		display: flex; justify-content: center;
		img { width: auto; height: auto; max-width: 100%; max-height: 100%; display: block; }
	}
	.modal-content-padding{
		.modal-content{
			padding: 50px 50px 45px;
			@media (max-width: @m){padding: 30px 35px 30px;}
		}
	}

	.toolbar-item{
		width: 50px; height: 50px; border-radius: 100px; box-shadow: 5px 8px 20px 0 rgba(3,32,62,0.2); display: flex; align-items: center; justify-content: center; align-items: center; background: #fff; cursor: pointer;
		&.disabled{opacity: .5;}
	}
	:deep(.swiper-container){
		user-select: none; -webkit-user-select: none; -ms-user-select: none; height: 100%;
		.swiper{height: 100%;}
		.swiper-navigation{position: fixed; top: 0; left: 0; right: 0; z-index: 100;}
		.swiper-button{
			position: fixed; top: 50vh; width: 45px; height: 45px; display: flex; align-items: center; justify-content: center; cursor: pointer;
			svg{height: 90%; .transition(fill);}
			&.swiper-button-disabled{display: none;}
			&:hover{
				svg{fill: @blue;}
			}
		}
		.swiper-slide{display: flex; align-items: center; justify-content: center;}
		.swiper-button-prev{left: 20%;}
		.swiper-button-next{right: 10%;}
	}
	.modal-gallery-main{
		position: fixed; top: 0; right: 0; bottom: 0; left: 10%;
		img{max-height: 90vh; max-width: 90vw; width: auto; display: block;}
		&.single{left: 0;}
	}
	.modal-gallery-thumbs{
		width: 10%; flex-grow: 0; flex-shrink: 0; background: #fff; box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.1); overflow: auto; padding: 20px; position: fixed; top: 0; left: 0; bottom: 0;
		img{display: block; width: auto; height: auto; max-height: 100%;}
	}
	.modal-gallery-thumbs-item{
		margin-bottom: 10px; cursor: pointer; border: 1px solid transparent; display: flex; align-items: center; justify-content: center; aspect-ratio: 1;
		&.active{border-color: #0078BA;}
	}
	.modal-gallery{
		.modal-content{width: 100%; height: 100%; max-width: none;}
		.modal-toolbar{position: fixed; top: 15px; right: 15px; z-index: 1000; display: flex; flex-direction: column; gap: 10px;}
		
		.modal-close{
			position: relative; top: auto; right: auto;
		}
	}
	button{padding: 0;}
</style>

<style lang="less">
	.btn:disabled {
		opacity: 0.4;
	}
	.camera-loading {
		position: absolute;
		z-index: 1;
		color: #fff;
	}
	.wqr-scan-camera-box {
		overflow: hidden;
		width: 100% !important;
		height: 200px;
	}
	.overlay-element {
		display: none;
	}
	.laser {
		width: 100% !important;
		margin: 0 !important;
	}
	video {
		display: block;
	}
	.wqr-scan-info .counter {
		color: @blue;
		padding-left: 4px;
	}
</style>
