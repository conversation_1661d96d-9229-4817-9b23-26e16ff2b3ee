<template>
	<div class="main-col sidebar">
		<div class="sidebar-header">
			<router-link to="/" class="logo"></router-link>
			<div class="sidebar-label">{{ store.labels.sales_assistant }}</div>
		</div>

		<ul v-if="menu" class="sidebar-nav">
			<li
				v-for="menuItem in menu"
				:key="menuItem.id"
				:class="[menuItem.anchor_text, {'active-submenu': menuItem.items ? menuItem.submenuActive : false}]"
				@mouseover="menuItem.items ? (menuItem.submenuActive = true) : (menuItem.submenuActive = false)"
				@mouseout="menuItem.submenuActive = false">
				<router-link :to="menuItem.url" :class="{'active': route.fullPath == menuItem.url}" :title="menuItem.anchor_text">
					<span v-if="menuItem.image" class="sidebar-nav-icon">
						<Image :src="menuItem.image_upload_path" width="22" height="22" />
					</span>
					<span>{{ menuItem.title }}</span>
				</router-link>
				<ul v-if="menuItem.items" class="sidebar-submenu">
					<li v-for="menuSubItem in menuItem.items" :key="menuSubItem.id">
						<router-link :to="menuSubItem.url">{{ menuSubItem.title }}</router-link>
					</li>
				</ul>
			</li>
			<ShoppingCart />
			<li class="sidebar-settings-m" id="menuSettings">
				<a @click="menuSettings()" href="javascript:void(0);">{{ store.labels.pa_settings }}<span class="sidebar-nav-icon"></span></a>
				<ul class="sidebar-settings-list">
					<li v-if="authStore.user">
						<strong>{{ authStore.user.first_name }} {{ authStore.user.last_name }}</strong>
					</li>
					<li v-if="selectedLocation">
						<a href="javascript:void(0);" @click="router.push({name: 'changeStore'})">{{ selectedLocation.title }}</a>
					</li>
					<li>
						<a href="javascript:void(0);" @click="authStore.logout()">{{ store.labels.logout }}</a>
					</li>
				</ul>
			</li>
		</ul>

		<div v-if="alertMessage" class="sidebar-alert-message">{{ store.labels.pa_alert_message }}</div>

		<div class="sidebar-footer">
			<div class="sidebar-user" v-if="authStore.user">{{ authStore.user?.email }}</div>
			<ul class="sidebar-footer-nav">
				<li class="sidebar-location" v-if="selectedLocation">
					<a href="javascript:void(0);" @click="router.push({name: 'changeStore'})">{{ selectedLocation.title }}</a>
				</li>
				<li class="sidebar-logout">
					<a href="javascript:void(0);" @click="authStore.logout()">{{ store.labels.logout }}</a>
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup>
	import {computed, ref, onMounted, onUnmounted} from 'vue';
	import {useRoute, useRouter} from 'vue-router';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useStore} from '@/stores';
	import {useAuthStore} from '@/stores/auth';
	import {useConfig} from '@/composables/useConfig';
	import Image from '@/components/Image.vue';
	import ShoppingCart from '@/components/webshop/widget/ShoppingCart.vue';

	const store = useStore();
	const authStore = useAuthStore();
	const ep = useEndpoints();
	const route = useRoute();
	const router = useRouter();
	const menu = computed(() => store.getMenu('pa_main'));
	const alertMessage = ref(false);
	const {appVersion} = useConfig();

	const selectedLocation = computed(() => {
		return authStore.locations.find(el => el.selected);
	});

	function menuSettings() {
		document.body.classList.toggle('setting-nav-active');
		let menuSettings = document.getElementById('menuSettings');
		menuSettings.classList.toggle('active');
	}

	function handleStorageChange(event) {
		if (event && event.key === 'creation') {
			// Update the alertMessage variable based on the new value in local storage
			alertMessage.value = JSON.parse(event.newValue);
		}
	}

	async function checkOrderCreationInProgress() {
		await useFetch({
			url: ep.endpoints.value._get_hapi_webshop_order_creation_in_progress,
			method: 'GET',
		}).then(async res => {
			// If the response is not true, recursively call the function with a delay
			if (res.data.order_creation_in_progress == false) {
				alertMessage.value = false;
				store.creation = false;
				localStorage.setItem('creation', JSON.stringify(store.creation));
			} else {
				setTimeout(checkOrderCreationInProgress, 3000);
			}
		});
	}

	onMounted(async () => {
		// Listen for changes to the 'creation' key in local storage
		window.addEventListener('storage', handleStorageChange);

		// Initial check for the value in local storage
		const creationValue = localStorage.getItem('creation');
		if (creationValue) {
			alertMessage.value = JSON.parse(creationValue);

			// If creationValue is true, start checking order creation in progress
			if (alertMessage.value === true) {
				await checkOrderCreationInProgress();
			}
		}
	});

	onUnmounted(() => {
		// Remove the event listener when the component is destroyed
		window.removeEventListener('storage', handleStorageChange);
	});
</script>

<style scoped>
.app-version{font-size: 11px; line-height: 1; opacity: .5;}
</style>