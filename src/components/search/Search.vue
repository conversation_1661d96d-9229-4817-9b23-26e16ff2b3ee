<template>
	<div class="sw">
		<a class="sw-toggle"></a>
		<div class="sw-form">
			<input class="sw-input" :class="{loading: loading}" autocomplete="off" @blur="blur" @keyup="isTyping($event)" v-model="searchTerm" type="text" :placeholder="store.labels.pa_enter_search_term" />
			<div v-if="searchTerm" class="sw-clear" @click="clearSearchTerm()"></div>
			<div class="sw-btns">
				<button class="sw-btn sw-search-btn" :disabled="!searchTerm || (searchTerm && searchTerm.length < 2)" type="submit" @click="goToSearchResults()">{{ store.labels.search_button }}</button>
				<QuickSearch />
				<QuickService />
			</div>

			<div class="ac" v-if="searchTerm && items.length">
				<div class="ac-loading" v-if="loading" />
				<ul class="ac-items" v-if="items?.length">
					<li class="ac-item" v-for="(item, index) in items" :key="item.id" @click="setSelectedItem(index), navigate()" :class="{active: dirty && selectedIndex == index}" :data-index="index">
						<router-link :to="item.url_without_domain">
							<div class="ac-image"><Image loading="lazy" :data="item.main_image_upload_path_thumb" /></div>
							<div class="ac-cnt">
								<div class="ac-title">{{ item.title }}</div>
								<div class="ac-category" v-if="item.category_title">{{ item.category_title }}</div>
								<div class="ac-price" v-if="item.price_custom">
									<div class="ac-current-price">{{ formatCurrency(item.price_custom) }}</div>
								</div>
							</div>
							<div class="ac-add-to-cart">
								<div v-if="item?.is_available" class="ac-btn-addtocart" @click.stop.prevent="addToCart(item.shopping_cart_code)"></div>
								<div v-else class="ac-btn-addtocart ac-btn-details"></div>
							</div>
						</router-link>
					</li>
					<li class="ac-showall" v-if="totalItems > 5">
						<a @click="goToSearchResults()">{{ store.labels.show_all }} ({{ totalItems }})</a>
					</li>
				</ul>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {ref} from 'vue';
	import {useStore} from '@/stores';
	import {useCatalogStore} from '@/stores/catalog';
	import {useWebshopStore} from '@/stores/webshop';
	import {useImages} from '@/composables/useImages';
	import {useRoute, useRouter} from 'vue-router';
	import {useEventBus} from '@/composables/useEventBus';
	import Image from '@/components/Image.vue';
	import QuickSearch from '@/components/search/QuickSearch.vue';
	import QuickService from '@/components/catalog/widget/QuickService.vue';
	import useHelpers from '@/composables/useHelpers';

	const store = useStore();
	const route = useRoute();
	const router = useRouter();
	const {generateThumbs} = useImages();
	const catalogStore = useCatalogStore();
	const webshopStore = useWebshopStore();
	const {formatCurrency} = useHelpers();
	const searchTerm = ref();
	const loading = ref(0);
	const items = ref([]);
	const totalItems = ref(0);
	const selectedIndex = ref(-1);
	const selectedItem = ref({});
	const dirty = ref(0);

	let typingTimer;
	async function isTyping(event) {
		clearTimeout(typingTimer);

		if (event.key == 'ArrowDown') {
			dirty.value = 1;
			const totalItems = items.value.length - 1;
			selectedIndex.value = selectedIndex.value >= totalItems ? totalItems : selectedIndex.value + 1;
			setSelectedItem();
			return true;
		}

		if (event.key == 'ArrowUp') {
			dirty.value = 1;
			selectedIndex.value = selectedIndex.value <= 0 ? 0 : selectedIndex.value - 1;
			setSelectedItem();
			return true;
		}

		if (event.key == 'Escape') {
			blur();
			return true;
		}

		if (event.key == 'Enter') {
			if (dirty.value) {
				navigate();
			} else {
				goToSearchResults();
			}
			return true;
		}

		typingTimer = setTimeout(search, 300);
	}

	function navigate() {
		items.value = [];
		dirty.value = 0;
		if (selectedItem.value) {
			return router.push(selectedItem.value.url_without_domain);
		}
	}

	const {emit} = useEventBus();
	function goToSearchResults() {
		if (searchTerm.value && searchTerm.value.length > 1) {
			reset();
			router.push({
				name: 'search',
				query: {search_q: searchTerm.value.trim()},
			});
			emit('search');
		}
	}

	function setSelectedItem(index = selectedIndex.value) {
		selectedItem.value = items.value[index];
		searchTerm.value = selectedItem.value.title;
		//document.querySelector('[data-index="' + selectedIndex.value + '"]').scrollIntoView({block: 'nearest', inline: 'nearest'});
	}

	async function search() {
		if (searchTerm.value && searchTerm.value.length > 1) {
			loading.value = 1;
			await catalogStore
				.fetchProducts({
					search_q: searchTerm.value.trim(),
					limit: 5,
					mode: 'search',
				})
				.then(res => {
					reset();
					items.value = res.data.items;
					totalItems.value = res.data.meta_data?.items_total && res.data.meta_data.items_total;
				});

			// generate thumbnails for search results
			await generateThumbs({
				data: items.value,
				preset: 'searchEntry',
			});
		}
	}

	function clearSearchTerm() {
		searchTerm.value = '';
	}

	function reset() {
		dirty.value = 0;
		loading.value = 0;
		items.value = [];
		totalItems.value = 0;
		selectedIndex.value = -1;
		selectedItem.value = {};
	}

	function blur() {
		setTimeout(() => {
			reset();
		}, 100);
	}

	//add to cart
	async function addToCart(value) {
		store.loading = 1;
		const cartCode = value;

		await webshopStore
			.addProduct({
				shopping_cart_code: cartCode,
			})
			.then(res => {
				store.loading = 0;
				router.push({name: 'shoppingCart'});
			});
	}
</script>

<style lang="less">
	.sw-btn {
		min-height: 0; height: 100%; border-radius: 2px!important; z-index: 1; aspect-ratio: 1;
		&:disabled { cursor: auto; }

		@media (max-width: @m) {
			background: transparent; font-size: 0;
			&:before { .icon-search; font: 16px/16px @fonti; color: @blue; position: absolute; right: 15px; }
		}
	}
</style>

<style lang="less" scoped>
	.btn { font-size: 0; border-radius: 0; border-inline: 0; }
	.sw { width: auto; flex-grow: 1; position: relative; display: inline-block; }
	.sw-form {
		position: relative;
		&:before { .icon-search; font: 18px/18px @fonti; color: @blue; position: absolute; left: 7.6%; top: 21px; }
		@media (max-width: @t) {
			&:before { left: 6%; }
		}
		@media (max-width: @m) {
			&:before { content: none; }
		}
	}
	.sw-input {
		width: 100%; height: 65px; border: 1px solid @borderColor; border-top: none; border-left: none; padding: 0 350px 0 10%; background: @white; font-size: 16px; line-height: 22px; font-weight: bold; letter-spacing: -0.3px; .placeholder(@textColor, @borderColor); text-align: left;
		&:hover,
		&:focus { border-color: @borderColor; }
		&.loading { background: #fff url(/media/images/loader.svg) no-repeat right 300px center; background-size: 30px auto; }
		@media (max-width: @m) {
			height: 47px; background: #f5f7f9; border-right: none; padding: 0 220px 0 15px; font-size: 14px; line-height: 19px; font-weight: 600;
			&.loading { background: #fff url(/media/images/loader.svg) no-repeat right 200px center; background-size: 25px auto; }
		}
	}
	.sw-clear {
		display: flex; align-items: center; justify-content: center; width: 31px; height: 31px; flex-shrink: 0; position: absolute; top: 17px; right: 260px; z-index: 1; cursor: pointer;
		&:before { display: flex; align-items: center; justify-content: center; width: 100%; height: 100%; background: #d7e4ef; border-radius: 100%; .icon-x; font: 15px/1 @fonti; color: @white; .transition(background); }
		@media (min-width: @t) {
			&:hover:before { background: @red; }
		}

		@media (max-width: @m) {
			width: 24px; height: 24px; top: 12px; right: 160px;
			&:before { font-size: 12px; }
		}
	}
	.sw-btns{
		display: flex; position: absolute; top: 5px; right: 5px; bottom: 5px; gap: 5px;
		@media (max-width: @m){top: 0; right: 0; bottom: 0;}
	}
	.sw-search-btn{
		min-width: 120px;
		@media (max-width: @m){min-width: 0;}
	}
	.sw-toggle { display: none; }

	/*------- autocomplete -------*/
	.ac {
		position: absolute;
		top: 100%;
		left: 0;
		right: 0;
		background: #fff;
		z-index: 100;
		box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
		font-size: 13px;
	}
	.ac-items {
		overflow: auto;
		max-height: 320px;
	}
	.ac-item {
		border-bottom: 1px solid @borderColor;
		a {
			display: flex;
			padding: 10px 0 10px 7%;
		}
		&:hover,
		&.active {
			background: #f8f9fa;
			.ac-title {
				color: @blue;
			}
		}
	}
	.ac-image {
		margin-right: 20px;
		width: 60px;
		display: flex;
		justify-content: center;
		align-items: center;
		flex-shrink: 0;
		img {
			max-width: 100%;
			max-height: 60px;
			height: auto;
			width: auto;
		}
	}
	.ac-title{font-weight: bold;}
	.ac-category{margin-top: 2px; font-size: 12px; color: @blue;}
	.ac-price{margin-top: 2px;}
	.ac-add-to-cart{
		display: flex; align-items: center; justify-content: flex-end; flex-grow: 1; margin: 10px 30px;

		@media (max-width: @m){margin: 5px 15px;}
	}
	.ac-btn-addtocart{
		display: flex; align-items: center; justify-content: center; width: 40px; height: 40px; background: @green; flex-shrink: 0; margin-left: 5px; padding: 0; font-size: 0; line-height: 0; position: relative;
		&:before{.icon-cart-empty; font: 19px/19px @fonti; color: @white; position: absolute;}
		&:hover{opacity: 0.8;}

		@media (max-width: @m){
			width: 38px; height: 38px; margin-left: 0;
			&:before{font-size: 17px; line-height: 17px;}
		}
	}
	.ac-btn-details:before{.icon-info; font: 17px/17px @fonti;}
	.ac-loading {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		&:before {
			.pseudo(50px,50px);
			background: url(/media/images/loader.svg);
			background-size: contain;
		}
	}

	.sw-form,
	.w-cart-sw {
		.ui-menu {
			width: 100% !important;
			position: absolute;
			list-style: none;
			padding: 0;
			left: 0;
			top: 65px;
			border-right: 1px solid @borderColor;
			background: @white;
			z-index: 1111;

			@media (max-width: @m) {
				top: 47px;
				max-height: 250px;
				overflow: auto;
				box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
				border: none;
			}
		}
		.ui-menu-item {
			cursor: pointer;
			width: 100%;
			a {
				display: flex;
				align-items: center;
				font-size: 14px;
				color: @textColor;
				padding: 10px 25px;
				min-height: 70px;
				border-bottom: 1px solid @borderColor;
			}
			img {
				display: block;
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
			}

			@media (max-width: @m) {
				a {
					font-size: 12px;
					padding: 10px 15px;
					min-height: 55px;
				}
			}
		}
		.ac-showall {
			margin: -1px 0 -1px -1px;
			color: @white;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
			.transition(all);
			a {
				width: 100%;
				min-height: 65px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 15px;
				color: @white;
				background: linear-gradient(63.43deg, #0050a0 0, #0078b4 100%);
				border: none;
				padding: 5px 20px;
				z-index: 111;
				.transition(all);
				cursor: pointer;
				&:hover {
					box-shadow: 0 100px 10px rgba(0, 0, 0, 0.08) inset;
					color: @white;
				}
			}
			.ui-state-focus {
				box-shadow: 0 100px 10px rgba(0, 0, 0, 0.08) inset;
				color: @white;
			}

			@media (max-width: @m) {
				width: 100%;
				margin: -1px 0 0;
				a {
					min-height: 47px;
					font-size: 14px;
					&:hover {
						box-shadow: none;
					}
				}
			}
		}
		.ui-state-focus {
			background: @borderColor;
			position: relative;
			z-index: 1;
			.transition(all);
		}
		.search-image {
			width: 70px;
			height: 70px;
			float: left;
			line-height: 70px;
			margin-right: 15px;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;

			@media (max-width: @m) {
				width: 55px;
				height: 55px;
				line-height: 55px;
			}
		}
		.search-cnt {
			display: flex;
			flex-direction: column;
		}
		.search-title {
			color: @textColor;
			display: block;
		}
		.search-category {
			display: block;
			font-size: 12px;
			line-height: 15px;
			color: @blue;
			padding: 3px 0 5px;
		}
		.search-price {
			display: flex;
			align-items: center;
		}
		.search-old-price {
			font-size: 12px;
			line-height: 15px;
			color: @textColor;
			padding-right: 5px;
		}
		.search-current-price {
			font-weight: bold;
		}
	}
	/*------- /autocomplete -------*/
</style>
