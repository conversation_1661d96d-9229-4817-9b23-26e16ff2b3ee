<template>
	<div class="btn sw-btn sw-scan-btn fancybox_iframe" title="Izdelek" @click="catalogStore.productModalStatus = 'search'"></div>
</template>

<script setup>
	import {useCatalogStore} from '@/stores/catalog';
	const catalogStore = useCatalogStore();
</script>

<style lang="less" scoped>
	.sw-scan-btn {
		min-height: 0; height: 100%;
		&:before { .icon-scan; font: 20px/1 @fonti; color: @white; text-indent: 1px; z-index: 1; }
		@media (max-width: @m) {
			&:before { font-size: 16px; color: @blue; }
		}
	}
</style>
