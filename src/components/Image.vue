<template>
	<img :src="image.src" :width="image.width" :height="image.height" :alt="image.alt" :title="image.title" />
</template>

<script setup>
	import {useConfig} from '@/composables/useConfig';
	import {computed} from 'vue';
	const props = defineProps(['src', 'alt', 'title', 'width', 'height', 'data', 'loading', 'default']);
	const config = useConfig();

	const image = computed(() => {
		const defaultImage = props.default ? props.default : '/media/images/no-image.jpg';

		// default data
		let imageWidth = props.width;
		let imageHeight = props.height;
		let imageSrc = props.src;

		// if data object is provided
		if (props.data?.width) imageWidth = props.data.width;
		if (props.data?.height) imageHeight = props.data.height;
		if (props.data?.thumb) imageSrc = props.data.thumb;

		// no image
		if (!imageSrc) {
			imageSrc = defaultImage;
		} else {
			imageSrc = config.baseUrl + imageSrc;
		}

		return {
			src: imageSrc,
			width: imageWidth,
			height: imageHeight,
			title: props.title,
			alt: props.alt,
		};
	});
</script>
