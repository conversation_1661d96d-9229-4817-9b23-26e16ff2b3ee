<template>
	<article class="clear cp">
		<div class="cp-col1">
			<div class="cp-col1-top">
				<div class="cp-info-top">
					<div class="cp-info-top-left">
						<div class="cp-badges" v-if="item.badges?.length || (item.selected_price && item.selected_price == 'uau')">
							<div v-if="item.selected_price && item.selected_price == 'uau'" class="cp-badge uau">
								<span>{{ store.labels.uau_badge_title }}</span>
								<div class="cp-badge-tooltip cp-badge-tooltip-uau">{{ store.labels.uau_badge_title }}</div>
							</div>

							<template v-if="item.bades?.length">
								<span v-for="badgeSpecial in item.badges" :key="badgeSpecial">
									<div class="cp-badge cp-badge-new new" v-if="badgeSpecial.category == 4">
										<span v-if="item.priority_details">{{ item.priority_details.title }}</span>
										<div class="cp-badge-tooltip cp-badge-tooltip-new">{{ store.labels.pa_new_badge_tooltip }}</div>
									</div>

									<div class="cp-badge cp-badge-discount discount" v-if="badgeSpecial.category == 0">
										<span>-{{ item.discount }} %</span>
										<div class="cp-badge-tooltip cp-badge-tooltip-discount">{{ store.labels.pa_discount_badge_tooltip }}</div>
									</div>

									<div class="cp-badge cp-badge-gift gift" v-if="badgeSpecial.category == 3">
										<span v-if="badgeSpecial.label_title">-{{ badgeSpecial.label_title }} %</span>
										<div class="cp-badge-tooltip cp-badge-tooltip-gift">{{ badgeSpecial.label_title }}</div>
									</div>
								</span>
							</template>
						</div>
						<h2 class="cp-title">
							<router-link :to="item.url_without_domain">{{ item.title }}</router-link>
						</h2>

						<div class="cp-info">
							<div class="cp-code">
								<strong>{{ store.labels.id }}:</strong> {{ item.code }}
							</div>
							<div class="cp-category" v-if="item.category_title">
								<router-link :to="item.category_url_without_domain">{{ item.category_title }}</router-link>
							</div>
							<div v-if="item.logistic_class" class="cp-logistic">{{ item.logistic_class }}</div>
							<!---
							<div class="cp-seller" v-if="item.seller_title">
								<router-link :to="item.seller_url_without_domain"
									><span>{{ item.seller_title }}</span></router-link
								>
							</div>
							-->
							<div v-if="item.element_state_code && conditionLabel" class="cp-condition-wrapper">
								<div v-if="item.element_state_code && conditionLabel" class="cp-condition">
									<span>{{conditionLabel}}</span>
								</div>
							</div>
						</div>
					</div>

					<div class="cp-info-top-right">
						<router-link class="cp-no-image" :to="item.url_without_domain"><img src="/media/images/no-image-60.jpg" alt="" /></router-link>
						<div class="cp-available-qty cp-available-qty-m" :class="[availableQtyClass]">
							<template v-if="item.status == '5' && item.date_available_humanize">
								{{ item.date_available_humanize }} ({{ item.user_warehouse_available_qty }})
							</template>
							<template v-else>
								{{ item.user_warehouse_available_qty }} ({{ item.user_nearby_available_qty }})
								<template v-if="item.status != '9' && item.status2 != '4'">({{ parseFloat(item.available_qty_supplier).toFixed(0) }})</template>
							</template>
						</div>
					</div>
				</div>
			</div>

			<div class="cp-bottom">
				<div class="cp-bottom-left">
					<div v-if="item.price_custom > 0" class="cp-price" :class="{'uau-badge': item.selected_price && item.selected_price == 'uau'}">
						<template v-if="(item.type == 'advanced' || item.type == 'configurable') && item.basic_price_custom > item.price_custom">
							<div class="cp-current-price cp-variation-price">
								<span class="cp-old-price cp-price-label">{{ store.labels.price_variation }}</span>
								<span>{{ formatCurrency(item.price_custom) }}</span>
							</div>
						</template>
						<template v-else>
							<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom) && item.selected_price != 'promotion2'">
								<div class="cp-old-price line-through">{{ formatCurrency(item.basic_price_custom) }}</div>
								<div class="cp-current-price cp-discount-price red">
									(-{{ item.discount_percent_custom }}%) {{ formatCurrency(item.price_custom) }}
									<span class="cp-installments-price" v-if="installmentPrice" v-html="store.labels.pa_installments_price_text.replace('%PRICE%', formatCurrency(installmentPrice))" />
								</div>
							</template>
							<div class="cp-current-price" v-else>
								{{ formatCurrency(item.price_custom) }}
								<span class="cp-installments-price" v-if="installmentPrice" v-html="store.labels.pa_installments_price_text.replace('%PRICE%', formatCurrency(installmentPrice))" />
							</div>
							<div class="cp-discount-expire" v-if="item.discount_expire">
								<span v-if="item.discount_expire" v-html="store.labels.pa_discount_expire.replace('%d%', formatDate(item.discount_expire))" />
							</div>
							<div v-if="item.loyalty_price_custom" class="cp-current-price cp-loyalty-price">
								<span>
									{{ formatCurrency(item.loyalty_price_custom) }}
									<span class="cp-installments-price" v-if="installmentPrice" v-html="store.labels.pa_installments_price_text.replace('%PRICE%', formatCurrency(installmentPrice))" />
								</span>
							</div>
						</template>
					</div>

					<template v-if="item.badges">
						<template v-for="badgeSpecial in item.badges" :key="badgeSpecial.code">
							<div v-if="badgeSpecial.category == 2 && badgeSpecial.label_title_hover" @mouseover="couponBadgeTooltip = true" @mouseleave="couponBadgeTooltip = false" @click="copyCoupon(badgeSpecial.label_title_hover)" class="cp-badge-coupon">
								<span v-if="badgeSpecial.label_title" class="cp-badge-coupon-title"
									><span>{{ badgeSpecial.label_title }}</span></span
								>
								<div v-if="badgeSpecial.label_title_hover" class="cp-badge-coupon-info">
									<span class="title">{{ badgeSpecial.label_title_hover }}</span>
									<span class="icon">
										<span v-if="couponBadgeTooltip" class="cp-badge-coupon-tooltip">{{ couponBadgeTooltipText }}</span>
									</span>
								</div>
							</div>
						</template>
					</template>

					<div v-if="item?.payment_options" class="cp-payments-options">
						<div v-for="(item, key, index) in item?.payment_options" :key="index" class="cp-payments-option">
							<span v-html="store.labels['payment_' + key] ? store.labels['payment_' + key] : [key]"></span> <span v-if="item.club_only" v-html="store.labels.payment_option_club_only"></span>
						</div>
					</div>
				</div>

				<div class="cp-btns">
					<template v-if="item.is_available && !item.variation_total">
						<router-link v-if="item.type == 'advanced' || item.type == 'configurable'" class="btn btn-green cp-btn-addtocart cp-btn-details" :to="item.url_without_domain">{{ store.labels[addToCartLabel] }}</router-link>
						<a v-else class="btn btn-green cp-btn-addtocart" @click="addToCart()">{{ store.labels[addToCartLabel] }}</a>
					</template>
					<router-link :to="item.url_without_domain" v-else class="btn btn-green cp-btn-addtocart cp-btn-details" :title="store.labels.read_more"></router-link>
				</div>
			</div>
		</div>

		<div class="cp-col2">
			<figure class="cp-image">
				<router-link :to="item.url_without_domain">
					<Image loading="lazy" :data="item.main_image_upload_path_thumb" />
				</router-link>
			</figure>

			<div class="cp-available-qty" :class="[availableQtyClass]">
				<template v-if="item.status == '5' && item.date_available_humanize">
					{{ item.date_available_humanize }} ({{ item.user_warehouse_available_qty }})
				</template>
				<template v-else>
					{{ item.user_warehouse_available_qty }} ({{ item.user_nearby_available_qty }})
					<template v-if="item.status != '9' && item.status2 != '4'">({{ parseFloat(item.available_qty_supplier).toFixed(0) }})</template>
				</template>
			</div>
		</div>
	</article>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useRouter} from 'vue-router';
	import {useWebshopStore} from '@/stores/webshop';
	import {computed, ref} from 'vue';
	import Image from '@/components/Image.vue';
	import Modal from '@/components/Modal.vue';
	import useHelpers from '@/composables/useHelpers';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const props = defineProps(['item']);
	const modalStatus = ref(0);
	const {formatCurrency, formatDate} = useHelpers();

	// breakpoints
	import {breakpoints} from '@/composables/useBreakpoints';
	const tabletBp = breakpoints('t');

	//coupon tooltip text
	let couponBadgeTooltip = ref(false);
	let couponBadgeTooltipText = ref(store.labels.pa_coupon_copy ? store.labels.pa_coupon_copy : 'Kopiraj kodo');

	//copy coupon
	function copyCoupon(value) {
		navigator.clipboard.writeText(value);

		if (tabletBp.value == true) {
			couponBadgeTooltipText.value = store.labels.pa_coupon_copied ? store.labels.pa_coupon_copied : 'Koda je kopirana';
			setTimeout(() => {
				couponBadgeTooltip.value = false;
			}, 2000);
		} else {
			couponBadgeTooltipText.value = store.labels.pa_coupon_copied ? store.labels.pa_coupon_copied : 'Koda je kopirana';
			setTimeout(() => {
				couponBadgeTooltipText.value = store.labels.pa_coupon_copy ? store.labels.pa_coupon_copy : 'Kopiraj kodo';
			}, 2000);
		}
	}

	const conditionLabel = computed(() => {
		const lbls = store.labels.product_condition;
		const state = props.item.value.element_state_code;
		if (!lbls || !state) return '';
		
		const states = lbls.split('\n');
		const stateEntry = states.find(entry => entry.startsWith(state));
		return stateEntry ? stateEntry.split('=')[1] : '';
	});

	//qty class
	const availableQtyClass = computed(() => {
		let itemClass;
		if(props.item.status == '9' || props.item.status2 == '4') {
			itemClass = 'not-available';
		} else if(props.item.status == '2') {
			itemClass = 'supplier';
		} else if(props.item.status == '7') {
			itemClass = 'not-available-in-store';
		} else if(props.item.status == '5') {
			itemClass = 'preorder';
		}

		return itemClass;
	});

	const addToCartLabel = computed(() => {
		let label = 'add_to_shopping_cart';
		if (['advanced', 'configurable'].indexOf(props.item.type)) {
			label = 'add_to_shopping_cart_configurable';
		} else if (props.item.status == '5') {
			label = 'add_to_shopping_cart_preorder';
		}

		return label;
	});

	const userWarehouse = 0;
	const qtyWarehouse = computed(() => {
		let warehouses = [];

		Object.entries(props.item.warehouses).forEach(entry => {
			const [key, value] = entry;
			warehouses.push(key);
		});

		return warehouses;
	});

	const installmentPrice = computed(() => {
		let installment_price = 0;
		if (props.item.installments_calculation?.installments_min_price) {
			installment_price = props.item.installments_calculation.installments_min_price;
			if (props.item.installments_calculation.installments_loyalty_min_price) {
				installment_price = props.item.installments_calculation.installments_loyalty_min_price;
			}
		}

		return installment_price;
	});

	const router = useRouter();
	async function addToCart() {
		modalStatus.value = 0;
		await webshopStore
			.addProduct({
				shopping_cart_code: props.item.shopping_cart_code,
			})
			.then(res => {
				router.push({name: 'shoppingCart'});
			});
	}
</script>
