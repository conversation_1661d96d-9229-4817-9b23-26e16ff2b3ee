<template>
	<a class="discount red c-btn-filter" :class="{active: route.query.discount}" @click="submit()">{{ store.labels.pa_discounted_products }}</a>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useRoute, useRouter} from 'vue-router';
	import {useEventBus} from '@/composables/useEventBus';

	const store = useStore();
	const router = useRouter();
	const route = useRoute();
	const {emit} = useEventBus();

	function submit() {
		const query = {
			...route.query,
			to_page: undefined,
			discount: route.query.discount ? undefined : 1,
		};

		// update route
		router.push({query});

		// emit changes
		emit('filter', {discount: route.query.discount ? undefined : 1});
	}
</script>

<style scoped>
	a {
		cursor: pointer;
	}
</style>
