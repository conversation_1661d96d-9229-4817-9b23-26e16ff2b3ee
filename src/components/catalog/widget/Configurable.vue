<template>
	<div v-if="item.item_type_config && item.item_type_config.product_data_summary" class="cd-variations variations">
		<div v-for="(variation, key) in item.item_type_config.product_data_summary" :key="key" class="variation">
			<div class="variation-title">{{ item.item_type_config.attribute_data[key].title }}</div>
			<div :class="['variation-items', 'variation-item_' + item.item_type_config.attribute_data[key].code + '']">
				<div
					v-for="(variationItem, key) in variation"
					:key="key"
					@click="chooseVar(variationItem, variationItem.data.id, variationItem.data.attribute_id)"
					:class="[
						'attribute',
						'variation-attribute',
						'variation-' + variationItem.data.code + '',
						{'active': (item.primary_product_id && item.primary_product_id == variationItem.data.active) || (item.primary_product_data && item.primary_product_data.id == variationItem.data.active) || item.id == variationItem.data.active,},
						getClass(variationItem, variationItem.data.code, variationItem.data.attribute_id)
					]">
					<span v-if="variationItem.data.image_upload_path" class="img">
						<Image :src="variationItem.data.image_upload_path" default="/media/images/no-image-100.jpg" :alt="variationItem.data.title" />
					</span>
					<span class="title">{{ variationItem.data.title }}</span>
					<span class="price">{{ formatCurrency(variationItem.data.price) }}</span>
				</div>
			</div>
		</div>
	</div>

	<div v-if="(item.type == 'advanced' && item.type_config) || (catalogStore.typeConfigurable && item.type_config)" class="variations">
		<div class="variation">
			<div v-if="advancedItemTitle" class="variation-title">
				{{ store.labels.pa_advanced_attr_title }} <strong class="strong">{{ advancedItemTitle }}</strong>
			</div>
			<div class="variation-items">
				<div v-for="(variationItem, key) in item.type_config" :key="key" @click="chooseAdvancedVar(key, variationItem.title)" :class="['attribute', 'variation-attribute', 'variation-' + variationItem.code + '', {'active': advancedItem && advancedItem == key}]">
					<span class="title">{{ variationItem.title }}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useCatalogStore} from '@/stores/catalog';
	import {useWebshopStore} from '@/stores/webshop';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';
	import Image from '@/components/Image.vue';
	import {useEventBus} from '@/composables/useEventBus';
	import {onMounted, ref, watch} from 'vue';
	import useHelpers from '@/composables/useHelpers';

	const store = useStore();
	const catalogStore = useCatalogStore();
	const webshopStore = useWebshopStore();
	const ep = useEndpoints();
	const {bus} = useEventBus();
	const {formatCurrency} = useHelpers();

	const props = defineProps(['item', 'advancedItem', 'advancedItemTitle']);
	const emit = defineEmits(['shopingCartCode', 'advancedItemAttr']);
	let isAvailable = ref([]);

	onMounted(async () => {
		if(props.item.type == 'configurable') {
			const urlParams = new URLSearchParams(window.location.search);
			const searchQ = urlParams.get('search_q');
			let confShopingCartCode = null

			for (const key in props.item.type_config.product_data) {
				const obj = props.item.type_config.product_data[key];
				if (obj.code === searchQ) {
					confShopingCartCode = obj.offer_shopping_cart_code
					break;
				}
			}

			emit('shopingCartCode', confShopingCartCode);
		}
	});

	async function chooseAdvancedVar(code, title) {
		emit('advancedItemAttr', code, title);
	}

	async function chooseVar(data, id, attributeId) {
		store.loading = 1;
		
		//find all active attributes and exclude the attribute from the group that is clicked
		const activeVariationItems = Object.values(props.item.item_type_config.product_data_summary)
		.flatMap((variation) => Object.values(variation))
		.filter((variationItem) => variationItem.data.active == props.item.id && variationItem.data.attribute_id != attributeId)
		.map((variationItem) => variationItem.data.id);

		//add the id of attribute on which the user clicked
		activeVariationItems.push(data.data.id);

		//find offer shopping cart code for product that have values from the activeVariationItems array
		const product = Object.values(props.item.item_type_config.product_data).filter((obj) =>
			activeVariationItems.every((value) => obj.configurable_attribute_items_ids.includes(value))
		);
		const productCartCode = product.map((obj) => obj.offer_shopping_cart_code);

		emit('shopingCartCode', productCartCode);
	}

	function checkIfAvailable() {
		// Found the object with the matching key
		isAvailable.value = [];
		const itemCode = props.item.code;
		const objectLength = Object.keys(props.item?.item_type_config?.attribute_data).length;
		if(itemCode && props.item?.item_type_config?.product_data && objectLength > 1) {
			let foundObject = null;

			for (const key in props.item?.item_type_config?.product_data) {
				if (props.item?.item_type_config?.product_data[key].code === itemCode) {
					foundObject = props.item?.item_type_config?.product_data[key];
					break;
				}
			}

			if (foundObject) {
				const matchingObjects = [];

				for (const key in props.item?.item_type_config?.product_data) {
					if (props.item?.item_type_config?.product_data[key].configurable_attribute_items_ids.includes(foundObject.configurable_attribute_items_ids[0]) ||
						props.item?.item_type_config?.product_data[key].configurable_attribute_items_ids.includes(foundObject.configurable_attribute_items_ids[1])) {
						matchingObjects.push(props.item?.item_type_config?.product_data[key]);
					}
				}

				isAvailable.value = matchingObjects;
			}
		}
	}

	watch(
		() => bus.value.event,
		async (newValue) => {
			//advanced item
			if (bus.value.event === 'confAvailableClass') {
				checkIfAvailable();
				bus.value.event = null;
			}
		}
	);

	function getClass(data, code, attributeId) {
		//find all active attributes and exclude the attribute from the group that is clicked
		const activeVariationItems = Object.values(props.item.item_type_config.product_data_summary)
		.flatMap((variation) => Object.values(variation))
		.filter((variationItem) => variationItem.data.active == props.item.id && variationItem.data.attribute_id != attributeId)
		.map((variationItem) => variationItem.data.id);

		//add the id of attribute on which the user clicked
		activeVariationItems.push(data.data.id);

		if(isAvailable.value.length) {
			const matchingProduct = isAvailable.value.find(item =>
				item.configurable_attribute_items_ids.every(id => activeVariationItems.includes(id))
			);

			if (matchingProduct) {
				return matchingProduct.is_available ? 'available' : 'unavailable';
			} else {
				return 'hidden';
			}
		}

		return false;
	}
</script>

<style lang="less" scoped>
	.variations {
		margin-top: 25px;

		@media (max-width: @t) {
			margin: 0 0 20px;
		}
		@media (max-width: @m) {
			margin-bottom: 25px;
		}
	}
	.variation {
		margin-top: 20px;
		flex-grow: 1;
		position: relative;
		&:first-child {
			margin-top: 0;
		}
		&:first-child .variation-attribute.unavailable>span:before{content: none;}
		&:first-child .variation-attribute.hidden{display: flex;}

		@media (max-width: @t) {
			margin: 0 0 20px;
		}
		@media (max-width: @m) {
			margin-bottom: 25px;
		}
	}
	.variation-title {
		font-size: 14px;
		line-height: 1.4;
		letter-spacing: -0.23px;
		font-weight: 600;
		padding-bottom: 12px;

		@media (max-width: @m) {
			padding-bottom: 10px;
			font-size: 15px;
			letter-spacing: -0.3px;
		}
	}
	.variation-items {
		width: calc(~'100% - -10px');
		margin-left: -5px;
		display: flex;
		flex-wrap: wrap;

		@media (max-width: @m) {
			width: calc(~'100% - -6px');
			margin-left: -3px;
		}
	}
	.variation-attribute {
		min-width: 125px;
		max-width: calc(~'100% / 3');
		display: flex;
		align-items: center;
		justify-content: flex-end;
		flex-direction: column;
		flex-shrink: 0;
		margin: 0 5px 10px;
		padding: 15px;
		border: 1px solid @borderColor;
		outline: 1px solid transparent;
		text-align: center;
		color: @textColor;
		text-decoration: none;
		cursor: pointer;
		transition: border 0.3s, outline 0.3s;
		position: relative;
		&.active {
			border: 1px solid @blue;
			outline: 1px solid @blue;
			@media (min-width: @t) {
				&:hover {
					border: 1px solid @blue;
					outline: 1px solid @blue;
				}
			}
		}
		&.unavailable {
			opacity: 0.5;
			&:before {
				.pseudo(auto,auto);
				background: linear-gradient(to top right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0) calc(50% - 1px), rgba(51, 51, 51, 0.25) 50%, rgba(0, 0, 0, 0) calc(50% + 1px), rgba(0, 0, 0, 0) 100%);
				top: 0;
				right: 0;
				bottom: 0;
				left: 0;
				z-index: 1;
			}
			&:hover {
				border: 1px solid @borderColor;
			}
		}
		&.hidden{display: none;}
		@media (min-width: @t) {
			&:hover {
				text-decoration: none;
				color: @textColor;
				border: 1px solid @textColor;
				outline: 1px solid transparent;
			}
		}
		.img {
			display: flex;
			align-items: flex-start;
			justify-content: center;
			flex-grow: 1;
			max-width: 25px;
			border-radius: 100%;
			margin-bottom: 10px;
			img {
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				display: block;
				border-radius: 100%;
			}
		}
		.title {
			display: block;
			font-size: 14px;
			line-height: 1.4;
			font-weight: bold;
		}
		.price {
			display: block;
			font-size: 12px;
			line-height: 1.4;
			padding-top: 3px;
		}

		@media (max-width: @m) {
			min-width: 95px;
			max-width: calc(~'100% / 2');
			margin: 0 3px 6px;
			padding: 11px;
			.img {
				max-width: 20px;
				margin-bottom: 5px;
			}
			.title {
				font-size: 12px;
			}
			.price {
				font-size: 11px;
			}
		}
	}
</style>
