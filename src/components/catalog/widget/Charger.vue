<template>
	<div v-if="hasCharger && hasCharger?.title != 'ni podatka'" class="cd-charger" @click="openChargerFlyout()">
		<span class="image" v-if="hasCharger?.image_upload_path" >
			<Image :src="hasCharger.image_upload_path" alt="" />
		</span>
		<span class="title">{{store.labels.read_more}}</span>
	</div>
</template>

<script setup>
	import {computed, onMounted, ref} from 'vue';
	import {useStore} from '@/stores';
	import useFlyout from '@/composables/useFlyout';
	import Image from '@/components/Image.vue';
	const store = useStore()
	const props = defineProps(['item']);
	const {openFlyout} = useFlyout();
	const attributes = computed(() => {
		return props.item?.attributes?.length ? props.item.attributes : [];
	});

	const hasCharger = computed(() => {
		const hasCharger = attributes.value.find(attr => attr.attribute_code.startsWith('ima-polnilec-') || attr.attribute_code == 'bf004045');
		return hasCharger;
	});
	
	function openChargerFlyout() {
		let content = '';

		const chargerIncluded = store.labels.charger_included;
		content += chargerIncluded.replace('%VALUE%', hasCharger.value?.title || '-');

		const chargerMinPower = attributes.value.find(attr => attr.attribute_code.startsWith('minimalna-zahtevana-') || attr.attribute_code == 'bf004074');
		const chargerMaxPower = attributes.value.find(attr => attr.attribute_code.startsWith('maks-moc-polnilca-') || attr.attribute_code.startsWith('maksimalna-moc-pol-') || attr.attribute_code == 'bf004069');
		if(chargerMinPower || chargerMaxPower) {
			content += store.labels.charger_power
				.replace('%MIN%', chargerMinPower?.title != 'ni podatka' ? chargerMinPower.title : '-')
				.replace('%MAX%', chargerMaxPower?.title != 'ni podatka' ? chargerMaxPower.title : '-');
		}

		const chargerUsbSupport = attributes.value.find(attr => attr.attribute_code.startsWith('podpira-usb-polnjen-') || attr.attribute_code == 'bf004107');
		if(chargerUsbSupport) {
			content += store.labels.charger_usb.replace('%VALUE%', chargerUsbSupport.title);
		}

		const chargerDescription = store.labels.charger_flyout_description;
		if(chargerDescription) {
			content += chargerDescription;
		}

		openFlyout({
			title: store.labels.charger_flyout_title,
			content
		})
	}
</script>

<style lang="less" scoped>
	.cd-charger{
		display: flex; font-size: 14px; line-height: 1; align-items: center; cursor: pointer; margin-top: 8px;
		@media (max-width: @m){font-size: 12px;}
	}
	.image{
		width: 24px; flex-grow: 0; flex-shrink: 0; margin-right: 8px;
		@media (max-width: @m){width: 20px;}
	}
	img{max-height: 25px; width: auto; height: auto; display: block; margin: auto;}
</style>