<template>
	<a :class="{active: route.query.with_qty}" @click="submit()">{{ store.labels.pa_with_qty }}</a>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useRoute, useRouter} from 'vue-router';
	import {useEventBus} from '@/composables/useEventBus';

	const store = useStore();
	const router = useRouter();
	const route = useRoute();
	const {emit} = useEventBus();

	function submit() {
		const query = {
			...route.query,
			to_page: undefined,
			with_qty: route.query.with_qty ? undefined : 1,
		};

		// update route
		router.push({query});

		// emit changes
		emit('filter', {with_qty: route.query.with_qty ? undefined : 1});
	}
</script>

<style scoped>
	a {
		cursor: pointer;
	}
</style>
