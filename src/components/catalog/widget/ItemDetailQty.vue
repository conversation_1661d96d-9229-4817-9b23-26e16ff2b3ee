<template>
	<div class="cd-qty-container">
		<div class="cd-qty">
			<span class="cd-btn-qty cd-btn-dec" @click="updateQty('-', item.shopping_cart_code)"></span>
			<input class="cd-input-qty product_qty_input" type="text" name="qty" @change="updateQty('*', item.shopping_cart_code)" v-model="productQty" />
			<span class="cd-btn-qty cd-btn-inc" @click="updateQty('+', item.shopping_cart_code)"></span>
		</div>
	</div>
</template>

<script setup>
	import { ref, watch } from 'vue'
	import { useStore } from '@/stores'
	import { useCatalogStore } from '@/stores/catalog'

	const props = defineProps(['item'])
	const store = useStore()
	const catalogStore = useCatalogStore()
	const productQty = ref('1')

	watch(
		() => catalogStore.qty,
		() => productQty.value = catalogStore.qty,
		{deep: true}
	)

	const updateQty = async (action, code) => {
		let qty = catalogStore.qty
		if(action == '+') qty++
		if(action == '*') qty = productQty.value
		if(action == '-') qty--

		if(qty <= 1) qty = 1

		catalogStore.qty = qty
	}
</script>
