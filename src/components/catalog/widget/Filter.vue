<template>
	<template v-if="filter.code == 'category' && searchPage == null"></template>
	<div v-else :class="['cf-item', 'cf-item-' + filter.code, {active: showFilter}]">
		<!-- Filter title  -->
		<div @click="toggleFilter(filter)" class="cf-title">{{ filter.label }}<span class="toggle-icon"></span></div>

		<div class="cf-item-wrapper">
			<div v-if="filter.layout == 'sf'" class="cf-row">
				<div class="cf-range">
					<div class="cf-slider-input">
						<input type="number" :step="filter.options_config.step" :min="filter.options_config.min_value" :max="filter.options_config.max_value" v-model="sliderMin" />
						<span class="cf-slider-input-separator"></span>
						<input type="number" :step="filter.options_config.step" :min="filter.options_config.min_value" :max="filter.options_config.max_value" v-model="sliderMax" />
					</div>
					<div class="cf-slider">
						<div class="cf-slider-range">
							<input type="range" :step="filter.options_config.step" :min="filter.options_config.min_value" :max="filter.options_config.max_value" v-model="sliderMin" />
							<input type="range" :step="filter.options_config.step" :min="filter.options_config.min_value" :max="filter.options_config.max_value" v-model="sliderMax" />
						</div>
					</div>
					<button @click="addFilter()" class="btn btn-lightBlue cf-range-btn">{{ store.labels.confirm_filters }}</button>
				</div>
			</div>
			<div v-else :class="['cf-row', filter.code == 'category' ? 'level-' + option.level : '', {'cf-row-not-available': option && option.total_available == 0}]" v-for="(option, index) in filter.options" :key="filter.code + '-' + option.code">
				<input type="checkbox" :id="'search-' + filter.code + '-' + option.code + index" :name="filter.filter_url" :value="option.filter_url" @change="addFilter(option.level, option.position)" v-model="selected" />
				<label class="" :for="'search-' + filter.code + '-' + option.code + index">
					<span v-if="filter.code == 'rates'" class="cp-rate rates-container">
						<span v-for="index in 5" :key="index" class="icon-star-empty" :class="{'icon-star': option.code >= index}"></span>
					</span>

					<span v-html="option.title"></span>
				</label>
				<span v-if="option && option.total_available > 0" class="cf-counter">{{ option.total_available }}</span>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {onMounted, ref, watchEffect} from 'vue';
	import {useStore} from '@/stores';
	import {useCatalogStore} from '@/stores/catalog';
	import {useRoute, useRouter} from 'vue-router';

	const store = useStore();
	const catalogStore = useCatalogStore();
	const selected = ref([]);
	const router = useRouter();
	const route = useRoute();
	const props = defineProps(['filter', 'searchPage']);
	const emit = defineEmits(['filter']);
	let showFilter = ref();

	// breakpoints
	import {breakpoints} from '@/composables/useBreakpoints';
	const mobileBp = breakpoints('m');

	function toggleFilter() {
		if (showFilter.value == false) {
			showFilter.value = true;
		} else {
			showFilter.value = false;
		}
	}

	onMounted(() => {
		showFilter.value = 0;
	});

	// price range slider
	const sliderMin = ref(props.filter.options_config && props.filter.options_config.min_value ? Math.round(props.filter.options_config.min_value) : 0);
	const sliderMax = ref(props.filter.options_config && props.filter.options_config.max_value ? Math.round(props.filter.options_config.max_value) : 0);

	// set selected filters
	function selectedFilters() {
		selected.value = props.filter.options_selected ? props.filter.options_selected : [];

		if (props.filter.layout == 'sf') {
			const price = props.filter.options_selected ? props.filter.options_selected[0].split('-') : [Math.round(props.filter.options_config.min_value), Math.round(props.filter.options_config.max_value)];
			if (price != null) {
				sliderMin.value = price[0];
				sliderMax.value = price[1];
			}
		}
	}

	// show/hide filter based on breakpoints
	function _toggleFilter() {
		if (mobileBp.value) {
			showFilter.value = 0;
		}
	}

	watchEffect(() => {
		_toggleFilter(mobileBp.value);
		selectedFilters(props.filter.options);
	});

	// on filter click/change
	function addFilter(level, position) {
		/*
		let attr = [];
		const delimiter = '.';
		const start = 1;
		if (props.filter.code == 'category' && level < 3) {
			props.filter.options.forEach(el => {
				let tokens;
				if (level == 1) {
					tokens = el.position.split(delimiter).slice(0, start);
				} else {
					tokens = el.position.split(delimiter).slice(0, 2);
				}
				let elementPosition = tokens.join(delimiter);
				if (elementPosition == position && el.selected == false) {
					attr.push(el.filter_url);
				} else if (elementPosition == position && el.selected == true) {
					attr = [];
				}
			});
		} else {
			attr = props.filter.layout == 'sf' ? sliderMin.value + '-' + sliderMax.value : selected.value;
		}
		*/

		let attr = props.filter.layout == 'sf' ? sliderMin.value + '-' + sliderMax.value : selected.value;
		router.push({query: {...route.query, [props.filter.filter_url]: attr, to_page: undefined}});
		emit('filter');
		window.scrollTo({top: 0, behavior: 'smooth'});
	}
</script>

<style lang="less" scoped>
	input[type='range'] {
		-webkit-appearance: none;
		width: 100%;
		background: transparent;
	}
	input[type='range']::-webkit-slider-thumb {
		-webkit-appearance: none;
	}
	input[type='range']:focus {
		outline: none;
	}
	input[type='range']::-ms-track {
		width: 100%;
		cursor: pointer;
		background: transparent;
		border-color: transparent;
		color: transparent;
	}

	.cf-slider-input {
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		input {
			width: 50%;
			height: 44px;
			flex-grow: 1;
			font-size: 14px;
			text-align: center;
		}
	}
	.cf-slider-input-separator {
		width: 9px;
		height: 1px;
		margin: 0 9px;
		background: @black;
		font-size: 0;
		line-height: 44px;
	}

	.cf-slider {
		display: block;
		width: 100%;
		height: 5px;
		margin: 20px auto 10px;
		overflow: initial;
		position: relative;
		top: 0;
		left: 0;
	}
	.cf-slider-range {
		display: block;
		width: 100%;
		height: 5px;
		input {
			display: flex;
			align-items: center;
			justify-content: center;
			min-height: unset;
			margin: 0;
			padding: 0;
			border: none;
			border-radius: 0;
			font-size: 0;
		}
		input[type='range'] {
			-webkit-appearance: none;
			width: 100%;
			height: 5px;
			position: absolute;
			left: 0;
			bottom: 0;
		}
		input[type='range']:focus {
			outline: none;
		}
		input[type='range']::-webkit-slider-runnable-track {
			width: 100%;
			height: 5px;
			animate: 0.2s;
			background: #e7eff5;
			border: 0;
			border-radius: 0;
			box-shadow: none;
			cursor: pointer;
		}
		input[type='range']::-webkit-slider-thumb {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 8px;
			height: 15px;
			margin: -5px 0 0 0;
			padding: 0;
			background: @blue;
			border: none;
			border-radius: 0;
			font-size: 0;
			position: relative;
			z-index: 2;
			cursor: pointer;
		}
	}
	.cf-range-btn {
		width: 100%;
		height: 44px;
		min-height: 44px;
		margin-top: 10px;
		font-size: 14px;
		order: 3;
	}
</style>
