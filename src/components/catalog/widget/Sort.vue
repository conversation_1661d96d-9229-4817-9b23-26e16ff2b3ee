<template>
	<select @change="sort()" v-model="selectedSort">
		<option value="">{{ store.labels.ordering_priority }}</option>
		<option value="rates">{{ store.labels.ordering_top_rated }}</option>
		<option value="new">{{ store.labels.ordering_recent }}</option>
		<option value="old">{{ store.labels.ordering_older }}</option>
		<option value="expensive">{{ store.labels.ordering_expensive }}</option>
		<option value="cheaper">{{ store.labels.ordering_cheaper }}</option>
		<option value="az">{{ store.labels.ordering_az }}</option>
		<option value="za">{{ store.labels.ordering_za }}</option>
	</select>
</template>

<script setup>
	import {useRoute, useRouter} from 'vue-router';
	import {ref, watch, onMounted} from 'vue';
	import {useStore} from '@/stores';
	import {useEventBus} from '@/composables/useEventBus';

	const store = useStore();
	const route = useRoute();
	const router = useRouter();
	const selectedSort = ref(route.query.sort ? route.query.sort : '');
	const {emit} = useEventBus();

	function sort() {
		const query = {
			...route.query,
			to_page: undefined,
			sort: undefined,
		};
		if (selectedSort.value) query.sort = selectedSort.value;

		// update route
		router.push({query});

		// emit changes
		emit('filter', {sort: selectedSort.value});
	}

	// breakpoints
	import {breakpoints} from '@/composables/useBreakpoints';
	const mobileBp = breakpoints('m');

	watch(
		() => [route.query.sort, mobileBp.value],
		([value, mbp]) => {
			selectedSort.value = value ? value : '';
			_moveElement(mobileBp.value);
		}
	);
	onMounted(() => _moveElement(mobileBp.value));

	function _moveElement(bp) {
		const el = document.querySelector('.c-toolbar-sort-container');
		const dest = document.querySelector('.c-items');
		const toolbar = document.querySelector('.c-toolbar');

		if (bp) {
			dest.prepend(el);
		} else {
			toolbar.append(el);
		}
	}
</script>
