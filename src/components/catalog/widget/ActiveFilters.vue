<template>
	<div class="cf-active">
		<div v-for="activeFilter in data.selected" :key="activeFilter.id" @click="removeFilter(activeFilter.attribute_slug, activeFilter.slug)" class="cf-active-item">
			<span>{{ activeFilter.title }}</span>
		</div>
		<div v-if="data.selected && data.selected.length > 1" @click="removeAllFilters()" class="cf-active-btn-section">
			<div class="cf-active-item btn-cf-active-clear">
				<span>{{ store.labels.pa_clear_filtering }}</span>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useRoute, useRouter} from 'vue-router';

	const store = useStore();
	const router = useRouter();
	const route = useRoute();
	const props = defineProps(['data']);
	const emit = defineEmits(['remove']);

	async function removeFilter(title, value) {
		// copy of current route query
		const query = JSON.parse(JSON.stringify(route.query));

		// remove selected item
		if (typeof query[title] == 'string') {
			query[title] = undefined;
		} else {
			const index = query[title].indexOf(value);
			query[title].splice(index, 1);
		}

		// reset pagination
		query.to_page = undefined;

		// update route
		router.push({query});

		// emit remove event
		emit('remove');
	}

	async function removeAllFilters() {
		// clear query but keep sort and search term
		const query = {};
		if (route.query.sort) query.sort = route.query.sort;
		if (route.query.search_q) query.search_q = route.query.search_q;

		// update route
		router.push({query});

		// emit remove event
		emit('remove');
	}
</script>

<style lang="less" scoped>
	.cf-active {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10px;
	}
	.cf-active-item {
		display: flex;
		align-items: center;
		justify-content: center;
		background: @white;
		border-radius: @borderRadius;
		font-size: 12px;
		line-height: 1.4;
		color: @textColor;
		text-decoration: none;
		border: 1px solid @borderColor;
		margin: 0 10px 10px 0;
		padding: 6px 10px;
		cursor: pointer;
		position: relative;
		.transition(all);
		span {
			position: relative;
			padding-left: 18px;
			&:before {
				.icon-x;
				font: 12px/12px @fonti;
				font-weight: bold;
				color: @red;
				position: absolute;
				left: 0;
				top: 2px;
				.transition(color);
			}
		}
		@media (min-width: @t) {
			&:hover {
				background: @red;
				color: @white;
				border-color: @red;
				span:before {
					color: @white;
				}
			}
		}

		@media (max-width: @m) {
			margin: 0 5px 5px 0;
		}
	}
	.btn-cf-active-clear {
		border-color: @red;
		color: @red;
		cursor: pointer;
		.transition(all);
	}
</style>
