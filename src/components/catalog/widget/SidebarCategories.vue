<template>
	<ul :class="{'ci-item-wrapper cf-item-wrapper': level == 0}" v-if="items.children">
		<li v-for="item in items.children" :key="item.id" :class="{'has-children': item.children, 'active': item.active}">
			<a v-if="item.children" @click="item.active = !item.active">
				<span class="title">{{ item.title }}</span>
				<span class="toggle-icon" v-if="item.children"></span>
			</a>
			<router-link v-else :to="item.url_without_domain">
				<span class="title" :class="{'blue': activeCategoryUrl == item.url_without_domain}">{{ item.title }}</span>
				<span v-if="item.total && !item.children && item.level > 2" class="ci-counter">{{ item.total }}</span>
			</router-link>
			<SidebarCategories v-show="item.children" :current="current" :items="item" level="1" />
		</li>
		<li v-if="level != 0">
			<router-link :to="items.url_without_domain">
				{{ store.labels.show_all }}
				<span v-if="items.total" class="ci-counter">{{ items.total }}</span>
			</router-link>
		</li>
	</ul>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {onMounted, ref, watch} from 'vue';
	import {useRoute} from 'vue-router';
	const props = defineProps(['items', 'current', 'level']);
	const store = useStore();
	const parentPosition = ref();
	let activeCategoryUrl = ref();
	const route = useRoute();

	const parent = props.current.toString().split('.');
	const r = parent.pop();
	parentPosition.value = parent.join('.');

	function urlPathDetect() {
		const urlPath = window.location.pathname;
		activeCategoryUrl.value = urlPath;
	}

	onMounted(() => {
		if (props.items.children) {
			props.items.children.forEach(el => {
				if (el.position_h == props.current || el.position_h == parentPosition.value) {
					el.active = true;
				}
			});
		}

		urlPathDetect();
	});

	watch(
		() => route.path,
		async () => urlPathDetect()
	);
</script>
