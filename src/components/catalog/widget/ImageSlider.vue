<template>
	<div v-if="images" class="cd-images" :class="{'modal-gallery': catalogStore.modalGallery}">
		<Splide :options="mainOptions" ref="main" :class="['cd-hero-slider cd-hero-image', {'single': images.length == 1}]">
			<SplideSlide v-for="(image, index) in images" :key="index" class="cd-hero-slide" @click="modal = 1, store.modal.index = index" @mouseenter="activeZoom(image?.url, index)" @mousemove="zoomOnMove($event, index)" @mouseleave="resetZoom">
				<span><Image :data="image.url_thumb_large" default="/media/images/no-image-490.jpg" :alt="title" /></span>
				<div v-if="zoomIndex === index && zoom" class="cd-hero-zoom" :style="zoomedImageStyle"></div>
				<!-- image state badge -->
				<div v-if="image.state == 'd'" class="badge">{{ store.labels.condition_images_badge }}</div>
			</SplideSlide>
		</Splide>
		<Splide :options="thumbsOptions" ref="thumbs" :class="['cd-thumbs cd-thumbs-slider', {'special': images.length < 6}]">
			<SplideSlide v-for="(thumb, index) in images" :key="index" class="cd-thumb">
				<span><Image :data="thumb.url_thumb" default="/media/images/no-image-100.jpg" :alt="title" /></span>
			</SplideSlide>
		</Splide>
		<Modal :openValue="modal == 1" @close="modal = 0" :images="images" mode="gallery" />
	</div>
	<div v-else class="cd-no-image"><img loading="lazy" src="/media/images/no-image-490.jpg" alt="no-image" /></div>
</template>

<script setup>
	import {useStore} from '@/stores/index';
	import {useCatalogStore} from '@/stores/catalog';
	import {useImages} from '@/composables/useImages';
	import Modal from '@/components/Modal.vue';
	import {computed, onBeforeUnmount, onMounted, ref} from 'vue';
	import {Splide, SplideSlide} from '@splidejs/vue-splide';
	import '@splidejs/vue-splide/css/core';
	import Image from '@/components/Image.vue';
	import {useConfig} from '@/composables/useConfig';
	
	const store = useStore();
	const catalogStore = useCatalogStore();
	const {generateThumbs} = useImages();
	const props = defineProps(['items', 'title']);
	const modal = ref(0);
	const {baseUrl} = useConfig();

	// generate thumbs
	const images = computed(() => {
		let items = props.items;
		generateThumbs({
			data: items,
			preset: 'catalogDetail',
		});
		return items;
	});

	//image slider
	const main = ref(Splide);
	const thumbs = ref(Splide);

	function reloadSlider() {
		if (main.value) {
			// Destroy the current slider
			main.value.splide.destroy();
			thumbs.value.splide.destroy();

			// Reinitialize the slider
			main.value.splide.mount();
			thumbs.value.splide.mount();

			if (thumbs.value.splide) {
				main.value?.sync(thumbs.value.splide);
			}
		}
	};

	const mainOptions = {
		type: 'fade',
		perPage: 1,
		perMove: 1,
		pagination: false,
		arrows: false,
		rewind: true,
		arrows: true,
	};

	const thumbsOptions = {
		type: 'slide',
		direction: 'ttb',
		height: 440,
		gap: 10,
		perPage: 5,
		perMove: 1,
		isNavigation: true,
		cover: true,
		pagination: false,
		rewind: false,
		arrows: true,
		updateOnMove: true,
	};

	let timeout;
	onMounted(() => {
		const thumbsSplide = thumbs.value?.splide;
		if (thumbsSplide) {
			main.value?.sync(thumbsSplide);
		}

		if(timeout) clearTimeout(timeout);
		timeout = setTimeout(() => {
			reloadSlider();
		},2000);
	});

	//gallery
	function modalGalleryOpen() {
		document.querySelector('html').style.overflow = 'hidden';
		catalogStore.modalGallery = 1;
	}
	function modalGalleryClose() {
		document.querySelector('html').style.overflow = 'unset';
		catalogStore.modalGallery = 0;
	}
	document.addEventListener('keyup', function (event) {
		if (event.keyCode === 27) {
			document.querySelector('html').style.overflow = 'unset';
			catalogStore.modalGallery = 0;
		}
	});

	//zoom on hover
	let zoom = ref(false);
	let zoomImage = ref(null);
	let zoomIndex = ref(null);
	let zoomScale = ref(1);
	let zoomedImageStyle = ref('');

	function activeZoom(image, index) {
		zoom.value = true;
		zoomImage.value = baseUrl + image;
		zoomIndex.value = index;
	}
	function zoomOnMove(event, index) {
		if(zoom.value == true && zoomIndex.value === index && zoomImage.value) {
			const container = event.currentTarget;
			if(container) {
				const { left, top, width, height } = container.getBoundingClientRect();
				const mouseX = event.clientX - left;
				const mouseY = event.clientY - top;

				zoomScale.value = 1.4;
				const backgroundImage = `white url("${zoomImage.value}")`;
				zoomedImageStyle.value = `
					background: ${backgroundImage};
					transform: scale(${zoomScale.value});
					transform-origin: ${(mouseX / width) * 100}% ${(mouseY / height) * 100}%;
				`;
			}
		}
	};
	function resetZoom() {
		zoom.value = false;
		zoomImage.value = null;
		zoomIndex.value = null;
		zoomScale.value = 1;
		zoomedImageStyle.value = '';
	};

	onBeforeUnmount(() => {
		if(timeout) clearTimeout(timeout);
	})
</script>

<style lang="less">
	.cd-hero-zoom{display: flex; align-items: center; background-repeat: no-repeat!important; background-size: contain!important; background-position: center!important; transition: transform 0.3s; position: absolute; top: 0; bottom: 0; right: 0; left: 0; cursor: crosshair; z-index: 1111;}
	.cd-images {
		display: flex;
		margin-top: 25px;
		position: relative;

		@media (max-width: @t) {
			display: none;
		}
	}
	.cd-gallery-close {
		display: none;
		align-items: center;
		justify-content: center;
		width: 54px;
		height: 54px;
		background: @red;
		border-radius: 100%;
		border: 3px solid @white;
		position: absolute;
		top: 25px;
		right: 25px;
		box-shadow: 5px 8px 20px rgba(3, 32, 62, 0.2);
		cursor: pointer;
		z-index: 1;
		.transition(all);
		&:before {
			.icon-x;
			font: 20px/20px @fonti;
			color: @white;
			position: absolute;
		}
		@media (min-width: @t) {
			&:hover {
				background: @red / 1.2;
			}
		}

		@media (max-width: @m) {
			width: 34px;
			height: 34px;
			line-height: 34px;
			border: 2px solid @white;
			top: -12px;
			right: -12px;
			&:before {
				font-size: 14px;
				line-height: 14px;
			}
		}
	}
	.cd-images-m {
		display: none;

		@media (max-width: @t) {
			display: flex;
		}
	}
	.cd-hero-image {
		width: calc(~'100% - 90px');
		min-height: 1px;
		cursor: pointer;
		position: relative;
		&.single {
			width: 100%;
		}

		@media (max-width: @l) {
			width: calc(~'100% - 70px');
		}
		@media (max-width: @t) {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 220px;
		}
		@media (max-width: @m) {
			height: 100px;
		}
	}
	.cd-hero-slider {
		flex-shrink: 0;
	}
	.cd-hero-slide {
		display: flex !important;
		align-items: center;
		justify-content: center;
		height: 500px;

		@media (max-width: @l) {
			height: 400px;
		}
		@media (max-width: @t) {
			height: 220px;
		}
		@media (max-width: @m) {
			height: 100px;
		}
	}
	.cd-thumbs-slider {
		width: 80px;
		margin-left: 10px;
		padding: 30px 0;
		flex-shrink: 0;

		.cd-thumb {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 80px;
			cursor: pointer;
			border: 1px solid @borderColor;
			.transition(border-color);
			&:hover {
				border-color: @blue;
			}

			&.is-active {
				opacity: 1;
				border-color: @blue;
				&:hover {
					border-color: @blue;
				}
			}
		}

		@media (max-width: @l) {
			width: 60px;

			.cd-thumb {
				height: 60px;
			}
		}
	}
	.cd-no-image {
		width: 100%;
		height: 300px;
		display: flex;
		justify-content: center;
		align-items: center;

		@media (max-width: @l) {
			height: 200px;
		}
		@media (max-width: @t) {
			height: 150px;
		}
		@media (max-width: @m) {
			height: 80px;
		}
	}

	.cd-images {
		&.modal-gallery {
			margin: 0;
			background: @white;
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			z-index: 1111;
			.single .splide__arrows {
				display: none;
			}
			.cd-hero-slide span {
				height: auto;
			}
			.cd-hero-image {
				width: 100%;
			}
			.splide__track {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
			}
			.splide__arrows {
				display: flex;
			}
			.cd-gallery-close {
				display: flex;
			}
			.cd-thumbs-slider {
				display: none;
			}
			.cd-hero-slide {
				height: auto;
			}
			.cd-hero-image {
				cursor: default;
			}
		}
	}
	.cd-hero-image {
		@media (max-width: @t) {
			& > a {
				display: flex;
				align-items: center;
				justify-content: center;
			}
			img {
				width: auto;
				height: auto;
				max-width: 100%;
				max-height: 100%;
				display: block;
			}
		}
	}
	.cd-hero-slider {
		.splide__arrows {
			display: none;
		}
		.splide__arrow {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50px;
			height: 50px;
			background: transparent;
			position: absolute;
			top: 50%;
			right: 10%;
			transform: translateY(-50%);
			cursor: pointer;
			z-index: 1;
			.transition(all);
			&:before {
				.icon-arrow-down;
				font: 40px/40px @fonti;
				color: @black;
				.rotate(-90deg);
				.transition(color);
			}
			@media (min-width: @t) {
				&:hover {
					background: transparent;
					box-shadow: none;
					&:before {
						color: @blue;
					}
				}
			}

			@media (max-width: @t) {
				right: 5%;
			}
		}
		.splide__arrow--prev {
			right: auto;
			left: 10%;
			&:before {
				.rotate(90deg);
			}

			@media (max-width: @t) {
				left: 5%;
			}
		}
	}
	.cd-hero-slide {
		span {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 500px;
		}
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}

		@media (max-width: @l) {
			span {
				height: 400px;
			}
		}
		@media (max-width: @t) {
			span {
				height: 220px;
			}
		}
		@media (max-width: @m) {
			span {
				height: 100px;
			}
		}
	}
	.cd-thumbs-slider {
		&.special {
			padding: 0;
			.splide__arrow {
				display: none !important;
			}
		}

		.splide__arrow {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 20px;
			min-height: unset;
			margin: 0;
			padding: 0;
			background: @white;
			border: 1px solid @borderColor;
			font-size: 0;
			line-height: 0;
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 1;
			.transition(all);
			&:before {
				.icon-arrow-down;
				font: 9px/9px @fonti;
				color: @blue;
				z-index: 1;
				.transition(color);
			}
			svg {
				display: none;
			}
			@media (min-width: @t) {
				&:hover {
					background: @blue;
					border-color: @blue;
					box-shadow: none;
					&:before {
						color: @white;
					}
				}
			}
		}
		.splide__arrow--prev {
			top: 0;
			bottom: unset;
			&:before {
				.rotate(180deg);
			}
		}
		.splide__arrow:disabled {
			opacity: 0.5;
			&:hover {
				background: @white;
				border-color: @borderColor;
				&:before {
					color: @blue;
				}
			}
		}

		.cd-thumb {
			span {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80px;
			}
			img {
				width: auto;
				height: auto;
				max-width: 90%;
				max-height: 90%;
			}
		}

		@media (max-width: @l) {
			.cd-thumb span {
				height: 60px;
			}
		}
	}
	.cd-no-image {
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}
	}

	.badge{
		display: flex; align-items: center; justify-content: center; width: auto; height: 32px; padding: 0 15px; background: #CDD700; font-size: 14px; font-weight: bold; color: @white; text-decoration: none; position: absolute; left: 0; bottom: 0; z-index: 111; cursor: pointer; .transition(opacity);
		@media (min-width: @t){
			&:hover{opacity: 0.9;}
		}

		@media (max-width: @ms){display: none;}
	}
</style>
