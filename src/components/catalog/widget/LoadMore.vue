<template>
	<div class="load-more-container c-load-more-container" v-if="pagination && pagination.page?.next" ref="target">
		<a class="btn btn-white btn-medium load-more btn-load-more" @click="nextPage">{{ store.labels.pa_load_more_catalog }}</a>
	</div>
</template>

<script setup>
	import {ref, watch} from 'vue';
	import {useRoute, useRouter} from 'vue-router';
	import {useElementVisibility} from '@vueuse/core';
	import {useStore} from '@/stores';

	const store = useStore();
	const props = defineProps(['pagination', 'autoload']);
	const emit = defineEmits(['loadmore']);
	const route = useRoute();
	const router = useRouter();
	const target = ref(null);
	const targetIsVisible = useElementVisibility(target);
	const counter = ref(0);

	function nextPage() {
		router.push({query: {...route.query, 'to_page': props.pagination.page.next}});
		emit('loadmore', props.pagination.page.next);
	}

	watch(targetIsVisible, newValue => {
		if (props.autoload && newValue) {
			if (counter.value < props.autoload) {
				nextPage();
				counter.value++;
			}
		}
	});
</script>

<style lang="less" scoped>
	.load-more {
		width: 100%;
	}
	.loading .load-more {
		font-size: 0;
		&:before {
			.pseudo(40px,40px);
			background: url(/media/images/loader.svg) no-repeat center center;
			background-size: contain;
		}
	}
</style>
