<template>
	<div class="c-legend" :class="{'active': activeTooltip}" v-if="paLegend || paLegendExtra">
		<div class="c-legend-label" @click="activeTooltip = !activeTooltip"><span>{{ store.labels.pa_legend }}</span></div>
		<div class="c-legend-tooltip">
			<div class="c-lt-header">
				<div class="c-lt-header-title">{{ store.labels.pa_legend }}</div>
				<div class="c-lt-close"></div>
			</div>
			
			<div v-if="paLegend" class="c-lt-top">
				<div class="c-lt-item" v-for="legendItem in paLegend" :key="legendItem.id">
					<template v-if="legendItem.image">
						<div class="c-lt-icon c-lt-img">
							<!-- FIXME INTEG srediti putanju nakon što BE omogući upload_path kod rotatora -->
							<Image width="13" height="13" :src="'/upload/'+legendItem.image" />
						</div>
					</template>
					<template v-else-if="legendItem.element_color_4">
						<div class="c-lt-icon" :class="'c-lt-icon-'+legendItem.id" :style="{backgroundColor: legendItem.element_color_4}"></div>
					</template>
					<span v-if="legendItem.title">{{ legendItem.title }}</span>
				</div>
			</div>
			
			<div v-if="paLegendExtra" class="c-lt-bottom">
				<div class="c-lte-item" v-for="legendItemExtra in paLegendExtra" :key="legendItemExtra.id">
					<div class="c-lte-item-col1">
						<div class="c-lte-img"><img src="/media/images/no-image-60.jpg" alt=""></div>
						<div v-if="legendItemExtra.element_content_small" class="c-lte-item-qty" :class="'c-lte-item-qty-'+legendItemExtra.id" :style="{background: legendItemExtra.element_color_4}" v-html="legendItemExtra.element_content_small"/>
					</div>
					<div class="c-lte-item-col2" :class="'c-lte-item-col2-'+legendItemExtra.id" v-if="legendItemExtra.content" v-html="legendItemExtra.content"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import { ref, onMounted } from 'vue'
	import { useStore } from '@/stores/index'
	import { useEndpoints } from '@/composables/useEndpoints'
	import { useFetch } from '@/composables/useFetch'
	import Image from '@/components/Image.vue'

	const store = useStore()
	const ep = useEndpoints()
	const paLegend = ref()
	const paLegendExtra = ref()
	const activeTooltip = ref(0)

	onMounted(async () => {
		await useFetch({url: ep.endpoints.value._get_hapi_rotator_elements + '?lang=si&code=pa_legend,pa_legend_extra&limit=20'})
		.then(res => {
			if(res.data?.length) {
				const pl = res.data.find(el => el.code == 'pa_legend')
				if(pl) paLegend.value = pl.items

				const ple = res.data.find(el => el.code == 'pa_legend_extra')
				if(ple) paLegendExtra.value = ple.items
			}
		})
	})
</script>

<style lang="less" scoped>
	.c-legend-label{cursor: pointer;}
	.c-legend{
		font-size: 12px; margin-right: 30px; position: relative;
		&.active{
			.c-legend-label:after{opacity: 1;}
			.c-legend-tooltip{max-height: unset; opacity: 1; visibility: visible;}
		}

		@media (max-width: @m){width: 100%; flex-grow: 1; margin: 0;}
	}
	.c-legend-active{
		@media (max-width: @m){
			overflow: hidden; position: relative;
			&:before{.pseudo(auto,auto); position: fixed; top: 0; right: 0; bottom: 0; left: 0; background-color: rgba(0,24,47,0.6); z-index: 1111;}
		}
	}
	.c-legend-label{
		font-size: 14px; line-height: 1.4; text-decoration: underline; color: @textColor; position: relative;
		span{
			position: relative; padding-left: 21px;
			&:before{.icon-info; font: 13px/13px @fonti; color: @green; position: absolute; left: 0; top: 2px;}
		}
		&:after{.pseudo(10px,10px); top: calc(~"100% - -7px"); left: 2px; background: @white; .rotate(45deg); opacity: 0; .transition(opacity); z-index: 111;}

		@media (max-width: @m){
			display: inline-flex; align-items: center; justify-content: center; flex-grow: 1; width: 100%; min-height: 47px; background: @white; border: 1px solid @borderColor; border-radius: @borderRadius; padding: 5px 15px; margin: 0; text-decoration: none;
			&:after{content: none;}
		}
	}
	.c-legend-tooltip{
		width: 300px; border-radius: @borderRadius; background: @white; position: absolute; right: -10px; top: 30px; box-shadow: 0 0 30px 0 rgba(0,34,67,0.2); z-index: 11; max-height: 0; opacity: 0; visibility: hidden; .transition(opacity);
		&:before{.pseudo(auto,15px); left: 0; right: 0; top: -15px;}

		@media (max-width: @m){
			width: auto; position: fixed; top: 15px; left: 15px; right: 15px; z-index: 11111;
			&:before{content: none;}
		}
	}
	.c-lt-header{
		display: none;

		@media (max-width: @m){display: flex; align-items: center; justify-content: space-between; border-bottom: 1px solid @borderColor; padding: 14px 15px; font-size: 14px; line-height: 19px; letter-spacing: -0.2px;}
	}
	.c-lt-header-title{
		padding-left: 21px; position: relative;
		&:before{.icon-info; font: 13px/13px @fonti; color: @green; position: absolute; left: 0; top: 3px;}
	}
	.c-lt-close{
		width: 15px; height: 15px; display: flex; align-items: center; justify-content: center; position: relative;
		&:before{.icon-x; font: 17px/17px @fonti; color: @red; position: absolute;}
	}
	.c-lt-top{
		padding: 20px 20px 10px;

		@media (max-width: @m){padding-top: 13px;}
	}
	.c-lt-item{display: flex; align-items: center; margin-bottom: 10px; font-size: 12px; line-height: 1.4;}
	.c-lt-icon{width: 13px; height: 13px; flex-shrink: 0; border-radius: @borderRadius; margin-right: 10px;}
	.c-lt-img{
		display: block;
		img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}
	}
	.c-lt-bottom{padding: 10px 20px; border-top: 1px solid @borderColor;}
	.c-lte-item{display: flex; align-items: center; flex-grow: 1; margin-bottom: 10px;}
	.c-lte-item-col1{width: 60px; height: 60px; flex-shrink: 0; margin-right: 15px; border-radius: @borderRadius;}
	.c-lte-img{
		width: 100%; height: 39px; display: flex; align-items: center; justify-content: center; border: 1px solid @borderColor; border-bottom: none; border-radius: 2px 2px 0 0;
		img{width: auto; height: auto; max-width: 90%; max-height: 90%; display: block;}
	}
	.c-lte-item-qty{display: flex; align-items: center; justify-content: center; min-height: 21px; border-radius: 0 0 2px 2px; font-size: 11px; line-height: 1.4; font-weight: bold; color: @white; text-align: center;}
	.c-lte-item-col2{
		display: flex; flex-direction: column; justify-content: center; font-size: 12px; line-height: 1.4;
		h3,h4{font-size: 12px; line-height: 1.4; font-weight: bold; padding: 0 0 3px;}
		p{padding-bottom: 3px;}
		p:last-child{padding-bottom: 0;}
	}	

	.cd-legend{
		display: none; 
		
		@media (max-width: @t){
			display: flex; align-items: flex-end; position: absolute; top: 32px; right: 25px;
			.c-toolbar-bottom{padding: 0; background: transparent;}
			.c-legend{width: auto; flex-grow: 0; margin: 0;}
			.c-legend-label{
				display: block; min-height: auto; background: transparent; border: none; border-radius: 0; padding: 0; font-size: 12px;
				span:before{top: 0;}
			}
			.c-legend-tooltip{top: 27px;}
		}
		@media (max-width: @m){
			top: 18px; right: 15px;
			.c-legend-label{
				font-size: 11px; text-decoration: underline; text-underline-offset: 1px;
				span{
					padding: 0;
					&:before{content: none;}
				}
			}
		}
	}
</style>
