<template>
	<Modal @close="closeModal" :openValue="catalogStore.productModalStatus">
		<div class="wqr-scan-camera">
			<div class="wqr-scan-info scan-qrcode">{{ catalogStore.productModalStatus == 'add' ? store.labels.pa_scan_new_item : store.labels.scan_qr_code }}</div>

			<div class="wqr-scan-buttons">
				<button class="wqr-scan-button btn-white" :class="{'active': selectedMode == 'ean'}" @click="selectMode('ean')">{{ store.labels.ean_code }}</button>
				<button class="wqr-scan-button btn-white" :class="{'active': selectedMode == 'qr'}" @click="selectMode('qr')">{{ store.labels.qr_code }}</button>
				<button class="wqr-scan-button btn-white" :class="{'active': selectedMode == 'sku'}" @click="selectMode('sku')">{{ store.labels.sku_code }}</button>
			</div>

			<div class="wqr-scan-camera-box">
				<div class="camera-loading" v-if="camLoading">{{ store.labels.waiting_messages }}</div>
				<StreamBarcodeReader v-if="catalogStore.productModalStatus" @decode="onDecode" @loaded="camLoading = 0" />
			</div>
		</div>

		<div class="wqr-scan-content">
			<input v-if="selectedMode == 'ean'" ref="inputField" type="text" name="qrcode" id="field-pa-cart-code" v-model="code" :placeholder="store.labels.scan_product_qr" required />
			<input v-if="selectedMode == 'qr'" ref="inputField" type="text" name="barcode" id="field-barcode" v-model="code" :placeholder="store.labels.scan_product_barcode" required />
			<input v-if="selectedMode == 'sku'" ref="inputField" type="text" name="skucode" id="field-sku-code" v-model="code" :placeholder="store.labels.scan_product_sku" required />
			<div class="msg" v-if="msg">{{ store.labels[msg] }}</div>

			<input class="btn wqr-scan-send" @click="submit" :disabled="!code" type="submit" :value="store.labels.coupon_btn_add" />
		</div>
	</Modal>
</template>

<script setup>
	import {StreamBarcodeReader} from 'vue-barcode-reader';
	import {useRouter} from 'vue-router';
	import {ref, watch} from 'vue';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useCatalogStore} from '@/stores/catalog';
	import Modal from '@/components/Modal.vue';

	const router = useRouter();
	const store = useStore();
	const webshopStore = useWebshopStore();
	const catalogStore = useCatalogStore();
	const code = ref();
	const camLoading = ref(1);
	const selectedMode = ref('ean');
	const msg = ref();
	const inputField = ref();
	let focusTimeout;

	function focusInput() {
		inputField.value.focus();
	};

	function closeModal() {
		catalogStore.productModalStatus = null;
		code.value = '';
	}

	function selectMode(mode) {
		clearTimeout(focusTimeout);
		code.value = '';
		msg.value = '';
		selectedMode.value = mode;

		focusTimeout = setTimeout(function () {
			focusInput();
		}, 500);
	}

	function onDecode(obj) {
		code.value = obj;
		submit();
	}

	watch(
		() => catalogStore.productModalStatus,
		newValue => {
			clearTimeout(focusTimeout);
			if (newValue) {
				focusTimeout = setTimeout(focusInput, 500);
			}
		}
	);

	watch(code, newValue => {
		if (newValue.length === 12 && selectedMode.value == 'ean') {
			inputField.value.blur();
			submit();
		}
	});

	async function submit() {
		msg.value = '';

		let productConf = {
			'ean_code': code.value,
		};
		if (selectedMode.value == 'qr') {
			productConf = {
				'internal_id': code.value,
			};
		}
		if (selectedMode.value == 'sku') {
			productConf = {
				'code': code.value,
			};
		}

		if(catalogStore.productModalStatus == 'search') {
			const res = await catalogStore.fetchProducts({
				mode: 'widget',
				...productConf,
			});
			if(res?.data?.items?.length) {
				const product = res.data.items.length > 1 ? res.data.items.find(item => item.configurable_product_id) : res.data.items[0];
				closeModal();
				router.push(product.url_without_domain);
			} else {
				msg.value = 'selectproduct';
			}
		}

		if(catalogStore.productModalStatus == 'add') {
			const res = await webshopStore.addProduct(productConf);
			if(res.success) {
				closeModal();
			} else {
				msg.value = res.data.label_name;
			}
		}
	}
</script>

<style lang="less" scoped>
	.btn {
		width: 100%;
		min-height: 0;
	}
	.msg {
		display: block;
		margin-top: 7px;
		font-weight: bold;
		font-size: 12px;
		line-height: 1.2;
		color: @red;
	}
</style>