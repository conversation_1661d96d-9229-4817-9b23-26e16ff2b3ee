<template>
	<div class="c-toolbar">
		<div class="ci-checkbox-container">
			<div class="ci-checkbox ci-discount">
				<FilterDiscount />
			</div>

			<div class="ci-checkbox ci-available">
				<FilterWithQty />
			</div>
		</div>

		<Legend />

		<div class="c-toolbar-sort-container">
			<div class="sort c-sort">
				<Sort />
			</div>
		</div>
	</div>
</template>

<script setup>
	import Sort from '@/components/catalog/widget/Sort.vue';
	import FilterWithQty from '@/components/catalog/widget/FilterWithQty.vue';
	import FilterDiscount from '@/components/catalog/widget/FilterDiscount.vue';
	import Legend from '@/components/catalog/widget/Legend.vue';
	import {useStore} from '@/stores';

	const store = useStore();
</script>

<style lang="less" scoped>
	.c-toolbar {
		display: flex;
		align-items: center;
		flex-shrink: 0;

		@media (max-width: @m) {
			width: 100%;
			flex-shrink: unset;
		}
	}
	.c-toolbar-bottom {
		@media (max-width: @m) {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 15px 15px 0;
			background: rgba(204, 216, 226, 0.2);
		}
	}
	.ci-checkbox-container {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-grow: 1;

		@media (max-width: @m) {
			width: 100%;
		}
	}
	.ci-checkbox {
		font-size: 14px;
		line-height: 20px;
		font-weight: 600;
		padding-right: 30px;
		position: relative;
		color: @textColor;
		a {
			text-decoration: none;
			position: relative;
			padding: 0 0 0 30px;
			display: flex;
			align-items: center;
			&:before {
				.pseudo(18px,18px);
				border: 1px solid @borderColor;
				background: @white;
				position: absolute;
				left: 0;
				top: 0;
			}
			&.active {
				&:before {
					.icon-check;
					color: @blue;
					font: 10px/19px @fonti;
					width: 18px;
					height: 18px;
					border-color: @blue;
					background: @white;
					display: flex;
					justify-content: center;
					top: -1px;
				}
			}
		}

		@media (max-width: @m) {
			width: 50%;
			padding: 10px 15px;
			&:nth-child(2) {
				border-left: 1px solid @borderColor;
			}
		}
	}
	.ci-discount a:hover {
		opacity: 0.8;
		color: @red;
		@media (max-width: @m) {
			color: @red;
			opacity: 1;
		}
	}
	.ci-available a {
		color: @green2;
		&:hover {
			opacity: 0.8;
			color: @green2;
			@media (max-width: @m) {
				color: @green2;
				opacity: 1;
			}
		}
	}
	.c-toolbar-sort-container {
		position: relative;

		@media (max-width: @m) {
			flex-shrink: 0;
			margin: 0 0 15px;
		}
	}
	.c-sort {
		width: 160px;
		flex-shrink: 0;
		select {
			padding: 0 30px 0 15px;
			background-position: right 15px top 20px;
		}

		@media (max-width: @m) {
			width: 100%;
			select {
				background-position: right 15px top 19px;
			}
		}
	}
</style>
