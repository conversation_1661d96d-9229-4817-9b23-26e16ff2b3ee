<template>
	<div v-if="store?.info?.standalone_product_enable == '1'" class="btn sw-btn sw-btn-services fancybox_iframe" title="Storitve" @click="openServiceList()"></div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useCatalog} from '@/composables/useCatalog';

	const store = useStore();
	const {openServiceList} = useCatalog();
</script>

<style lang="less" scoped>
	.sw-btn-services:before{
		.icon-service; font: 24px/1 @fonti; color: @white;
		@media (max-width: @m){color: @blue; font-size: 20px;}
	}
</style>