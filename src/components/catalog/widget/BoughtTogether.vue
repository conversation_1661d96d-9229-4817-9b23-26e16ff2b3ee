<template>
	<div class="bought-together cd-bought-together" id="bought-together">
		<div class="cd-subtitle bought-related-title">{{ store.labels.pa_bought_together }}</div>
		<div id="product_special_list_recommendation" class="items-list">
			<div class="catalog-product-related-labels">
				<div v-for="relatedItem in items" :key="relatedItem.id" :class="['cd-bought-together-item', {'selected': relatedItem.selectedItem == true}]">
					<div class="cp-col1 cd-bt-item-content">
						<div class="cp-col1-top">
							<div class="cp-info-top">
								<div class="cp-badges">
									<div v-if="relatedItem.discount_percent_custom > 0 || relatedItem.price_custom < relatedItem.basic_price_custom" class="cp-badge cp-badge-discount discount">
										<span>-{{ relatedItem.discount_percent_custom }} %</span>
									</div>
									<div v-if="relatedItem.priority_details && relatedItem.priority_details.code == 'new'" class="cp-badge cp-badge-new new">
										<span v-if="relatedItem.priority_details.title">{{ relatedItem.priority_details.title }}</span>
									</div>
								</div>
								<h2 class="cp-title cp-bt-title">
									<router-link :to="relatedItem.url_without_domain">{{ relatedItem.title }}</router-link>
								</h2>
								<div class="cp-info">
									<div class="cp-code">
										<strong>{{ store.labels.id }}:</strong> {{ relatedItem.code }}
									</div>
									<div v-if="relatedItem.category_title" class="cp-category">
										<router-link :to="relatedItem.category_url_without_domain">{{ relatedItem.category_title }}</router-link>
									</div>
								</div>

								<div class="cp-info-top-right">
									<div class="cd-bt-item-image cd-bt-item-image-m">
										<router-link :to="relatedItem.url_without_domain">
											<Image :src="relatedItem.main_image_upload_path" :width="60" :height="60" default="/media/images/no-image-60.jpg" :alt="relatedItem.title" />
										</router-link>
									</div>
									<div class="cp-available-qty cp-available-qty-m" :class="[availableQtyClass]">
										<template v-if="relatedItem.status == '5' && relatedItem.date_available_humanize">
											{{ relatedItem.date_available_humanize }} ({{ relatedItem.user_warehouse_available_qty }})
										</template>
										<template v-else>
											{{ relatedItem.user_warehouse_available_qty }} ({{ relatedItem.user_nearby_available_qty }})
											<template v-if="relatedItem.status != '9' && relatedItem.status2 != '4'">({{ parseFloat(relatedItem.available_qty_supplier).toFixed(0) }})</template>
										</template>
									</div>
								</div>
							</div>

							<div v-if="relatedItem.attributes_special" class="cp-attributes">
								<span v-for="(relatedItemAttr, index) in relatedItem.attributes_special" :key="relatedItemAttr.code" class="cp-attribute">
									{{ relatedItemAttr.attribute_title }} <strong>{{ relatedItemAttr.title }}</strong
									><span v-if="index > 1" class="comma">, </span>
								</span>
							</div>
						</div>

						<div class="cp-price cd-bt-item-price">
							<div class="cd-bt-price-row1">
								<template v-if="['advanced', 'configurable'].indexOf(relatedItem.type) && relatedItem.basic_price_custom > relatedItem.price_custom">
									<div class="cp-current-price cp-variation-price">
										<span class="cp-old-price cp-price-label">{{ store.labels.price_variation }}</span>
										<span>{{ formatCurrency(relatedItem.price_custom) }}</span>
									</div>
								</template>
								<template v-else>
									<template v-if="(relatedItem.discount_percent_custom > 0 || relatedItem.price_custom < relatedItem.basic_price_custom) && relatedItem.selected_price != 'promotion2'">
										<div class="cp-old-price line-through">{{ formatCurrency(relatedItem.basic_price_custom) }}</div>
										<div class="cp-current-price cp-discount-price red">(-{{ relatedItem.discount_percent_custom }}%) {{ formatCurrency(relatedItem.price_custom) }}</div>
									</template>
									<div class="cp-current-price" v-else>
										{{ formatCurrency(relatedItem.price_custom) }}
									</div>
									<div class="cp-discount-expire" v-if="relatedItem.discount_expire">
										{{ relatedItem.discount_expire }}
									</div>
								</template>
							</div>
						</div>
					</div>

					<div class="cd-bt-col2">
						<div class="cd-bt-item-image">
							<router-link :to="relatedItem.url_without_domain">
								<Image :src="relatedItem.main_image_upload_path" :width="60" :height="60" default="/media/images/no-image-60.jpg" :alt="relatedItem.title" />
							</router-link>
						</div>
						<div class="cp-available-qty" :class="[availableQtyClass]">
							<template v-if="relatedItem.status == '5' && relatedItem.date_available_humanize">
								{{ relatedItem.date_available_humanize }} ({{ relatedItem.user_warehouse_available_qty }})
							</template>
							<template v-else>
								{{ relatedItem.user_warehouse_available_qty }} ({{ relatedItem.user_nearby_available_qty }})
								<template v-if="relatedItem.status != '9' && relatedItem.status2 != '4'">({{ parseFloat(relatedItem.available_qty_supplier).toFixed(0) }})</template>
							</template>
						</div>
						<div class="cd-bt-item-checkbox-container">
							<input
								@click="addRelatedItem(relatedItem.shopping_cart_code, relatedItem.selectedItem)"
								v-model="relatedItem.selectedItem"
								type="checkbox"
								name="product_special_list_recommendation"
								:id="'product_special_list_recommendation-' + relatedItem.shopping_cart_code"
								:value="relatedItem.shopping_cart_code" />
							<label :for="'product_special_list_recommendation-' + relatedItem.shopping_cart_code">{{ store.labels.bought_together_label }}</label>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="cd-bt-category">
		<div class="cd-subtitle cd-bt-category-title">{{ store.labels.pa_bought_together_categories }}</div>
		<div class="cd-bt-category-items">
			<template v-for="relatedItem in itemsCategories" :key="relatedItem.id">
				<router-link :to="relatedItem.category_url_without_domain" class="cd-bt-category-item">{{ relatedItem.category_title }}</router-link>
			</template>
		</div>
	</div>
</template>

<script setup>
	import {computed, ref} from 'vue';
	import {useStore} from '@/stores/index';
	import {useCatalogStore} from '@/stores/catalog';
	import Image from '@/components/Image.vue';
	import useHelpers from '@/composables/useHelpers';

	const store = useStore();
	const catalogStore = useCatalogStore();
	const {formatCurrency} = useHelpers();
	const props = defineProps(['items', 'item']);

	//qty class
	const availableQtyClass = computed(() => {
		let itemClass;
		if(props.item.status == '9' || props.item.status2 == '4') {
			itemClass = 'not-available';
		} else if(props.item.status == '2') {
			itemClass = 'supplier';
		} else if(props.item.status == '7') {
			itemClass = 'not-available-in-store';
		} else if(props.item.status == '5') {
			itemClass = 'preorder';
		}

		return itemClass;
	});

	const itemsCategories = computed(() => {
		const unique = [...new Map(props.items.map(c => [c.category_id, c])).values()];
		return unique;
	});

	function addRelatedItem(code, value) {
		if (value) {
			let index = catalogStore.relatedItemSelected.findIndex(el => el == code);
			catalogStore.relatedItemSelected.splice(index, 1);
		} else {
			catalogStore.relatedItemSelected.push(code);
		}
	}
</script>

<style lang="less" scoped>
	.cd-bought-together {
		margin-top: 40px;

		@media (max-width: @t) {
			margin: 0 0 30px;
		}
	}
	.cd-bought-together-item {
		display: flex;
		width: 100%;
		border: 2px solid @borderColor;
		border-radius: @borderRadius;
		margin-bottom: 15px;
		.transition(all);
		&.selected {
			border-color: @blue;
		}
		&.main {
			max-height: 0;
			visibility: hidden;
			opacity: 0;
			overflow: hidden;
			margin: 0;
			padding: 0;
			border: none;
		}

		@media (max-width: @m) {
			border: 1px solid @blue;
			position: relative;
		}
	}
	.cd-bt-item-content {
		padding: 20px 20px 20px 25px;

		@media (max-width: @m) {
			padding: 0;
		}
	}
	.cp-bt-title {
		font-weight: normal;
		margin-right: 0;
	}
	.cd-bt-item-price {
		align-items: flex-end;

		@media (max-width: @m) {
			min-height: 44px;
			padding-right: 45px;
			padding-left: 15px;
			padding-bottom: 10px;
		}
	}
	.cd-bt-discount-expire {
		display: block;
		margin: 4px 0 0 0;
		padding: 0;
		&:before {
			content: none;
		}
	}
	.cd-bt-col2 {
		width: 100px;
		display: flex;
		flex-direction: column;
		flex-shrink: 0;
		padding: 20px 10px 15px;
		border-left: 1px solid @borderColor;

		@media (max-width: @m) {
			width: auto;
			padding: 0;
			border: none;
			position: absolute;
			bottom: 10px;
			right: 10px;
			z-index: 1;
			.cp-available-qty {
				display: none;
			}
		}
	}
	.cd-bt-item-image {
		width: 100%;
		display: flex;
		justify-content: center;
		position: relative;
		flex-grow: 1;
		& > a {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 60px;
		}
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}

		@media (max-width: @m) {
			display: none;
		}
	}
	.cd-bt-item-image-m {
		display: none;

		@media (max-width: @m) {
			display: flex;
			align-items: center;
			border-left: 1px solid @borderColor;
			& > a {
				height: 45px;
			}
			img {
				max-width: 95%;
				max-height: 95%;
			}
		}
	}
	.cp-available-qty {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		background: @green;
		margin-top: 15px;
		padding: 6px 0;
		border-radius: @borderRadius;
		font-size: 11px;
		line-height: 1.4;
		text-align: center;
		font-weight: bold;
		color: @white;
		&.supplier {
			background: #f08747;
		}
		&.not-available-in-store {
			background: #f5b800;
		}
		&.not-available {
			background: #adbac4;
		}
		&.preorder {
			background: @blue;
		}
	}
	.cd-bt-item-checkbox-container {
		display: block;
		font-size: 0;
		line-height: 0;
		text-align: center;
		margin-top: 15px;
		input[type='checkbox'] + label {
			width: 24px;
			height: 24px;
			padding: 0;
			margin: 0;
			font-size: 0;
			line-height: 0;
		}
		input[type='checkbox'] + label:before {
			width: 22px;
			height: 22px;
			font-size: 10px;
			line-height: 22px;
		}

		@media (max-width: @m) {
			margin: 0;
		}
	}
	.bought-together-message {
		display: block;
		font-size: 14px;
		margin-top: 5px;
		color: @green;
	}
	.cd-bought-together-list-message {
		font-size: 14px;
		text-align: center;
		color: @green;
		position: absolute;
		top: calc(~'100% - -15px');
		left: -20px;
		right: -35px;
		a {
			color: @green;
			text-decoration: underline;
			&:hover {
				text-decoration: none;
			}
		}
	}

	.cd-bt-category {
		display: block;
		margin-top: 40px;
		position: relative;

		@media (max-width: @t) {
			margin: 0;
		}
	}
	.cd-bt-category-items {
		display: flex;
		flex-wrap: wrap;
	}
	.cd-bt-category-item {
		min-height: 28px;
		border: 1px solid @borderColor;
		border-radius: @borderRadius;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 8px 8px 0;
		padding: 5px 10px;
		font-size: 14px;
		line-height: 1.4;
		color: @textColor;
		.transition(all);
		@media (min-width: @t) {
			&:hover {
				border-color: @blue;
				color: @blue;
			}
		}

		@media (max-width: @m) {
			margin: 0 5px 5px 0;
			font-size: 12px;
		}
	}
</style>
