import {defineStore} from 'pinia';
import {useEndpoints} from '@/composables/useEndpoints';
import {useFetch} from '@/composables/useFetch';
import {useStore} from '@/stores';

const ep = useEndpoints();

export const useCatalogStore = defineStore({
	id: 'catalog',
	state: () => ({
		categories: [],
		typeConfigurable: false,
		relatedItemSelected: [],
		servicesSelected: [],
		activeFilters: [],
		toggleFilters: [],
		qty: 1,
		modalGallery: 0,
		itemDetail: false,
		redirected: false,
		productModalStatus: null, // null, search, add
	}),
	actions: {
		async fetchCategory(options = {}) {
			options.lang = 'si';
			options.single = true;

			return await useFetch({
				url: ep.endpoints.value._post_hapi_catalog_categories,
				method: 'POST',
				body: options,
			});
		},

		async fetchCategories(options = {}) {
			options.lang = 'si';
			options.hierarhy_by_position = options.hierarhy_by_position ? options.hierarhy_by_position : true;

			return await useFetch({
				url: ep.endpoints.value._post_hapi_catalog_categories,
				method: 'POST',
				body: options,
			});
		},

		async fetchProducts(options = {}) {
			options.lang = 'si';

			return await useFetch({
				url: ep.endpoints.value._post_hapi_catalog_products,
				method: 'POST',
				body: options,
			});
		},
	},
});
