import {defineStore} from 'pinia';
import {useEndpoints} from '@/composables/useEndpoints';
import {useFetch} from '@/composables/useFetch';
import {useStore} from '@/stores';
import {useWebshopStore} from '@/stores/webshop';
import {useToken} from '@/composables/useToken';

const ep = useEndpoints();

export const useAuthStore = defineStore({
	id: 'auth',
	state: () => ({
		user: {},
		locations: [],
	}),
	actions: {
		async fetchLocations() {
			await useFetch({url: ep.endpoints.value._get_hapi_auth_store}).then(res => (this.locations = res.data));
		},

		async submitLocation(locationId) {
			const store = useStore();
			const webshopStore = useWebshopStore();
			store.loading = 1;

			await useFetch({
				url: ep.endpoints.value._put_hapi_auth_store,
				method: 'PUT',
				body: {'store_location_id': locationId},
			}).then(async res => {
				if (res.success) {
					await this.fetchLocations();
					await webshopStore.fetchCarts();
				}

				store.loading = 0;
			});
		},

		async fetchUser() {
			const token = useToken();
			const webshopStore = useWebshopStore();

			await useFetch({url: ep.endpoints.value._get_hapi_webshop_seller_info}).then(async res => {
				if(res.success) {
					this.user = res.data;
				} else {
					await token.generateToken();
					await webshopStore.fetchCarts();
				}
			});
		},

		async logout(url) {
			await useFetch({
				url: ep.endpoints.value._post_hapi_auth_logout_api,
				method: 'POST',
				body: {'success_url': url},
			}).then(res => {
				if (res.success) {
					window.open(res.data.old_redirect_url, '_self');
				} else {
					console.log('logout: false');
				}
			});
		},
	},
});
