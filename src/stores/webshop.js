import {defineStore} from 'pinia';
import {useEndpoints} from '@/composables/useEndpoints';
import {useFetch} from '@/composables/useFetch';
import {useToken} from '@/composables/useToken';
import {useStore} from '@/stores';

const ep = useEndpoints();
const token = useToken();

export const useWebshopStore = defineStore({
	id: 'webshop',
	state: () => ({
		carts: {},
		cart: {},
		order: null,
		itemSelected: [],
		unchecked: 0,
		formValid: 0,
		shippingOptions: [],
		successMessage: null,
		updateCart: [],
		salesmanPriceItem: null,
		cartError: null,
		cartErrorInfo: null
	}),
	actions: {
		async fetchCarts() {
			return await useFetch({url: ep.endpoints.value._get_hapi_webshop_multicart}).then(async res => {
				this.carts = res.data.carts;
				await this.fetchCartItems();
				return res.data;
			});
		},

		async addCart() {
			const store = useStore();
			store.loading = 2;

			return await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_cart,
				method: 'POST',
			}).then(async res => {
				await token.generateToken({
					tokenId: res.data.token_id,
					tokenHash: res.data.token_hash,
				});
				this.fetchCarts();
			});
		},

		async removeCart(payload) {
			const store = useStore();
			store.loading = 1;

			return await useFetch({
				url: ep.endpoints.value._delete_hapi_webshop_cart,
				method: 'DELETE',
				body: [payload],
			}).then(res => this.fetchCarts());
		},

		async fetchCartItems() {
			const store = useStore();
			// + '&reset_cart_api=1&force_error=unknownError'
			return await useFetch({url: ep.endpoints.value._get_hapi_webshop_cart_full}).then(res => {
				this.cart = res.data;
				this.cartError = null;
				this.cartErrorInfo = null;
				store.loading = 0;
				return res.data;
			});
		},

		async addProduct(payload) {
			const store = useStore();
			store.loading = 1;

			let options = {
				'shopping_cart_code': payload.shopping_cart_code,
				'quantity': payload.quantity ? payload.quantity : 1,
			};
			if (payload.services) options.services = payload.services;
			if (payload.ean_code) options.ean_code = payload.ean_code;
			if (payload.internal_id) options.internal_id = payload.internal_id;
			if (payload.code) options.code = payload.code;

			return await useFetch({
				url: ep.endpoints.value._put_hapi_webshop_product,
				method: 'POST',
				body: [options],
			}).then(res => {
				this.fetchCarts();
				return res;
			});
		},

		async removeProduct(payload) {
			const store = useStore();
			store.loading = 1;

			await useFetch({
				url: ep.endpoints.value._delete_hapi_webshop_product,
				method: 'DELETE',
				body: [{'shopping_cart_code': payload.shopping_cart_code}],
			}).then(res => {
				this.fetchCarts();
				return res;
			});
		},

		async updateProduct(payload) {
			const store = useStore();
			store.loading = 1;

			let options = {
				'shopping_cart_code': payload.shopping_cart_code,
				'quantity': payload.quantity,
				'avans': payload.avans,
				'user_force_avans_item': payload.user_force_avans_item,
				'user_force_avans_item_reason': payload.user_force_avans_item_reason,
				'warehouse_pickup': payload.warehouse_pickup,
			};
			if (payload.services) options.services = payload.services;
			if (payload.serial_numbers) options.serial_numbers = payload.serial_numbers;

			return await useFetch({
				url: ep.endpoints.value._put_hapi_webshop_product,
				method: 'PUT',
				body: [options],
			}).then(res => {
				this.fetchCartItems();
				return res;
			});
		},

		async fetchShippingOptions() {
			return await useFetch({
				url: ep.endpoints.value._get_hapi_webshop_shipping,
			}).then(res => {
				this.shippingOptions = res.data;
			});
		},

		async setShippingOption(payload) {
			const store = useStore();
			store.loading = 1;

			const shippingId = store.flyout.selectedShippingId;
			const products = this.itemSelected.map(el => el.shopping_cart_code);

			const options = {
				shipping_id: shippingId,
				shopping_cart_codes: products,
				shipping_data: {},
			};

			// "osobno_preuzimanje"
			if (store.flyout.selectedLocation) {
				options.shipping_data.location_point_id = store.flyout.selectedLocation;
			}

			// "dostavna_sluzba"
			if (store.flyout.selectedDate) {
				options.shipping_data.shipping_date = store.flyout.selectedDate;
			}

			// shipping checkbox widget "Prevzem v skladištu" (item.warehouse_pickup_enabled)
			if (payload?.product) {
				options.shopping_cart_codes = [payload.product];

				this.shippingOptions.find(el => {
					if (payload.status == 1 && el.code == 'osobno_preuzimanje_skladiste') options.shipping_id = el.id;
					if (payload.status == 0 && el.code == 'osobno_preuzimanje_poslovnica') options.shipping_id = el.id;
				});
			}

			let endpoint = ep.endpoints.value._put_hapi_webshop_parcel_shipping;
			if(payload && payload == 'merge') {
				endpoint = ep.endpoints.value._put_hapi_webshop_parcel_shipping_merge;
			}

			/*
			await useFetch({
				url: ep.endpoints.value._delete_hapi_webshop_shipping_salesman_price,
				method: 'DELETE',
				body: {'shopping_cart_code': products},
			}).then(res => this.fetchCarts());
			*/

			return await useFetch({
				url: endpoint,
				method: 'PUT',
				body: options,
			}).then(res => this.fetchCarts());
		},

		async resetShipping() {
			const store = useStore();
			store.loading = 1;

			return await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_reset_delivery,
				method: 'POST',
			}).then(res => this.fetchCarts());
		},

		async setCustomer(payload) {
			const store = useStore();
			store.loading = 1;
			return await useFetch({
				url: ep.endpoints.value._post_hapi_customer_select_user,
				method: 'POST',
				body: payload,
			}).then(res => this.fetchCarts());
		},

		async setRecipient(payload) {
			const store = useStore();
			store.loading = 1;
			return await useFetch({
				url: ep.endpoints.value._post_hapi_customer_select_parcel_user,
				method: 'POST',
				body: payload,
			}).then(res => this.fetchCarts());
		},

		async updateCartTitle(payload) {
			return await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_cart_title,
				method: 'POST',
				body: payload,
			}).then(res => console.debug('title updated: ' + payload.cart_title));
		},

		async addCard(cardId) {
			const store = useStore();
			store.loading = 1;
			return await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_cart_code,
				method: 'POST',
				body: {'cart_code': cardId},
			}).then(res => {
				this.fetchCarts();
				return res;
			});
		},

		async addCurrentCard(cardId) {
			const store = useStore();
			store.loading = 1;
			return await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_current_cart_code,
				method: 'POST',
				body: {'cart_code': cardId},
			}).then(res => {
				this.fetchCarts();
				return res;
			});
		},

		async restoreCart(cardId) {
			const store = useStore();
			store.loading = 1;
			return await useFetch({
				url: ep.endpoints.value._post_hapi_webshop_cart_restore,
				method: 'POST',
				body: {'cart_code': cardId},
			}).then(res => {
				this.fetchCarts();
				return res;
			});
		},

		async updatePayment(options) {
			const store = useStore();
			store.loading = 1;

			const res = await useFetch({
				url: ep.endpoints.value._post_hapi_customer,
				method: 'POST',
				body: options,
			});
			this.fetchCarts();
			return res;
		}
	},
});
