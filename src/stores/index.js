import {defineStore} from 'pinia';
import {useEndpoints} from '@/composables/useEndpoints';
import {useFetch} from '@/composables/useFetch';
import {useWebshopStore} from '@/stores/webshop';
import {useCatalogStore} from '@/stores/catalog';
import {useAuthStore} from '@/stores/auth';

const ep = useEndpoints();

export const useStore = defineStore({
	id: 'main',
	state: () => ({
		labels: {},
		menus: [],
		redirects: [],
		routes: {},
		info: {},
		flyout: 0,
		loading: 0,
		creation: false,
		modal: {}
	}),
	actions: {
		async fetchCmsPage(slug) {
			return await useFetch({url: ep.endpoints.value._get_hapi_cms_pages + `?slug=${slug}`}).then(res => res.data.length && res.data[0]);
		},

		async initStore() {
			this.loading = 1;
			await useFetch({url: ep.endpoints.value._get_hapi_cms_labels}).then(res => (this.labels = res.data));
			await useFetch({url: ep.endpoints.value._get_hapi_cms_menus}).then(res => (this.menus = res.data));
			await useFetch({url: ep.endpoints.value._get_hapi_misc_routes}).then(res => (this.routes = res.data));
			await useFetch({url: ep.endpoints.value._get_hapi_misc_info}).then(res => (this.info = res.data));

			const authStore = useAuthStore();
			const webshopStore = useWebshopStore();
			const catalogStore = useCatalogStore();
			await authStore.fetchLocations();
			await authStore.fetchUser();
			await webshopStore.fetchCarts();
			await webshopStore.fetchShippingOptions();
			await catalogStore.fetchCategories().then(res => (catalogStore.categories = res.data));

			this.loading = 0;
		},
	},
	getters: {
		getMenu: state => code => {
			const menu = state.menus.find(el => el.code == code);
			return menu ? menu.items : '';
		},
	},
});
