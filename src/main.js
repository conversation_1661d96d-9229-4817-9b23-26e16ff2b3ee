import {createApp} from 'vue';
import {createPinia} from 'pinia';
import {useEndpoints} from '@/composables/useEndpoints';
import {useToken} from '@/composables/useToken';
import {useConfig} from '@/composables/useConfig';
import {useAuth} from '@/composables/useAuth';
import * as Sentry from '@sentry/vue';

import App from './App.vue';
import router from './router';

const app = createApp(App);
const ep = useEndpoints();
const token = useToken();
const auth = useAuth();
const {appVersion} = useConfig();

app.use(createPinia());

async function mountApp() {
	const endpoints = await ep.getEndpoints();

	if (endpoints.label_name) {
		await token.generateToken({credentials: 'omit'});
		if (location.href.includes('localhost')) {
			await auth.login().then(res => {
				token.setToken(res.jwt);
			});
		}
		setTimeout(mountApp, 300);
	} else {
		app.use(router).mount('#app');
	}
}

Sentry.init({
	app,
	dsn: 'https://<EMAIL>/4503930887077888',
	integrations: [
		Sentry.browserTracingIntegration({ router }),
	],
	trackComponents: true,
	maxBreadcrumbs: 10,
	release: appVersion,
	environment: process.env.NODE_ENV === 'production' ? 'production' : 'development',

	// Error monitoring
	sampleRate: process.env.NODE_ENV == 'development' ? 1 : 0.3,
	replaysOnErrorSampleRate: process.env.NODE_ENV == 'development' ? 0 : 0.3, // Screen capture

	// Performance monitoring
	tracesSampleRate: process.env.NODE_ENV == 'development' ? 0 : 0.3,
	replaysSessionSampleRate: 0, // Screen capture	
});
mountApp();
