<template>
	<div class="page-wrapper">
		<div class="main">
			<Sidebar />
			<div class="main-col main-content">
				<Header />
				<div class="content-section">
					<RouterView />
				</div>
			</div>
		</div>
	</div>
	<Loading />
	<ProductModal />
	<Flyout />
</template>

<style lang="less">
	@import '/media/webshop';
</style>

<script setup>
	import Flyout from '@/components/Flyout.vue';
	import {useStore} from './stores';
	import {useCatalogStore} from './stores/catalog';
	import Sidebar from '@/components/cms/Sidebar.vue';
	import Header from '@/components/cms/Header.vue';
	import Loading from '@/components/Loading.vue';
	import ProductModal from '@/components/catalog/widget/ProductModal.vue';

	const store = useStore();
	store.initStore();
	const catalogStore = useCatalogStore();
</script>
