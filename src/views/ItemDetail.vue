<template>
	<div v-if="item" class="cd-row">
		<a class="cd-back-btn" href="javascript:void(0);" @click="goBack()">Nazaj</a>

		<div class="cd-col1">
			<div class="cd-top">
				<div class="cd-badges">
					<div v-if="item.selected_price && item.selected_price == 'uau'" @mouseover="mouseOverBadge('uau')" @mouseleave="mouseLeaveBadge()" class="cd-badge uau">
						<span>{{ store.labels.uau_badge_title }}</span>
						<div v-if="'uau' == selectedBadge" class="cd-badge-tooltip">{{ store.labels.uau_badge_title }}</div>
					</div>

					<div v-if="item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom" @mouseover="mouseOverBadge('discount')" @mouseleave="mouseLeaveBadge()" class="cd-badge discount">
						<span>-{{ item.discount_percent_custom }} %</span>
						<div v-if="'discount' == selectedBadge" class="cd-badge-tooltip">{{ store.labels.pa_discount_badge_tooltip }}</div>
					</div>

					<div v-if="item.priority_details && item.priority_details.code == 'new'" @mouseover="mouseOverBadge('new')" @mouseleave="mouseLeaveBadge()" class="cd-badge new">
						<span v-if="item.priority_details.title">{{ item.priority_details.title }}</span>
						<div v-if="'new' == selectedBadge" class="cd-badge-tooltip">{{ store.labels.pa_new_badge_tooltip }}</div>
					</div>

					<template v-for="(badge_special_1, index) in item.badges?.length" :key="index">
						<div v-if="badge_special_1.category == 3 && badge_special_1.label_title" @mouseover="mouseOverBadge(badge_special_1.id)" @mouseleave="mouseLeaveBadge()" class="cd-badge gift">
							<span>-{{ badge_special_1.label_title }} %</span>
							<div v-if="badge_special_1.id == selectedBadge" class="cd-badge-tooltip">{{ badge_special_1.label_title }}</div>
						</div>
					</template>

					<div v-if="item.type != 'standalone'" class="cd-badge cd-badge-qty cp-badge" :class="[availableQtyClass]">
						<template v-if="item.status == '5' && item.date_available_humanize">
							{{ item.date_available_humanize }} ({{ item.user_warehouse_available_qty }})
						</template>
						<template v-else>
							{{ item.user_warehouse_available_qty }} ({{ item.user_nearby_available_qty }})
							<template v-if="item.status != '9' && item.status2 != '4'">({{ parseFloat(item.available_qty_supplier).toFixed(0) }})</template>
						</template>
					</div>
				</div>
				<div v-if="item.ean_code" class="cd-ean-box">
					<div class="cd-ean">
						<span>{{ item.ean_code }}</span>
					</div>
				</div>
			</div>

			<div class="cd-header">
				<template v-if="item.type == 'advanced'">
					<h1 v-if="item.primary_product_data && !advancedItem" class="cd-title">{{ item.seo_h1 }}</h1>
					<h1 v-if="item.type_config" class="cd-title">
						<span v-for="(varTitle, key) in item.type_config" :key="key" :class="{'hidden': advancedItem && advancedItem != key}">
							{{ item.seo_h1 }}<template v-if="varTitle.title_extra">, {{ varTitle.title_extra }}</template>
						</span>
					</h1>
				</template>
				<template v-else>
					<h1 class="cd-title">{{ item.seo_h1 }}</h1>
				</template>
				<div class="cd-info">
					<!-- Product code number & cart attributes -->
					<div class="cd-code">
						<template v-if="item.type == 'advanced'">
							<div v-if="item.primary_product_data && !advancedItem">
								<strong class="label">{{ store.labels.id }}:</strong> <span data-product_code="1">{{ item.primary_product_data.code }}</span>
							</div>
							<div v-if="item.type_config">
								<strong class="label">{{ store.labels.id }}: </strong>
								<span v-for="(varCode, key) in item.type_config" :key="key" :class="{'hidden': advancedItem && advancedItem != key}">
									<template v-if="item.primary_product_data">{{ item.primary_product_data.code }}</template
									><template v-if="varCode.code_extra">, {{ varCode.code_extra }}</template>
								</span>
							</div>
						</template>
						<template v-else-if="item.type == 'configurable' && item.primary_product_data">
							<div>
								<strong class="label">{{ store.labels.id }}:</strong> <span>{{ item.primary_product_data.code }}</span>
							</div>
						</template>
						<template v-else>
							<div>
								<strong class="label">{{ store.labels.id }}:</strong> <span>{{ item.code }}</span>
							</div>
						</template>
					</div>
					<div v-if="item.category_title" class="cd-category">
						<router-link :to="item.category_url_without_domain">{{ item.category_title }}</router-link>
					</div>
					<div v-if="item.logistic_class" class="cd-logistic">{{ item.logistic_class }}</div>
					<div v-if="item.element_state_code && conditionLabel" class="cd-condition-wrapper">
						<div class="cd-condition" @click="flyoutCondition(conditionLabel, store.labels['flyout_content_condition_' + item.element_state_code])">
							<span>{{conditionLabel}}</span>
						</div>
					</div>
				</div>
				<!---
				<div class="cd-info-extra">
					<router-link v-if="item.seller_title" :to="item.seller_url_without_domain" class="cd-info-container cd-seller-info"
						><span>{{ item.seller_title }}</span></router-link
					>
				</div>
				-->
			</div>

			<div class="cd-header-extra">
				<div class="cd-header-extra-col1">
					<!--
					<div class="cd-legend"><Legend /></div>
					-->
				</div>
				<div class="cd-header-extra-col2">
					<div class="cd-images-m">
						<div v-if="item.main_image" class="cd-hero-image">
							<a href="javascript:void(0);" class="cd-hero-slide product-gallery">
								<Image :src="item.main_image_upload_path" default="/media/images/no-image-490.jpg" :width="490" :height="490" :alt="item.title" />
							</a>
						</div>
						<div v-else class="cd-no-image"><img src="/media/images/no-image-490.jpg" alt="" data-product_main_image="1" /></div>
					</div>
				</div>
			</div>

			<template v-for="(gift, index) in item.badges?.length" :key="index">
				<div v-if="gift.category == 3 && (gift.title || gift.short_description)" class="cd-gift-section cd-col1-box" :class="{'no-image': !gift.gift_image_upload_path, active: gift.zoomActive}">
					<div v-if="gift.gift_image_upload_path" class="cd-gift-img-zoom">
						<Image :src="gift.gift_image_upload_path" default="/media/images/no-image-490.jpg" :width="510" :height="510" :alt="gift.label_title" />
					</div>
					<div v-if="gift.gift_image_upload_path" @mouseover="gift.zoomActive = true" @mouseleave="gift.zoomActive = false" class="cd-gift-img">
						<Image :src="gift.gift_image_upload_path" default="/media/images/no-image-100.jpg" :width="60" :height="60" :alt="gift.label_title" />
					</div>
					<div class="cd-gift-cnt">
						<div v-if="gift.title" class="cd-gift-title">{{ gift.title }}</div>
						<div v-if="gift.short_description" class="cd-gift-desc" v-html="gift.short_description"></div>
					</div>
				</div>
			</template>

			<div v-show="attributes" class="cd-section cd-attributes-section cd-col1-box">
				<div class="cd-attributes-header">
					<div class="cd-attributes-title cd-subtitle">{{ store.labels.tab_specs }}</div>
				</div>
				<div class="cd-attributes-table">
					<!-- Attributes -->
					<div v-for="(attribute, key) in attributes" :key="key" class="cd-attributes">
						<div class="cd-attributes-title">{{ attribute[0].attribute_title }}</div>
						<div class="cd-attributes-desc">
							<template v-for="(attribute_title, index) in attribute" :key="attribute_title.id"> <span v-if="index && index > 0">, </span>{{ attribute_title.title }} </template>
						</div>
					</div>
				</div>
			</div>

			<div v-if="description || item?.element_content_state" class="cd-section cd-desc cd-col1-box" :class="{'active': item.descActive && (mobileBp || tabletBp)}">
				<div class="cd-desc-header cd-attributes-header" @click="item.descActive = !item.descActive">
					<div class="cd-desc-title cd-subtitle cd-attributes-title">{{ store.labels.tab_product_description }}</div>
				</div>
				<div class="cd-desc-content state" v-if="item?.element_content_state">
					<h4>{{ store.labels.tab_product_state_description }}</h4>
					<div v-html="item.element_content_state"></div>
				</div>
				<div v-if="description" class="cd-desc-content" v-html="description"></div>
			</div>
			
			<div v-if="safetyInfo" class="cd-section cd-desc cd-safety cd-col1-box" :class="{'active': item.safetyInfo && (mobileBp || tabletBp)}">
				<div class="cd-desc-header cd-safety-header" @click="item.safetyInfo = !item.safetyInfo">
					<div class="cd-desc-title cd-subtitle cd-safety-title">{{ store.labels.tab_safety }}</div>
				</div>
				<div class="cd-desc-content">
					<div class="safety-section" v-if="safetyInfo.info" v-html="safetyInfo.info" />
					<div class="safety-section" v-if="safetyInfo.brandName && safetyInfo.brandZipCode && safetyInfo.brandCountry && safetyInfo.brandEmail">
						<div class="title">{{store.labels.safety_brand_info}}</div>
						<div v-if="store.labels.safety_brand_content" v-html="store.labels.safety_brand_content"></div>
						<div class="info">
							<p>{{ safetyInfo.brandName }}</p>
							<p>{{ safetyInfo.brandZipCode }}</p>
							<p>{{ safetyInfo.brandCountry }}</p>
							<p>{{ safetyInfo.brandEmail }}</p>
						</div>
					</div>
					<div class="safety-section" v-if="safetyInfo.rpName && safetyInfo.rpZipCode && safetyInfo.rpCountry && safetyInfo.rpEmail">
						<div class="title">{{store.labels.safety_responsible}}</div>
						<div v-if="store.labels.safety_responsible_content" v-html="store.labels.safety_responsible_content"/>
						<div class="info">
							<p>{{ safetyInfo.rpName }}</p>
							<p>{{ safetyInfo.rpZipCode }}</p>
							<p>{{ safetyInfo.rpCountry }}</p>
							<p>{{ safetyInfo.rpEmail }}</p>
						</div>
					</div>
					<div class="safety-section" v-if="safetyInfo.images?.length">
						<div class="title">{{store.labels.safety_images}}</div>
						<div v-if="store.labels.safety_images_content" v-html="store.labels.safety_images_content" />
						<div class="safety-images">
							<div v-for="(item, index) in safetyInfo.images" :key="item" class="safety-image" @click="openSafetyImagesModal(index)">
								<img :src="item" alt="" loading="lazy" />
							</div>
						</div>
						<Modal :openValue="safetyImagesModalStatus == 1" @close="safetyImagesModalStatus = 0" :images="safetyInfo.images" mode="gallery" />
					</div>
					<div class="safety-section" v-if="safetyInfo.documents?.length">
						<div class="title">{{store.labels.safety_documents}}</div>
						<div v-html="store.labels.safety_documents_content"></div>
						<a class="btn-download" :href="document" target="_blank" v-for="document in safetyInfo.documents" :key="document">{{getFileName(document)}}</a>
					</div>
				</div>
			</div>

			<div v-if="item.warehouses" class="cd-section cd-warehouses cd-col1-box" :class="{'active': item.warehousesActive && (mobileBp || tabletBp)}">
				<div class="cd-desc-header cd-warehouses-header" @click="item.warehousesActive = !item.warehousesActive">
					<div class="cd-desc-title cd-subtitle cd-attributes-title">{{ store.labels.pa_available_in_stores }}</div>
				</div>
				<div class="cd-warehouses-items">
					<p v-for="item in item.warehouses" :key="item.id" class="cd-warehouses-item">
						<span class="cd-warehouses-title">{{ item.title }}</span>
						<span class="cd-warehouses-qty" :class="{'last': item.available_qty == 1, 'unavailable': item.available_qty < 1}">{{ item.available_qty }}</span>
					</p>
				</div>
			</div>
		</div>

		<div class="cd-col2">
			<div v-if="item.price_custom > 0" class="cd-price" :class="{'uau-badge': item.selected_price && item.selected_price == 'uau'}">
				<div class="cd-price-row1">
					<div class="cd-price-cnt">
						<template v-if="item.type == 'advanced'">
							<div class="cd-old-price">{{ store.labels.price }}</div>
							<div v-if="item.primary_product_data && !advancedItem" class="cd-current-price">
								{{ formatCurrency(item.price_custom) }}
								<div class="cd-price-badge" v-if="item.selected_price == 'promotion2' && item.price_custom < item.basic_price_custom">
									{{store.labels.prihranek_promotion}}: <strong>{{ formatCurrency(item.basic_price_custom - item.price_custom) }}</strong>
								</div>
							</div>
							<template v-if="item.type_config">
								<template v-for="(varPrice, key) in item.type_config" :key="key">
									<div class="cd-current-price" :class="{'hidden': advancedItem && advancedItem != key}">
										{{ formatCurrency(varPrice.price) }}
										<div class="cd-price-badge" v-if="varPrice.selected_price == 'promotion2' && item.price_custom < item.basic_price_custom">
											{{store.labels.prihranek_promotion}}: <strong>{{ formatCurrency(varPrice.basic_price_custom - varPrice.price_custom) }}</strong>
										</div>
									</div>
									<span
										v-if="varPrice.installments_calculation && varPrice.installments_calculation?.installments_min_price"
										class="cd-installments-price"
										:class="{'hidden': advancedItem && advancedItem != key}"
										v-html="store.labels?.installments_price_text?.replace('%PRICE%', formatCurrency(varPrice.installments_calculation?.installments_min_price))"></span>
								</template>
							</template>
						</template>
						<template v-else>
							<template v-if="(item.discount_percent_custom > 0 || item.price_custom < item.basic_price_custom) && item.selected_price != 'promotion2'">
								<div class="cd-old-price line-through">{{ formatCurrency(item.basic_price_custom) }}</div>
								<div class="cd-current-price red">{{ formatCurrency(item.price_custom) }}</div>
							</template>
							<template v-else>
								<div class="cd-old-price">{{ store.labels.price }}</div>
								<div class="cd-current-price">
									{{ formatCurrency(item.price_custom) }}
									<div class="cd-price-badge" v-if="item.selected_price == 'promotion2' && item.price_custom < item.basic_price_custom">
										{{store.labels.prihranek_promotion}}: <strong>{{ formatCurrency(item.basic_price_custom - item.price_custom) }}</strong>
									</div>
								</div>
							</template>
							<span v-if="item.installments_calculation?.installments_min_price" class="cd-installments-price" v-html="store.labels?.installments_price_text?.replace('%PRICE%', formatCurrency(item.installments_calculation?.installments_min_price))"></span>
						</template>
					</div>
				</div>

				<template v-if="item.badges?.length">
					<template v-for="badgeSpecial in item.badges" :key="badgeSpecial.code">
						<div v-if="badgeSpecial.category == 2 && badgeSpecial.label_title_hover" @mouseover="couponBadgeTooltip = true" @mouseleave="couponBadgeTooltip = false" @click="copyCoupon(badgeSpecial.label_title_hover)" class="cp-badge-coupon cd-badge-coupon">
							<span v-if="badgeSpecial.label_title" class="cp-badge-coupon-title"
								><span>{{ badgeSpecial.label_title }}</span></span
							>
							<div v-if="badgeSpecial.label_title_hover" class="cp-badge-coupon-info">
								<span class="title">{{ badgeSpecial.label_title_hover }}</span>
								<span class="icon">
									<span v-if="couponBadgeTooltip" class="cp-badge-coupon-tooltip">{{ couponBadgeTooltipText }}</span>
								</span>
							</div>
						</div>
					</template>
				</template>
			</div>

			<div v-if="item.discount_expire" class="cd-discount-expire" v-html="store.labels?.pa_discount_expire?.replace('%d%', formatDate(item.discount_expire))"></div>
			<div v-if="item.date_available_humanize" class="cd-preorder-info cd-discount-expire">
				<span>Izid: {{ item.date_available_humanize }}</span>
			</div>

			<div
				v-if="item.installments_calculation && item.installments_calculation?.installments_min_price && item.installments_calculation.regular"
				@mouseover="item.installmentActive = true"
				@mouseleave="item.installmentActive = false"
				class="cd-installments-note"
				:class="{'hidden': item.type_config && advancedItem && item.configurable_product_id}">
				<span class="cd-installments-note-title">{{ store.labels.installments_calculate }}</span>
				<span v-show="item.installmentActive" class="cd-installments-note-tooltip">
					<span class="cd-installments-note-close"></span>
					<span v-if="item.installments_list_data.payment_logo_upload_path" class="cd-installments-tooltip-logo">
						<Image :src="item.installments_list_data.payment_logo_upload_path" :width="75" :height="22" default="/media/images/no-image-100.jpg" :alt="item.installments_list_data.payment_title" />
					</span>
					<span v-if="item.installments_list_data.payment_title" class="cd-installments-tooltip-title">{{ item.installments_list_data.payment_title }}</span>
					<span v-if="item.installments_calculation.regular" class="cd-installments-tooltip-table">
						<template v-for="(installment, key) in item.installments_calculation.regular" :key="key">
							<span>{{ key }} obrokov</span> <strong class="red">{{ formatCurrency(installment) }} </strong>&nbsp;/ mesec
							<br />
						</template>
					</span>
					<span v-if="item.installments_list_data.payment_description" v-html="item.installments_list_data.payment_description"></span>
					<span v-else v-html="store.labels.informativni_izracun"></span>
				</span>
			</div>

			<template v-if="item.type_config && item.configurable_product_id">
				<template v-for="(varInstallments, key) in item.type_config" :key="key">
					<div
						v-if="varInstallments.installments_calculation && varInstallments.installments_calculation?.installments_min_price && varInstallments.installments_calculation.regular"
						@mouseover="varInstallments.installmentActive = true"
						@mouseleave="varInstallments.installmentActive = false"
						class="cd-installments-note"
						:class="{'hidden': advancedItem && advancedItem != key}">
						<span class="cd-installments-note-title">{{ store.labels.installments_calculate }}</span>
						<span v-show="varInstallments.installmentActive" class="cd-installments-note-tooltip">
							<span class="cd-installments-note-close"></span>
							<span v-if="varInstallments.installments_list_data.payment_logo" class="cd-installments-tooltip-logo">
								<Image :src="varInstallments.installments_list_data.payment_logo" :width="75" :height="22" default="/media/images/no-image-100.jpg" :alt="varInstallments.installments_list_data.payment_title" />
							</span>
							<span v-if="varInstallments.installments_list_data.payment_title" class="cd-installments-tooltip-title">{{ varInstallments.installments_list_data.payment_title }}</span>
							<span v-if="varInstallments.installments_calculation.regular" class="cd-installments-tooltip-table">
								<template v-for="(installment, key) in varInstallments.installments_calculation.regular" :key="key">
									<span>{{ key }} obrokov</span> <strong class="red">{{ formatCurrency(installment) }} </strong>&nbsp;/ mesec
									<br />
								</template>
							</span>
							<span v-if="varInstallments.installments_list_data.payment_description" v-html="varInstallments.installments_list_data.payment_description"></span>
							<span v-else v-html="store.labels.informativni_izracun"></span>
						</span>
					</div>
				</template>
			</template>

			<div v-if="item?.payment_options?.length" class="cd-payments-options">
				<div class="cd-payments-options-title" v-html="store.labels.item_payment_options_title"></div>
				<div v-for="(item, key, index) in item?.payment_options" :key="index" class="cd-payments-option">
					<span v-html="store.labels['payment_' + key] ? store.labels['payment_' + key] : [key]"></span> <span v-if="item.club_only" v-html="store.labels.payment_option_club_only"></span>
				</div>
			</div>

			<div v-if="item.shipping_options?.length && item.shipping_options.filter(option => option.active).length" class="cd-shipping-options">
				<div v-for="item in item.shipping_options.filter(option => option.active)" :key="item.id" class="cd-shipping-option">
					<span class="title">
						<template v-if="item.id == 's'">{{ store.labels.item_delivery_standard_delivery }}:</template>
						<template v-else-if="item.id == 'e'">{{ store.labels.item_delivery_express_delivery }}:</template>
						<template v-else-if="item.id == 'bb'">{{ store.labels.item_delivery_bigbang_delivery }}:</template>
						<template v-else-if="item.id == 'bb_xxl'">{{ store.labels.item_delivery_bigbang_xxl_delivery }}:</template>
						<template v-else-if="item.id == 'bb_fast'">{{ store.labels.item_delivery_premium_title }}:</template>
						<template v-else-if="item.id == 'p'">{{ store.labels.item_delivery_pickup }}:</template>
					</span>
					<span class="value">
						<strong :class="{'green': !item.shipping_price || item.shipping_price == 0}">&nbsp;<template v-if="item.shipping_price && item.shipping_price > 0">{{ formatCurrency(item.shipping_price) }}</template>
						<template v-else>{{ store.labels.free }}</template></strong>
					</span>
				</div>
			</div>

			<div class="cd-special-attributes">
				<div v-if="item.attributes_special" class="cd-energy-info">
					<template v-for="(attr, index) in item.attributes_special" :key="attr.code">
						<template v-if="['bf000029', 'bf001083', 'bf004128', 'bf004129'].includes(attr.attribute_code)">
							<div v-if="index == 0" @click="modalStatus = 1" class="cd-energy" :class="{'link': item.energy_image_upload_path}">
								<Image v-if="attr.image_upload_path" :src="attr.image_upload_path" default="/media/images/no-image-100.jpg" :alt="attr.title" />
								<template v-if="attr.title && attr.energy_image_upload_path != null">{{ attr.title }}</template>
								<span>{{ store.labels.energy_title }}</span>
							</div>
						</template>
					</template>
				</div>
				<Charger :item="item" />
			</div>

			<Modal v-if="item.energy_image_upload_path" :openValue="modalStatus == 1" @close="modalStatus = 0" :item="item" mode="energy" />

			<ImageSlider v-if="item.all_images" :items="item.all_images" :title="item.title" />
{{item.configurable_product_id}}
			<Configurable v-if="item.configurable_product_id || catalogStore.typeConfigurable == true || item.type == 'advanced'" @shopingCartCode="getShopingCartCode" @advancedItemAttr="advancedItemAttrSelected" :item="item" :advancedItem="advancedItem" :advancedItemTitle="advancedItemTitle" />

			<ShoppingCartEntryServices :item="item" mode="itemDetail" />

			<!-- Bought together -->
			<BoughtTogether v-if="typeof boughtTogether.items !== 'undefined' && boughtTogether.items.length > 0" :items="boughtTogether.items" :item="item" />

			<div class="cd-add-to" v-if="item.price_custom && item.price_custom > 0 && (item.is_available || (item.user_warehouse && item.user_warehouse_available_qty > 0))">
				<div class="cd-add-to-section">
					<!--<SetCompare :item="item" />-->
					<!-- Add to cart section -->
					<div class="add-to-cart-container">
						<div class="add-to-cart-container">
							<template v-if="availabilityCondition && myLocationPointAvailabilityInfo == false">
								<div class="cd-not-available">
									<strong>{{ store.labels.pa_not_available }}</strong>
								</div>
							</template>
							<template v-else-if="item.configurable_product_id && item.type_config">
								<template v-for="(varAddToCart, key) in item.type_config" :key="key">
									<button :class="{'hidden': advancedItem && advancedItem != key}" @click="addToCart(varAddToCart.shopping_cart_code)" class="btn btn-green cd-btn-add">
										<span v-if="item.status == '5'">{{ store.labels.cd_add_to_shopping_cart_preorder }}</span>
										<template v-else>
											<template v-if="item.configurable_product_id">
												<span :class="{'hidden': advancedItem && advancedItem != key}">{{ formatCurrency(varAddToCart.price) }}</span>
											</template>
											<template v-else>
												<span>{{ formatCurrency(item.price_custom) }}</span>
											</template>
										</template>
									</button>
								</template>
							</template>
							<template v-else>
								<ItemDetailQty v-if="!singleItemCondition" :item="item" />
								<button @click="addToCart(item.shopping_cart_code)" class="btn btn-green cd-btn-add">
									<span v-if="item.status == '5'"
										>{{ store.labels.cd_add_to_shopping_cart_preorder }}<span v-if="item.price_custom" class="price-special"> ({{ formatCurrency(item.price_custom) }})</span></span
									>
									<span v-else>{{ formatCurrency(item.price_custom) }}</span>
								</button>
							</template>
						</div>
					</div>
				</div>
				
			</div>
		</div>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores/index';
	import {useCatalogStore} from '@/stores/catalog';
	import {useWebshopStore} from '@/stores/webshop';
	import {onMounted, computed, ref, watch} from 'vue';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {breakpoints} from '@/composables/useBreakpoints';
	import {useRoute, useRouter} from 'vue-router';
	import ImageSlider from '@/components/catalog/widget/ImageSlider.vue';
	import Image from '@/components/Image.vue';
	import ShoppingCartEntryServices from '@/components/webshop/widget/ShoppingCartEntryServices.vue';
	//import SetCompare from '@/components/catalog/widget/SetCompare.vue'
	import ItemDetailQty from '@/components/catalog/widget/ItemDetailQty.vue';
	import Legend from '@/components/catalog/widget/Legend.vue';
	import BoughtTogether from '@/components/catalog/widget/BoughtTogether.vue';
	import Configurable from '@/components/catalog/widget/Configurable.vue';
	import Modal from '@/components/Modal.vue';
	import Charger from '@/components/catalog/widget/Charger.vue';
	import {useEventBus} from '@/composables/useEventBus';
	import useHelpers from '@/composables/useHelpers';
	import useFlyout from '@/composables/useFlyout';

	const store = useStore();
	const catalogStore = useCatalogStore();
	const webshopStore = useWebshopStore();
	const ep = useEndpoints();
	const route = useRoute();
	const router = useRouter();
	const {formatCurrency, formatDate, getFileName, isObjectEmpty} = useHelpers();
	const {openFlyout} = useFlyout();
	const tabletBp = breakpoints('t');
	const mobileBp = breakpoints('m');
	const {bus} = useEventBus();
	let item = ref(null);
	const modalStatus = ref(0);
	const boughtTogether = ref(0);
	let attributes = ref(null);
	const description = ref(null);
	let advancedItem = ref(false);
	let advancedItemTitle = ref(false);

	let selectedBadge = ref('');
	function mouseOverBadge(value) {
		selectedBadge.value = value;
	}
	function mouseLeaveBadge() {
		selectedBadge.value = null;
	}

	function fetchProductData(value) {
		item.value = value.data;
		document.title = item.value.seo_title;

		catalogStore.servicesSelected = [];
		catalogStore.qty = 1;
		catalogStore.relatedItemSelected = [];

		if (value.data.configurable_product_id && value.data.type_config) {
			const firstKey = Object.keys(value.data.type_config)[0];
			const title = value.data.type_config[firstKey].title;

			advancedItem.value = firstKey;
			advancedItemTitle.value = title;
		}

		//attributes
		let attr = value.data.attributes;

		const warrantyLabels = {
			months12: '12 mesecev',
			months24: '24 mesecev',
			months36: '36 mesecev',
			months48: '48 mesecev',
			months60: '60 mesecev',
		};

		if (attr) {
			attributes.value = attr.reduce(function (r, a) {
				r[a.attribute_title] = r[a.attribute_title] || [];
				r[a.attribute_title].push(a);
				return r;
			}, Object.create(null));
		}

		// Add warranty attribute if available
		const warranty = (item.value.channel_element_warranty) ? item.value.channel_element_warranty : item.value.element_warranty;
		if(warranty) {
			if (!attributes.value) attributes.value = {};
			attributes.value['Garancija'] = [{
				attribute_title: 'Garancija',
				title: warrantyLabels[warranty] || warranty,
				id: 'warranty_' + warranty
			}];
		}

		// Add guarantee attribute if available
		const guarantee = (item.value.channel_element_guarantee) ? item.value.channel_element_guarantee : item.value.element_guarantee;
		if(guarantee) {
			if (!attributes.value) attributes.value = {};
			attributes.value['Jamstvo'] = [{
				attribute_title: 'Jamstvo',
				title: warrantyLabels[guarantee] || guarantee,
				id: 'guarantee_' + guarantee
			}];
		}

		//item description
		let descriptionLength = value.data.content ? value.data.content.replace(/<[^>]*>?/gm, '') : null;
		let pimChars = 0;
		if (value.data.appconfig) {
			pimChars = value.data.appconfig.product_pim_min_chars;
		}
		if (value.data.check_lists && value.data.check_lists?.['webcatalog_cs-fm'] == true && descriptionLength < pimChars) {
			let script = document.createElement('script');
			function setAttributes(el, attrs) {
				for (var key in attrs) {
					el.setAttribute(key, attrs[key]);
				}
			}
			setAttributes(script, {
				type: 'text/javascript',
				src: '//media.flixfacts.com/js/loader.js',
				'data-flix-distributor': '10251',
				'data-flix-language': 'sl',
				'data-flix-brand': value.data.manufacturer_title,
				'data-flix-ean': value.data.ean_code,
				'data-flix-sku': '',
				'data-flix-button': 'flix-minisite',
				'data-flix-inpage': 'flix-inpage',
				'data-flix-button-image': '',
				'data-flix-fallback-language': 'en',
				'data-flix-autoload': 'inpage',
				'data-flix-price': '',
			});
			description.value = '<div id="flix-inpage"></div>';
			document.head.appendChild(script);
		} else if (value.data.check_lists && value.data?.check_lists?.['webcatalog_cs-lb'] == true && descriptionLength < pimChars) {
			let script = document.createElement('script');
			script.setAttribute('src', 'http://button.loadbee.com/js/v2/loadbee.js');
			document.head.appendChild(script);
			description.value =
				'<div class="loadbeeTabContent"><div class="loadbeeTab" data-loadbee-manufacturer="EAN" data-loadbee-product="' + value.data.ean_code + '" data-loadbee-language="sl_SI" data-loadbee-css="default" data-loadbee-button="default" data-loadbee-template="default"></div></div>';
		} else if (value.data.check_lists && value.data?.check_lists?.['webcatalog_cs-lt'] == true && descriptionLength < pimChars) {
			description.value = '<iframe width="100%" src="https://bigbang.parhelion.hr/?ean=' + value.data.ean_code + '" frameborder="0" scrolling="auto" id="parhelion-frames"></iframe>';
		} else {
			description.value = value.data.content;
		}

		console.log(item.value);
	}

	async function fetchProduct() {
		store.loading = 1;
		let url = window.location;
		let data = url.pathname.split('-izdelek-');
		let urlId = data[1].replace('/', '');
		let slug = data[0].replace('/', '');

		await useFetch({
			url: ep.endpoints.value._get_hapi_catalog_product + '?item_id=' + urlId + '&item_slug=' + slug + '&check_lists=webcatalog_cs-lb,webcatalog_cs-fm,webcatalog_cs-lt&appconfig=product_pim_min_chars',
			method: 'GET',
		}).then(async res => {
			if (res.success) {
				fetchProductData(res);

				await useFetch({
					url: ep.endpoints.value._post_hapi_catalog_products,
					method: 'POST',
					body: {
						lang: 'si',
						mode: 'widget',
						related_code: 'bought_together',
						related_item_id: res.data.id,
						related_widget_data: res.data.related_widget_data,
						only_available: true,
						limit: '10',
						always_to_limit: true,
					},
				}).then(res => {
					if (res.success) {
						boughtTogether.value = res.data;
					}
				});

				_moveElement(tabletBp.value, mobileBp.value);

				store.loading = 0;
			}
			if (res.success == false && res.data.redirect_url) {
				catalogStore.redirected = true;
				if (res.data.redirect_url_without_domain) {
					router.push(res.data.redirect_url_without_domain);
				} else {
					const link = res.data.redirect_url;
					window.location.href = link;
				}
			}
		});
	}

	watch(
		() => [tabletBp.value, mobileBp.value, route.path],
		([tbp]) => {
			_moveElement(tabletBp.value, mobileBp.value);
		}
	);

	watch(
		() => route.path,
		async () => {
			if (catalogStore.itemDetail) {
				await fetchProduct();
			}
		}
	);

	onMounted(async () => {
		await fetchProduct();
		document.body.classList.add('page-catalog-detail');
	});

	// breakpoints
	function _moveElement(tbp, mbp) {
		const elHeader = document.querySelector('.cd-header');
		const elHeaderExtra = document.querySelector('.cd-header-extra-col1');
		const elHeaderExtraCol2 = document.querySelector('.cd-header-extra-col2');
		const elCol1 = document.querySelector('.cd-col1');
		const elCol2 = document.querySelector('.cd-col2');
		const elTop = document.querySelector('.cd-top');
		const elPrice = document.querySelector('.cd-price');
		const elPriceRow = document.querySelector('.cd-price-row1');
		const elInstallments = document.querySelector('.cd-installments-note');
		const elPaymentsOptions = document.querySelector('.cd-payments-options');
		const elShippingOptions = document.querySelector('.cd-shipping-options');
		const elEnergy = document.querySelector('.cd-special-attributes');
		const elEan = document.querySelector('.cd-ean-box');
		const elDiscountExp = document.querySelector('.cd-discount-expire');
		const elBadgeCoupon = document.querySelector('.cd-badge-coupon');

		if (mbp) {
			if (elEnergy) {
				elHeaderExtraCol2.append(elEnergy);
			}
			if (elEan) {
				elHeader.after(elEan);
			}
			if (elTop) {
				elHeaderExtra.append(elTop);
			}
			if (elPrice) {
				elHeaderExtra.append(elPrice);
			}
			if (elInstallments) {
				elHeaderExtra.append(elInstallments);
			}
			if (elPaymentsOptions) {
				elHeaderExtra.append(elPaymentsOptions);
			}
			if (elShippingOptions) {
				elHeaderExtra.append(elShippingOptions);
			}
			if (elBadgeCoupon) {
				elHeaderExtra.append(elBadgeCoupon);
			}
			if (elEnergy) {
				elHeaderExtra.append(elEnergy);
			}
			if (elDiscountExp) {
				elPriceRow.append(elDiscountExp);
			}
		} else if (tbp) {
			if (elEnergy) {
				elHeaderExtra.append(elEnergy);
			}
			if (elEan) {
				elTop.append(elEan);
			}
			if (elTop) {
				elHeaderExtra.append(elTop);
			}
			if (elPrice) {
				elHeaderExtra.append(elPrice);
			}
			if (elInstallments) {
				elHeaderExtra.append(elInstallments);
			}
			if (elPaymentsOptions) {
				elHeaderExtra.append(elPaymentsOptions);
			}
			if (elShippingOptions) {
				elHeaderExtra.append(elShippingOptions);
			}
			if (elBadgeCoupon) {
				elHeaderExtra.append(elBadgeCoupon);
			}
			if (elEnergy) {
				elHeaderExtra.append(elEnergy);
			}
			if (elDiscountExp) {
				elPriceRow.append(elDiscountExp);
			}
		}
	}

	//item desc
	window.addEventListener(
		'message',
		function (e) {
			if (e.hasOwnProperty('originalEvent')) {
				var origin = e.originalEvent.origin || e.origin;
			} else {
				var origin = e.origin;
			}
			if (origin !== 'https://bigbang.parhelion.hr') return;
			document.getElementById('parhelion-frames').style.height = e.data.frameHeight + 'px';
		},
		false
	);

	//variation
	async function getShopingCartCode(value) {
		catalogStore.typeConfigurable = true;
		const offerShoppingCartCode = value;
		const offerEp = ep.endpoints.value._get_hapi_catalog_offer;
		const offerEpValue = offerEp.replace('%CODE%', offerShoppingCartCode);

		return await useFetch({
			url: offerEpValue,
			method: 'GET',
		}).then(async res => {
			fetchProductData(res);

			await useFetch({
				url: ep.endpoints.value._post_hapi_catalog_products,
				method: 'POST',
				body: {
					lang: 'si',
					mode: 'widget',
					related_code: 'bought_together',
					related_item_id: res.data.id,
					related_widget_data: res.data.related_widget_data,
					only_available: true,
					limit: '10',
					always_to_limit: true,
				},
			}).then(res => {
				if (res.success) {
					boughtTogether.value = res.data;
				}
			});

			bus.value.event = 'confAvailableClass';
			store.loading = 0;
		});
	}

	// product condition label
	const conditionLabel = computed(() => {
		const lbls = store.labels.product_condition;
		const state = props.item.value.element_state_code;
		if (!lbls || !state) return '';
		
		const states = lbls.split('\n');
		const stateEntry = states.find(entry => entry.startsWith(state));
		return stateEntry ? stateEntry.split('=')[1] : '';
	});

	//advanced item attribute select
	function advancedItemAttrSelected(value, title) {
		advancedItem.value = value;
		advancedItemTitle.value = title;
	}

	//back button
	function goBack() {
		if (catalogStore.redirected == true) {
			router.go(-2);
			catalogStore.redirected = false;
		} else {
			router.back();
			catalogStore.redirected = false;
		}
	}

	//add to cart
	async function addToCart(value) {
		modalStatus.value = 0;
		store.loading = 1;
		let options = [{shopping_cart_code: value, quantity: catalogStore.qty, services: catalogStore.servicesSelected}];
		let related = [];
		if (catalogStore.relatedItemSelected.length) {
			catalogStore.relatedItemSelected.forEach(el => {
				related.push({shopping_cart_code: el, quantity: 1});
			});
		}
		const data = options.concat(related);

		return await useFetch({
			url: ep.endpoints.value._put_hapi_webshop_product,
			method: 'POST',
			body: data,
		}).then(res => {
			webshopStore.fetchCarts();
			router.push({name: 'shoppingCart'});
			store.loading = 0;
		});
	}

	const availabilityCondition = computed(() => {
		let availableConditionValue = item.value.warehouses_pa_ids ? item.value.available_pa_qty < 1 : item.value.available_qty + item.value.user_warehouse_available_qty < 1;
		return availableConditionValue;
	});
	const singleItemCondition = computed(() => {
		let singleItemConditionValue = item.value.warehouses_pa_ids ? item.value.available_pa_qty == 1 : item.value.available_qty + item.value.user_warehouse_available_qty == 1;
		return singleItemConditionValue;
	});
	const myLocationPointAvailabilityInfo = computed(() => {
		let myLocationPointAvailabilityInfoValue = item.value.user_warehouse_available_qty ? item.value.user_warehouse_available_qty == 1 : item.value.shipping_date;
		return myLocationPointAvailabilityInfoValue;
	});

	// item safety info tab
	const safetyImagesModalStatus = ref(0);
	function openSafetyImagesModal(index) {
		safetyImagesModalStatus.value = 1;
		store.modal.index = index;
	}
	const safetyInfo = computed(() => {
		if(!item.value) return false;
		const lang = 'si';
		let data = {};

		if( item.value.element_safety_documents?.[lang]?.length) data.documents =  item.value.element_safety_documents[lang];
		if( item.value.element_safety_images?.[lang]?.length) data.images =  item.value.element_safety_images[lang];
		if( item.value.element_safety_info?.[lang]?.length) data.info =  item.value.element_safety_info[lang];

		if( item.value.element_sman_name) data.brandName =  item.value.element_sman_name;
		if( item.value.element_sman_code) data.brandZipCode =  item.value.element_sman_code;
		if( item.value.element_sman_country) data.brandCountry =  item.value.element_sman_country;
		if( item.value.element_sman_email) data.brandEmail =  item.value.element_sman_email;

		if( item.value.element_rp_name) data.rpName =  item.value.element_rp_name;
		if( item.value.element_rp_zipcode) data.rpZipCode =  item.value.element_rp_zipcode;
		if( item.value.element_rp_country) data.rpCountry =  item.value.element_rp_country;
		if( item.value.element_rp_email) data.rpEmail =  item.value.element_rp_email;

		return isObjectEmpty(data) ? false : data;
	});

	//qty class
	const availableQtyClass = computed(() => {
		let itemClass;
		if(item.value.status == '9' || item.value.status2 == '4') {
			itemClass = 'not-available';
		} else if(item.value.status == '2') {
			itemClass = 'supplier';
		} else if(item.value.status == '7') {
			itemClass = 'not-available-in-store';
		} else if(item.value.status == '5') {
			itemClass = 'preorder';
		}

		return itemClass;
	});

	//coupon tooltip text
	let couponBadgeTooltip = ref(false);
	let couponBadgeTooltipText = ref(store.labels.pa_coupon_copy ? store.labels.pa_coupon_copy : 'Kopiraj kodo');

	//copy coupon
	function copyCoupon(value) {
		navigator.clipboard.writeText(value);

		if (tabletBp.value == true) {
			couponBadgeTooltipText.value = store.labels.pa_coupon_copied ? store.labels.pa_coupon_copied : 'Koda je kopirana';
			setTimeout(() => {
				couponBadgeTooltip.value = false;
			}, 2000);
		} else {
			couponBadgeTooltipText.value = store.labels.pa_coupon_copied ? store.labels.pa_coupon_copied : 'Koda je kopirana';
			setTimeout(() => {
				couponBadgeTooltipText.value = store.labels.pa_coupon_copy ? store.labels.pa_coupon_copy : 'Kopiraj kodo';
			}, 2000);
		}
	}

	//flyout condition
	function flyoutCondition(title, content) {
		openFlyout({
			mode: 'condition',
			title: title,
			content: content,
		})
	}

	//installment price
	/*
	const installmentPrice = computed(() => {
		let installmentPriceValue = (item.value.installments_calculation.regular) ? item.value.installments_calculation.regular : null
		if(installmentPriceValue) {
			let installmentPriceLast = installmentPriceValue[Object.keys(installmentPriceValue)[Object.keys(installmentPriceValue).length - 1]]
			let formatter = new Intl.NumberFormat('sl-SI', {
				style: 'currency',
				currency: 'EUR'
			})
			let installmentPriceText = store.labels.installments_price_text
			return installmentPriceText.replace('%PRICE%', formatter.format(installmentPriceLast))
		}
	})
	*/
</script>

<style lang="less" scoped>
	.cd-section{
		margin-bottom: 20px;
		@media (max-width: @t){margin-bottom: 15px;}
	}
	.safety-section{
		padding-bottom: 25px;
		.title{font-weight: bold; padding: 0 0 10px; font-size: 15px;}
		.info p{padding: 0;}
	}
	.safety-images{display: flex; flex-wrap: wrap; gap: 10px;}
	.safety-image{width: calc(100% / 4 - 8px); cursor: pointer;}

	.cd-row {
		display: flex;
		min-height: calc(~'100vh - 250px');

		@media (max-width: @t) {
			display: block;
			min-height: 100vh;
		}
	}
	.btn-download{
		display: flex; align-items: center; border: 1px solid @borderColor; margin-bottom: 5px; padding: 10px 15px; word-wrap: break-word; font-size: 13px;
		&:hover{color: @blue;}
		&:before{.icon-upload; font: 18px/1 @fonti; .scaleY(-1); margin-right: 10px; color: @blue;}
	}

	.cd-col1 {
		flex-grow: 1;
		padding-right: 50px;
		position: relative;
		&:before {
			.pseudo(auto,auto);
			background: #f8f9fa;
			top: -100px;
			bottom: -150px;
			right: 0;
			left: -200px;
			z-index: -1;
		}
		&:after {
			.pseudo(1px,auto);
			background: @borderColor;
			top: -100px;
			bottom: -150px;
			right: 0;
			z-index: 1;
		}

		@media (max-width: @l) {
			padding-right: 30px;
		}
		@media (max-width: @t) {
			width: 100%;
			padding: 85px 45px 45px;
			background: #f8f9fa;
			&:before,
			&:after {
				content: none;
			}
		}
		@media (max-width: @m) {
			padding: 47px 15px 15px;
			border-bottom: 1px solid @borderColor;
		}
	}
	.cd-back-btn {
		width: 30px;
		height: 30px;
		min-height: 30px;
		flex-shrink: 0;
		padding: 0;
		font-size: 0;
		line-height: 0;
		position: sticky;
		top: 100px;
		margin: 0 40px 0 -70px;
		&:before {
			.icon-arrow;
			font: 9px/9px @fonti;
			color: @white;
			position: absolute;
		}

		@media (max-width: @t) {
			display: none;
		}
		@media (max-width: @m) {
			display: flex;
			width: 47px;
			height: 47px;
			border-radius: 0;
			position: fixed;
			top: 0;
			left: 0;
			margin: 0;
			z-index: 111;
			&:before {
				font-size: 11px;
				line-height: 11px;
			}
		}
	}
	.cd-top {
		display: flex;
		align-items: flex-start;
		margin-bottom: 10px;

		@media (max-width: @t) {
			margin-bottom: 15px;
		}
		@media (max-width: @m) {
			margin-bottom: 10px;
		}
	}
	.cd-badges {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		padding-right: 50px;

		@media (max-width: @m) {
			flex-direction: row-reverse;
			justify-content: flex-end;
			flex-wrap: wrap;
			padding-right: 55px;
			margin-bottom: 10px;
		}
	}
	.cd-badge {
		flex-shrink: 0;
		width: 30px;
		height: 30px;
		margin: 0 5px 5px 0;
		border-radius: @borderRadius;
		font-size: 0;
		line-height: 0;
		position: relative;
		z-index: 1;
		&:after {
			.pseudo(auto,25px);
			top: 100%;
			left: -20px;
			right: -20px;
			display: none;
			visibility: hidden;
		}
		&.discount {
			background: @red;
		}
		&.new {
			background: #0050a0;
		}
		&.gift {
			background: @yellow;
		}
		&.coupon {
			background: url(/media/images/star.svg) no-repeat;
			background-size: contain;
			background-position: center;
		}
		&.uau {
			background: #f57855;
		}
		&.tooltip-active {
			z-index: 11;
			&:after {
				display: block;
				visibility: visible;
			}
		}

		@media (max-width: @m) {
			width: 20px;
			height: 20px;
			margin: 0 1px 1px 0;
			&:after {
				content: none;
			}
		}
	}
	.cd-badge-tooltip {
		width: auto;
		min-height: 28px;
		white-space: nowrap;
		padding: 5px 10px;
		border-radius: @borderRadius;
		background: @white;
		font-size: 12px;
		line-height: 18px;
		letter-spacing: -0.15px;
		text-align: center;
		position: absolute;
		top: 37px;
		left: 50%;
		transform: translateX(-50%);
		flex-shrink: 0;
		box-shadow: 0 0 20px 0 rgba(0, 34, 67, 0.25);
		z-index: 11;
		&:before {
			.pseudo(7px,7px);
			background: @white;
			top: -3px;
			left: 50%;
			transform: translateX(-50%) rotate(45deg);
		}

		@media (max-width: @m) {
			display: none !important;
		}
	}
	.cd-badge-qty {
		display: flex;
		align-items: center;
		justify-content: center;
		width: auto;
		background: @green2;
		padding: 6px 20px;
		border-radius: @borderRadius;
		font-size: 11px;
		line-height: 1.4;
		text-align: center;
		font-weight: bold;
		color: @white;
		&.supplier {
			background: #f08747;
		}
		&.not-available-in-store {
			background: #f5b800;
		}
		&.not-available {
			background: #adbac4;
		}
		&.preorder {
			background: @blue;
		}

		@media (max-width: @m) {
			padding: 3px 15px;
		}
		@media (max-width: @ms) {
			padding: 3px 10px;
		}
	}

	.cd-ean-box {
		display: flex;
		justify-content: flex-end;
		flex-grow: 1;

		@media (max-width: @m) {
			justify-content: flex-start;
			width: calc(~'100% - -30px');
			margin-left: -15px;
		}
	}
	.cd-ean {
		display: block;
		flex-shrink: 0;
		padding: 5px 10px;
		background: @white;
		border: 1px solid @borderColor;
		border-radius: @borderRadius;
		font-size: 14px;
		line-height: 1.3;
		color: @textColor;
		position: relative;
		.transition(all);
		z-index: 11;
		span {
			padding-left: 25px;
			position: relative;
			&:before {
				.icon-eancode;
				font: 12px/12px @fonti;
				color: @black;
				position: absolute;
				top: 2px;
				left: 0;
			}
		}
		&.longer {
			padding-right: 30px;
			cursor: pointer;
			&:before {
				.icon-arrow-down;
				font: 8px/8px @fonti;
				color: @blue;
				position: absolute;
				top: 10px;
				right: 11px;
				.transition(all);
			}
			&.active {
				background: #f8f9fa;
				border-color: @blue;
				&:before {
					.rotate(180deg);
				}
				.cd-ean-codes {
					max-height: 1000px;
					overflow: unset;
				}
			}
			@media (min-width: @t) {
				&:hover {
					border-color: @blue;
				}
			}
		}

		@media (max-width: @m) {
			width: 100%;
			flex-shrink: unset;
			margin-bottom: -1px;
			padding: 7px 15px;
			border-left: none;
			border-right: none;
			border-radius: 0;
			font-size: 11px;
			span:before {
				top: 0;
			}
		}
	}
	.cd-ean-codes {
		position: absolute;
		top: calc(~'100% - -1px');
		left: -1px;
		right: -1px;
		max-height: 0;
		overflow: hidden;
		.transition(all);
		&:before {
			.pseudo(auto,auto);
			left: 0;
			right: 0;
			bottom: 0;
			top: -32px;
			box-shadow: 0 15px 25px 0 rgba(3, 32, 62, 0.1);
			z-index: -1;
		}
		.cd-ean {
			margin: 0;
			border-top: none;
			border-radius: 0;
		}

		@media (max-width: @m) {
			left: 0;
			right: 0;
		}
	}

	.cd-header {
		display: block;
		margin-bottom: 20px;
		position: relative;

		@media (max-width: @t) {
			display: flex;
			flex-direction: column;
		}
		@media (max-width: @m) {
			width: calc(~'100% - -30px');
			margin: 0 0 0 -15px;
			padding: 12px 15px;
			background: @white;
		}
	}
	.cd-title {
		font-size: 24px;
		line-height: 1.4;
		letter-spacing: -0.4px;
		font-weight: normal;
		padding: 0;
		& > .hidden {
			display: none;
		}

		@media (max-width: @l) {
			font-size: 20px;
		}
		@media (max-width: @t) {
			font-size: 18px;
		}
		@media (max-width: @m) {
			font-size: 14px;
			font-weight: bold;
		}
	}
	.cd-info {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 5px;
		font-size: 14px;
		line-height: 1.4;

		@media (max-width: @t) {
			margin-top: 7px;
		}
		@media (max-width: @m) {
			font-size: 11px;
		}
	}
	.cd-code .hidden {
		display: none;
	}
	.cd-category {
		padding-left: 9px;
		margin-left: 8px;
		position: relative;
		&:before {
			.pseudo(1px,12px);
			left: 0;
			top: 3px;
			background: #ccd8e2;
		}
		a {
			color: @blue;
			@media (min-width: @t) {
				&:hover {
					text-decoration: underline;
				}
			}
		}

		@media (max-width: @m) {
			&:before {
				height: 9px;
			}
		}
	}
	.cd-logistic{
		padding-left: 9px;
		margin-left: 8px;
		margin-right: 12px;
		text-transform: uppercase;
		font-weight: bold;
		position: relative;
		&:before {
			.pseudo(1px,12px);
			left: 0;
			top: 3px;
			background: #ccd8e2;
		}

		@media (max-width: @m) {
			&:before {
				height: 9px;
			}
		}
		@media (max-width: @ms){margin-right: 0;}
	}
	.cd-condition-wrapper{
		@media (max-width: @ms){width: 100%;}
	}
	.cd-condition{
		display: inline-flex; align-items: center; justify-content: center; min-height: 25px; margin: 3px 0; padding: 2px 12px; background: #CDD700; border-radius: 13px; font-size: 12px; line-height: 1.3; color: #0050A0; font-weight: 600; cursor: pointer;
		span{
			display: flex; align-items: center; padding: 0 14px 0 0; position: relative;
			&:before{content: none;}
			&:after{.icon-arrow-down(); font: 9px/1 @fonti; color: #0050A0; position: absolute; right: 0; .rotate(-90deg); .transition(all);}
		}
		@media (min-width: @t){
			&:hover span:after{right: -3px;}
		}
		
		@media (max-width: @ms){min-height: 22px; margin: 10px 0 0; padding: 2px 10px; font-size: 11px;}
	}

	.cd-info-extra {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 18px;

		@media (max-width: @m) {
			width: calc(~'100% - -30px');
			margin: 12px 0 -1px -15px;
			border-top: 1px solid @borderColor;
		}
	}
	.cd-info-container {
		display: flex;
		align-items: center;
		justify-content: center;
		min-height: 30px;
		margin-right: -1px;
		padding: 5px 12px;
		background: @white;
		border: 1px solid @borderColor;
		font-size: 12px;
		letter-spacing: -0.26px;
		line-height: 1.2;
		font-weight: bold;
		white-space: nowrap;
		span {
			padding-left: 19px;
			position: relative;
			&:before {
				.icon-seller;
				font: 15px/1 @fonti;
				color: @blue;
				position: absolute;
				left: 0;
				top: -1px;
			}
		}

		@media (max-width: @m) {
			margin: 0;
			border: none;
			border-right: 1px solid @borderColor;
			&:last-child {
				border: none;
			}
		}
	}
	.cd-seller-info {
		color: @blue;
		&:hover {
			text-decoration: underline;
		}
	}

	.cd-header-extra {
		display: none;

		@media (max-width: @t) {
			display: flex;
			width: calc(~'100% - -90px');
			margin: 0 0 25px -45px;
			background: @white;
			border-top: 1px solid @borderColor;
			border-bottom: 1px solid @borderColor;
		}
		@media (max-width: @m) {
			width: calc(~'100% - -30px');
			margin: 0 0 15px -15px;
		}
	}
	.cd-header-extra-col1 {
		@media (max-width: @t) {
			flex-grow: 1;
			padding: 25px 25px 25px 45px;
			position: relative;
		}
		@media (max-width: @m) {
			padding: 15px;
		}
	}
	.cd-header-extra-col2 {
		@media (max-width: @t) {
			width: 35%;
			flex-shrink: 0;
			padding: 25px 45px 25px 25px;
			border-left: 1px solid @borderColor;
		}
		@media (max-width: @m) {
			padding: 15px;
		}
	}

	.cd-col1-box {
		width: 100%;
		padding: 27px 35px;
		background: @white;
		border: 1px solid @borderColor;
		border-radius: @borderRadius;

		@media (max-width: @l) {
			padding: 25px;
		}
		@media (max-width: @m) {
			padding: 15px 20px;
		}
	}

	.cd-gift-section {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20px;
		padding-right: 100px;
		position: relative;
		&:before {
			.pseudo(50px,50px);
			border-radius: 100%;
			background: @yellow;
			.icon-gift;
			font: 24px/24px @fonti;
			color: @white;
			right: 35px;
			top: 50%;
			transform: translateY(-50%);
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
		}
		&.active {
			.cd-gift-img:before {
				opacity: 1;
			}
			.cd-gift-img-zoom {
				max-height: unset;
				opacity: 1;
			}
		}

		@media (max-width: @l) {
			align-items: flex-start;
			padding-right: 80px;
			&:before {
				width: 40px;
				height: 40px;
				font-size: 18px;
				line-height: 18px;
				right: 25px;
			}
		}
		@media (max-width: @m) {
			margin-bottom: 15px;
			padding: 15px 20px 20px 15px;
			&:before {
				width: 30px;
				height: 30px;
				font-size: 14px;
				line-height: 14px;
				right: unset;
				top: 7px;
				left: 7px;
				transform: none;
				z-index: 1;
			}
			&.no-image {
				padding-left: 60px;
				&:before {
					left: 15px;
					top: 15px;
				}
			}
		}
	}
	.cd-gift-img-zoom {
		display: flex;
		align-items: center;
		justify-content: center;
		background: @white;
		position: absolute;
		top: -1px;
		left: 110px;
		right: -1px;
		box-shadow: 0 10px 30px 0 rgba(0, 34, 67, 0.25);
		z-index: 11;
		max-height: 0;
		opacity: 0;
		overflow: hidden;
		.transition(opacity);
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}

		@media (max-width: @l) {
			left: 100px;
		}
		@media (max-width: @t) {
			display: none;
		}
	}
	.cd-gift-img {
		width: 60px;
		height: 60px;
		margin-right: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		position: relative;
		&:before {
			.pseudo(10px,10px);
			background: @white;
			right: -16px;
			top: 50%;
			transform: rotate(45deg) translateY(-50%);
			z-index: 11;
			opacity: 0;
			.transition(opacity);
		}
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}

		@media (max-width: @l) {
			margin-right: 15px;
		}
	}
	.cd-gift-cnt {
		font-size: 14px;
		line-height: 1.4;
		letter-spacing: -0.3px;
		p {
			padding: 0;
		}

		@media (max-width: @m) {
			font-size: 12px;
			letter-spacing: normal;
		}
	}
	.cd-gift-desc {
		padding-top: 3px;
	}

	.cd-btn-add {
		min-width: 160px;
		min-height: 54px;
		flex-grow: 1;
		margin-left: 10px;
		padding: 0 35px;
		font-size: 14px;
		border-radius: 27px;
		font-weight: bold;
		box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);
		position: relative;
		.transition(box-shadow);
		.price-special {
			color: rgba(255, 255, 255, 0.65);
		}
		&.hidden {
			display: none;
		}
		.hidden {
			display: none;
		}
		& > span {
			position: relative;
			padding-left: 35px;
			flex-shrink: 0;
			&:before {
				.icon-cart-empty;
				font: 21px/21px @fonti;
				color: @white;
				position: absolute;
				left: 0;
			}
		}
		&:hover {
			box-shadow: none;
		}

		@media (max-width: @m) {
			min-width: 120px;
			min-height: 40px;
			margin-left: 8px;
			padding: 0 25px;
			font-size: 12px;
			border-radius: 22px;
			box-shadow: none;
			& > span {
				padding-left: 28px;
				&:before {
					font-size: 19px;
					line-height: 18px;
				}
			}
			&:hover {
				box-shadow: none;
			}
		}
	}

	.cd-attributes-section {
		display: block;
		position: relative;
	}
	.cd-attributes-header {
		display: flex;
		align-items: center;
		padding-bottom: 15px;

		@media (max-width: @l) {
			padding-bottom: 10px;
		}
		@media (max-width: @m) {
			padding-bottom: 15px;
		}
	}
	.cd-attributes-title {
		padding: 0;
		flex-grow: 1;
		padding-right: 30px;
	}
	.cd-attributes-edit {
		width: 20px;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		position: relative;
		&:before {
			.icon-edit;
			font: 16px/16px @fonti;
			color: @blue;
		}
	}
	.cd-attributes {
		font-size: 14px;
		line-height: 1.5;
		letter-spacing: -0.2px;
		display: flex;
		justify-content: space-between;
		padding: 10px 15px;
		.cd-attributes-title,
		.cd-attributes-desc {
			width: 50%;
		}
		.cd-attributes-title {
			padding-right: 10px;
		}
		.cd-attributes-desc {
			text-align: right;
			padding-left: 10px;
		}

		@media (max-width: @m) {
			font-size: 12px;
			padding: 9px 10px;
		}
	}
	.cd-attributes:nth-child(odd) {
		background: #f0f2f4;
	}

	.cd-desc {
		max-width: var(--contentWidth);
		overflow: hidden;

		@media (max-width: @t) {
			max-width: unset;
			overflow: unset;
			padding: 0;
			&.active {
				.cd-desc-title:after {
					opacity: 0;
				}
				.cd-desc-content {
					max-height: unset;
					padding-bottom: 25px;
					&.state{
						margin: 0 15px 15px; padding: 10px;
					}
				}
			}
		}
		@media (max-width: @m) {
			&.active .cd-desc-content {
				padding-bottom: 15px;
			}
		}
	}
	.cd-desc-header {
		@media (max-width: @t) {
			padding: 25px;
		}
		@media (max-width: @m) {
			padding: 14px 20px;
		}
	}
	.cd-desc-title {
		@media (max-width: @t) {
			padding-left: 22px;
			position: relative;
			&:before,
			&:after {
				.pseudo(12px,2px);
				background: @blue;
				left: 0;
				top: 9px;
				z-index: 1;
				.transition(opacity);
			}
			&:after {
				width: 2px;
				height: 12px;
				top: 4px;
				left: 5px;
			}
		}
	}

	.cd-warehouses {
		@media (max-width: @t) {
			padding: 0;
			&.active {
				.cd-desc-title:after {
					opacity: 0;
				}
				.cd-warehouses-items {
					max-height: unset;
					padding-bottom: 15px;
				}
			}
		}
	}
	.cd-warehouses-items {
		padding-top: 15px;

		@media (max-width: @t) {
			padding: 0 25px;
			max-height: 0;
			overflow: hidden;
			transition: max-height 0.3s, padding 0.3s;
		}
	}
	.cd-warehouses-item {
		display: flex;
		align-items: center;
		flex-grow: 1;
		position: relative;
		padding: 10px 15px;
		&:nth-child(odd) {
			background: #f0f2f4;
		}

		@media (max-width: @m) {
			padding: 10px;
		}
	}
	.cd-warehouses-title {
		display: block;
		flex-grow: 1;
		font-size: 14px;

		@media (max-width: @m) {
			font-size: 12px;
		}
	}
	.cd-warehouses-qty {
		font-size: 12px;
		line-height: 1.3;
		margin-left: 15px;
		padding-left: 18px;
		position: relative;
		&:before {
			.icon-check;
			font: 11px/11px @fonti;
			color: @green;
			position: absolute;
			left: 0;
			top: 1px;
		}
		&.last:before {
			.icon-danger-white;
			font: 12px/12px @fonti;
			color: @yellow;
			top: 0;
		}
		&.unavailable:before {
			.icon-x;
			font: 13px/13px @fonti;
			color: #a9afb4;
			top: 0;
		}
	}

	.cd-col2 {
		display: block;
		width: 620px;
		flex-shrink: 0;
		padding: 20px 0 0 50px;

		@media (max-width: @l) {
			width: 480px;
			padding-top: 0;
			padding-left: 30px;
		}
		@media (max-width: @t) {
			width: 100%;
			margin: 0;
			padding: 45px;
			flex-shrink: unset;
			position: relative;
		}
		@media (max-width: @m) {
			padding: 25px 15px;
		}
	}

	.cd-badge-coupon {
		margin: 1px 0;

		@media (max-width: @t) {
			margin: 10px 0 0;
		}
	}

	.cd-price {
		display: flex;
		align-items: flex-end;
		flex-wrap: wrap;
		position: relative;
		&.uau-badge {
			.cd-current-price:not(.blue),
			.cd-installments-price :deep(span) {
				color: #f57855;
			}
		}

		@media (max-width: @m) {
			display: block;
		}
	}
	.cd-price-row1 {
		flex-grow: 1;
	}
	.cd-price-cnt {
		display: flex;
		flex-wrap: wrap;
		align-items: baseline;
	}
	.cd-old-price {
		width: 100%;
		font-size: 14px;

		@media (max-width: @m) {
			font-size: 12px;
			margin: 0 0 1px;
		}
	}
	.cd-current-price {
		font-size: 24px; font-weight: bold; display: flex; align-content: center; gap: 15px;
		&.hidden {
			display: none;
		}

		@media (max-width: @ms){
			flex-direction: column; gap: 5px;
		}
		@media (max-width: @m) {
			font-size: 16px;
		}
	}
	.cd-price-badge{
		background: #CDD700; font-weight: normal; font-size: 13px; line-height: 1; padding: 7px 9px; align-self: center; position: relative; border-radius: 2px;
		@media (max-width: @t){font-size: 11px;}
		@media (max-width: @ms){margin-bottom: 5px;}
		strong{font-size: 14px;
			@media (max-width: @t){font-size: 11px;}
		}
		&:before{
			.pseudo(6px,6px); .rotate(45deg); background: #CDD700; top: calc(~'50% - 3px'); left: -3px;
			@media (max-width: @ms){display: none;}
		}
	}	
	.cd-installments-price {
		font-size: 14px;
		font-weight: normal;
		position: relative;
		width: 100%;
		&.hidden {
			display: none;
		}

		@media (max-width: @m) {
			font-size: 12px;
		}
	}
	.cd-discount-expire {
		font-size: 12px;
		padding-top: 5px;

		@media (max-width: @t) {
			display: block;
			padding-top: 3px;
		}
		@media (max-width: @m) {
			font-size: 11px;
		}
	}

	.cd-installments-note {
		display: inline-block;
		margin: 10px auto 0;
		font-size: 14px;
		line-height: 1.4;
		flex-shrink: 0;
		position: relative;
		&.hidden {
			display: none;
		}

		@media (max-width: @m) {
			font-size: 12px;
		}
	}
	.cd-installments-note-title {
		padding-left: 33px;
		position: relative;
		display: inline-flex;
		align-items: center;
		&:before {
			.icon-credit-card;
			font: 16px/17px @fonti;
			color: @blue;
			position: absolute;
			left: 0;
		}

		@media (max-width: @m) {
			padding-left: 27px;
			&:before {
				font-size: 13px;
				line-height: 14px;
			}
		}
	}
	.cd-installments-note-close {
		display: none;
		align-items: center;
		justify-content: center;
		width: 30px;
		height: 30px;
		flex-shrink: 0;
		background: @red;
		border: 2px solid @white;
		border-radius: 100%;
		position: absolute;
		top: -12px;
		right: -12px;
		box-shadow: 5px 8px 20px 0 rgba(3, 32, 62, 0.2);
		&:before {
			.icon-x;
			font: 12px/12px @fonti;
			color: @white;
			position: absolute;
		}

		@media (max-width: @t) {
			display: flex;
		}
	}
	.cd-installments-note-tooltip {
		width: 350px;
		background: @white;
		padding: 17px 25px;
		border-radius: @borderRadius;
		font-size: 13px;
		line-height: 1.5;
		font-weight: normal;
		text-align: left;
		color: @textColor;
		position: absolute;
		top: calc(~'100% - -15px');
		left: -25px;
		box-shadow: 0 5px 25px 0 rgba(1, 61, 112, 0.4);
		z-index: 111;
		.transition(opacity);
		&:before {
			.pseudo(10px,10px);
			background: @white;
			left: 33px;
			top: -4px;
			.rotate(45deg);
		}
		&:after {
			.pseudo(auto,20px);
			left: 0;
			right: 0;
			top: -20px;
			opacity: 0;
		}

		@media (max-width: @m) {
			width: 310px;
			padding: 15px;
			font-size: 12px;
			top: calc(~'100% - -10px');
			left: -5px;
			z-index: 11;
			&:before {
				width: 8px;
				height: 8px;
				left: 11px;
				top: -3px;
			}
			&:after {
				content: none;
			}
		}
	}
	.cd-installments-tooltip-logo {
		width: 75px;
		height: 22px;
		display: block;
		margin-bottom: 12px;
		img {
			width: auto;
			height: auto;
			max-width: 75px;
			max-height: 22px;
			display: block;
		}

		@media (max-width: @m) {
			width: 55px;
			height: 18px;
			margin-bottom: 10px;
			img {
				max-width: 55px;
				max-height: 18px;
			}
		}
	}
	.cd-installments-tooltip-title {
		display: block;
		font-weight: bold;
		padding-bottom: 4px;
	}
	.cd-installments-tooltip-table {
		display: block;
		margin-bottom: 10px;
	}

	.cd-payments-options{
		display: block; margin-top: 10px; font-size: 12px; line-height: 1.4;

		@media (max-width: @m){font-size: 11px;}
	}
	.cd-payments-options-title{display: block; margin-bottom: 3px; font-weight: bold;}
	.cd-payments-option{
		display: block; margin-bottom: 2px;
		&:last-child{margin-bottom: 0;}
	}

	.cd-shipping-options{
		display: flex; align-items: center; flex-wrap: wrap; margin-top: 10px;

		@media (max-width: @m){display: block;}
	}
	.cd-shipping-option{
		display: flex; align-items: center; margin: 5px 7px 0 0; padding-right: 8px; font-size: 12px; position: relative;
		&:before{.pseudo(1px,12px); background: @gray; position: absolute; right: 0;}
		&:last-child{
			margin-right: 0; padding-right: 0;
			&:before{content: none;}
		}
		.value{text-transform: lowercase;}

		@media (max-width: @m){
			margin: 3px 0 0; padding-right: 0; font-size: 11px;
			&:before{content: none;}
		}
	}

	.cd-energy-info {
		position: relative;

		@media (max-width: @m) {
			margin-top: 15px;
		}
	}
	.cd-energy {
		height: 25px;
		display: inline-flex;
		align-items: center;
		flex-shrink: 0;
		margin: 15px 0 0;
		font-size: 14px;
		line-height: 1.6;
		color: @textColor;
		text-decoration: none;
		&.link {
			cursor: pointer;
		}
		&:hover {
			color: @textColor;
		}

		@media (max-width: @t) {
			margin-top: 20px;
		}
		@media (max-width: @m) {
			height: 20px;
			margin: 0;
			font-size: 12px;
		}
	}

	.cd-add-to {
		width: 750px;
		border-top: 1px solid @borderColor;
		position: fixed;
		bottom: 0;
		right: 0;
		z-index: 111;

		@media (max-width: @l) {
			width: 610px;
		}
		@media (max-width: @t) {
			width: 70%;
			border: none;
			bottom: 0;
			right: 0;
		}
		@media (max-width: @m) {
			width: auto;
			background: @white;
			border-top: 1px solid @borderColor;
			bottom: 50px;
			left: 0;
			right: 0;
		}
	}
	.cd-add-to-section {
		display: flex;
		align-items: center;
		padding: 25px 130px 25px 50px;
		background: @white;
		position: relative;
		&:before {
			.pseudo(auto,25px);
			left: 25px;
			right: 25px;
			top: 0;
			box-shadow: 0 0 40px 0 rgba(3, 32, 62, 0.25);
			z-index: -1;
		}

		@media (max-width: @l) {
			padding-left: 30px;
		}
		@media (max-width: @t) {
			padding: 9px 15px;
		}
	}
	.add-to-cart-container {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		flex-grow: 1;
		position: relative;
	}

	.cd-not-available {
		font-size: 14px;
		margin-right: 15px;

		@media (max-width: @m) {
			margin: 0;
			font-size: 14px;
			text-align: center;
			flex-grow: 1;
		}
	}
</style>

<style lang="less">
	.page-catalog-detail {
		.page-wrapper {
			overflow: unset;
		}

		@media (max-width: @t) {
			.content-section {
				padding: 0 0 80px;
			}
		}
		@media (max-width: @m) {
			.main-header {
				left: 47px;
			}
			.content-section {
				padding: 0 0 60px;
			}
			.sw-form .ui-menu {
				left: -47px !important;
			}
		}
	}
	.cd-desc-content {
		font-size: 14px;
		h2,
		h3,
		h4 {
			padding-top: 20px;
		}
		ul {
			list-style: none;
			padding: 0;
			margin: 0 0 15px 20px;
			li {
				position: relative;
				padding: 2px 0 4px 25px;
				&:before {
					.pseudo(12px, 1px);
					background: @blue;
					top: 14px;
					left: 0;
					z-index: 1;
				}
			}

			@media (max-width: @m) {
				margin: 0 0 15px 0;
				font-size: 14px;
				li {
					padding: 2px 0 2px 20px;
					&:before {
						width: 10px;
						top: 11px;
						left: 0;
					}
				}
			}
		}
		ol {
			margin: 0 0 15px 38px;
			li {
				margin: 0 0 5px;
			}
		}
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}
		iframe {
			width: 100%;
		}
		figure {
			margin-bottom: 10px;
		}
		figcaption {
			padding: 10px 0 0;
		}
		.btn,
		input[type='submit'],
		button {
			min-width: 180px;
			margin-bottom: 5px;
		}
		table {
			display: block;
			width: 100% !important;
			border-spacing: 0;
			margin: 10px 0px 20px;
			th {
				font-weight: bold;
				font-size: 14px;
				text-align: left;
				padding: 6px 0;
				border-bottom: 1px solid @gray;
			}
			td {
				border-bottom: 1px solid @gray;
				padding: 6px 0;
			}
			&.stripe tbody tr:nth-child(even) {
				background: #e9e9e9;
			}
		}

		&.state{
			margin-bottom: 20px; padding: 15px; background: #f0f2f4; transition: none;
			h4{font-size: 16px; padding: 0 0 15px;}
		}

		@media (max-width: @t) {
			padding: 0 25px;
			max-height: 0;
			overflow: hidden;
			transition: max-height 0.3s, padding 0.3s;
		}
		@media (max-width: @m) {
			padding: 0 20px;
			font-size: 12px;
			ul {
				font-size: 12px;
				li:before {
					top: 10px;
				}
			}
			ol {
				margin: 0 0 15px 20px;
			}
			.btn,
			input[type='submit'],
			button {
				width: 100%;
			}

			&.state{
				margin-bottom: 0; padding: 0;
				h4{font-size: 14px; padding: 0 0 10px;}
			}
		}
	}

	.cd-installments-price {
		span {
			font-weight: bold;
		}
		p {
			padding: 0;
		}
	}
	.cd-installments-note-tooltip {
		a {
			color: @blue;
			text-decoration: underline;
		}
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
		}
	}

	.cd-energy {
		span {
			margin-left: 12px;
		}
		img {
			width: auto;
			height: auto;
			max-width: 100%;
			max-height: 100%;
			display: block;
		}

		@media (max-width: @m) {
			span {
				display: none;
			}
		}
	}

	.cd-qty-container.single {
		display: none;
	}
	.cd-qty {
		display: flex;
		align-items: center;
		flex-shrink: 0;
		width: 110px;
		background: @white;
		border: 1px solid @borderColor;
		border-radius: 27px;
		box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);

		@media (max-width: @m) {
			width: 90px;
			border-radius: 22px;
			box-shadow: 0 5px 20px 0 rgba(3, 32, 62, 0.1);
		}
	}
	.cd-btn-qty {
		width: 40%;
		height: 54px;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		flex-shrink: 0;
		cursor: pointer;
		&:before,
		&:after {
			.pseudo(14px,2px);
			background: @blue;
		}
		&:after {
			.pseudo(2px,14px);
			left: 50%;
			transform: translateX(-50%);
		}

		@media (max-width: @m) {
			height: 40px;
			&:before {
				width: 12px;
			}
			&:after {
				height: 12px;
			}
		}
	}
	.cd-btn-dec:after {
		content: none;
	}
	.cd-input-qty {
		width: 30%;
		border: none;
		text-align: center;
		padding: 0;
		height: 54px;
		font-size: 16px;

		@media (max-width: @m) {
			height: 40px;
			font-size: 12px;
		}
	}
	.cd-compare-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		height: 54px;
		margin-right: 10px;
		padding: 0 35px;
		border: 1px solid @borderColor;
		border-radius: 27px;
		background: @white;
		box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);
		position: relative;
		.transition(box-shadow);
		.cd-compare-remove {
			display: none;
		}
		&.compare_active {
			.cd-compare-add {
				display: none;
			}
			.cd-compare-remove {
				display: block;
			}
		}
		@media (min-width: @t) {
			&:hover {
				box-shadow: none;
			}
		}

		@media (max-width: @l) {
			width: 54px;
			padding: 0;
			border-radius: 100%;
			&:before {
				.icon-compare;
				font: 20px/20px @fonti;
				color: @blue;
				position: absolute;
				.transition(color);
			}
			&.compare_active:before {
				color: @black;
			}
		}
		@media (max-width: @m) {
			width: 44px;
			height: 44px;
			margin-right: 13px;
			&:before {
				font-size: 17px;
				line-height: 17px;
			}
		}
	}
	.cd-compare-icon {
		padding-left: 27px;
		font-size: 14px;
		line-height: 1.4;
		font-weight: 500;
		position: relative;
		&:before {
			.icon-compare;
			font: 20px/20px @fonti;
			color: @blue;
			position: absolute;
			top: -1px;
			left: 0;
		}

		@media (max-width: @l) {
			display: none !important;
		}
	}
	.cd-compare-info {
		padding: 5px 20px;
		background: @white;
		border: 1px solid @borderColor;
		border-radius: 27px;
		position: absolute;
		left: 0;
		bottom: calc(~'100% - -10px');
		font-size: 12px;
		color: @green;
		white-space: nowrap;
		text-align: center;
		z-index: 11;
		&:before {
			.pseudo(8px,8px);
			background: @white;
			border-left: 1px solid @borderColor;
			border-bottom: 1px solid @borderColor;
			bottom: -5px;
			left: 39px;
			.rotate(-45deg);
		}
		a {
			color: @blue;
			text-decoration: underline;
			&:hover {
				text-decoration: none;
			}
		}

		@media (max-width: @l) {
			left: -18px;
		}
		@media (max-width: @m) {
			&:before {
				left: 34px;
			}
		}
	}
</style>
