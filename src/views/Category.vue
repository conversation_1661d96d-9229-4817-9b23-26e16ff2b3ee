<template>
	<div class="c-header">
		<h1 class="c-title" v-if="pageTitle">{{ pageTitle }}</h1>
		<Toolbar />
	</div>

	<div class="c-row">
		<div class="c-filters-section">
			<div class="cf">
				<div class="ci-categories" :class="{active: !mobileBp}" v-if="categories">
					<div class="ci-title cf-title" v-if="categories?.title">{{ categories.title }}</div>
					<SidebarCategories :items="categories" :current="category.position_h" level="0" />
				</div>

				<Filter v-for="filter in filters" :key="filter.id" :filter="filter" :searchPage="route.query.search_q" @filter="onFilter()" />
			</div>
		</div>

		<div class="c-items-section">
			<ActiveFilters v-if="activeFilters && activeFilters.selected?.length" :data="activeFilters" @remove="onFilter()" />
			<div class="fz0 c-items" :class="{loading: productsLoader}">
				<IndexEntry v-for="item in products" :key="item.id" :item="item" />
			</div>
			<LoadMore @loadmore="loadMore" :class="{loading: productsLoader}" autoload="2" :pagination="pagination" />
			<div v-if="!productsLoader && !products.length" class="c-empty">{{ store.labels.no_products }}</div>
		</div>
	</div>
</template>

<script setup>
	import {useRoute, useRouter} from 'vue-router';
	import {useStore} from '@/stores';
	import {useCatalogStore} from '@/stores/catalog';
	import {watch, ref, computed} from 'vue';
	import {useEventBus} from '@/composables/useEventBus';
	import {useImages} from '@/composables/useImages';
	import IndexEntry from '@/components/catalog/IndexEntry.vue';
	import Toolbar from '@/components/catalog/widget/Toolbar.vue';
	import LoadMore from '@/components/catalog/widget/LoadMore.vue';
	import SidebarCategories from '@/components/catalog/widget/SidebarCategories.vue';
	import Filter from '@/components/catalog/widget/Filter.vue';
	import ActiveFilters from '@/components/catalog/widget/ActiveFilters.vue';

	const route = useRoute();
	const router = useRouter();
	const store = useStore();
	const {generateThumbs} = useImages();
	const catalogStore = useCatalogStore();
	const products = ref([]);
	const newProducts = ref([]);
	const filters = ref([]);
	const activeFilters = ref([]);
	const category = ref([]);
	const productsLoader = ref(1);
	const pagination = ref({});
	const {bus} = useEventBus();

	// breakpoints
	import {breakpoints} from '@/composables/useBreakpoints';
	const mobileBp = breakpoints('m');

	const pageTitle = computed(() => {
		let title = 'Izdelki';
		if (route.query?.search_q) title = route.query.search_q;
		if (category.value?.title) title = category.value.title;
		document.title = title;
		return title;
	});

	function loadMore(page) {
		getProducts({page});
	}

	// current category data
	async function getCategory() {
		products.value = [];
		if (route.params.category) {
			await catalogStore
				.fetchCategory({
					slug: route.params.category.join('/'),
					mode: 'full',
				})
				.then(res => {
					//console.log(res);
					category.value = res.data[0];
				});
		}
	}

	// subcategories
	const categories = computed(() => {
		if (!route.query.search_q) {
			const catPosition = category.value.position_h;
			let rootCategory = catPosition && catPosition.includes('.') ? catPosition.split('.')[0] : catPosition;

			return catalogStore.categories.find(el => {
				return el.position_h == rootCategory;
			});
		}

		return null;
	});

	// products
	async function getProducts(options = {}) {
		productsLoader.value = 1;

		// filter
		if (route.query) {
			Object.entries(route.query).forEach(el => {
				const value = typeof el[1] != 'string' ? el[1].join(',') : el[1];
				if (el[0] != 'to_page') options[el[0]] = value;
			});
		}

		// options
		if (options.to_page) options.to_page = options.to_page;
		if (options.page) options.page = options.page;
		options.mode = 'index';
		if (!route.query.search_q && category.value.id) {
			options.category_position = category.value.position_h;
			options._category_id = category.value.id;
			options._search_id = category.value.search_id ? category.value.search_id : 1;
		}
		if (route.query.search_q) {
			options.search_q = route.query.search_q;
			options._search_id = true;
		}
		if (route.query.sort) options.sort = route.query.sort;
		if (route.query.with_qty) options.with_qty = route.query.with_qty;
		if (route.query.discount) options.discount = route.query.discount;

		// get products
		await catalogStore.fetchProducts(options).then(async res => {
			pagination.value = res.data?.meta_data?.pagination || {};
			newProducts.value = res.data?.items || [];
			filters.value = res.data?.search_fields;
			activeFilters.value = res.data?.search_fields_meta_data;
			if (res.data.items) {
				if (options.filter) {
					products.value = res.data.items;
				} else {
					res.data.items.forEach(el => {
						products.value.push(el);
					});
				}
			}

			productsLoader.value = 0;
			store.loading = 0;

			//generate thumbs
			await generateThumbs({
				data: newProducts.value,
				preset: 'catalogEntry',
			});
		});
	}

	// initial data
	async function loadData() {
		store.loading = 1;
		await getCategory();

		const options = {};
		if (route.query.to_page) options.to_page = route.query.to_page;
		await getProducts(options);
	}
	loadData();

	// If category url is changed, reload all data
	watch(
		() => route.path,
		async (newPath, oldPath) => {
			if (route.name === 'category' || route.name === 'products' || route.name === 'search') {
				await loadData();
			}
		}
	);

	// if url query is changed (filter), reload products
	function onFilter() {
		setTimeout(async () => {
			await getProducts({filter: true});
		}, 300);
	}

	// reload products if sort or search term is changed
	watch(
		() => bus.value,
		() => {
			if (['search', 'filter'].includes(bus.value.event)) onFilter();
		}
	);
</script>

<style lang="less">
	.c-items-section {
		position: relative;
	}
	.c-items.loading {
		position: relative;
		&:before {
			.pseudo(auto,auto);
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
			background: rgba(255, 255, 255, 0.5) url(/media/images/loader.svg) no-repeat center top 50px;
			background-size: 50px auto;
			z-index: 100;
		}
	}

	.fade-enter-active,
	.fade-leave-active {
		transition: opacity 0.3s ease;
	}

	.fade-enter-from,
	.fade-leave-to {
		opacity: 0;
	}
</style>
