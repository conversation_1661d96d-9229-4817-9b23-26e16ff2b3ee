<template>
	<template v-if="webshopStore.carts">
		<div v-if="webshopStore.cart && webshopStore.cart.label_name" class="global-error" v-html="store.labels[webshopStore.cart.label_name]"></div>
		<div v-else class="w-table" v-for="(cart, index) in webshopStore.carts" :key="cart.id">
			<ShoppingCartEntry :cart="cart" :index="index" />
		</div>
	</template>
	<template v-else>
		<div class="empty-cart" v-html="store.labels.empty_shopping_cart" />
	</template>

	<template v-if="!webshopStore.itemSelected.length">
		<div class="floating-nav">
			<ResetShipping />
			<AddService />
			<QuickAddToCart />
			<QuickAddCart />
			<AddCart />
		</div>
	</template>

	<Modal @close="closeMessage()" :openValue="webshopStore.successMessage ? (modalStatus = 1) : (modalStatus = 0)">
		<div class="w-cart-success-message">
			{{ store.labels[webshopStore.successMessage.label_name] }}
			<div v-if="webshopStore.successMessage.first_order_id">
				{{ store.labels.pa_cart_success_order_id }} <strong>{{ webshopStore.successMessage.first_order_id }}</strong>
			</div>
		</div>
	</Modal>

	<Modal :openValue="webshopStore.updateCart.length || bus.event == 'salesmanPriceModal'" mode="updateCartView">
		<div class="w-cart-update-tooltip">
			<div class="w-cart-update-tooltip-header">
				<span v-if="bus.event == 'salesmanPriceModal'">{{ store.labels[bus.data.item.label_name] ? store.labels[bus.data.item.label_name] : bus.data.item.label_name }}</span>
				<span v-else-if="webshopStore.updateCart.length && webshopStore.updateCart[0].label_name == 'error_order_with_given_cart_code_already_exists'">{{ store.labels.cart_update_error_order_already_exists }}</span>
				<span v-else>{{ store.labels.cart_update_error }}</span>
			</div>
			<div class="w-cart-update-tooltip-body">
				<template v-if="bus.event == 'salesmanPriceModal'">
					<template v-if="bus.data?.item && bus.data.item.label_name && bus.data.item.label_name == 'error_attachment_required'">{{ store.labels.pa_salesman_price_error_attachment_modal }}</template>
					<template v-else>{{ store.labels.pa_salesman_price_error_modal }}</template>
				</template>
				<template v-else>{{ store.labels.cart_update_title }}</template>
				<div class="w-cart-update-tooltip-btns">
					<template v-if="bus.event == 'salesmanPriceModal'">
						<div @click="bus.event = null" class="btn btn-white btn-modal">{{ store.labels.cancel }}</div>
						<div @click="updateSalesmanPrice(bus.data.shopping_cart_code)" class="btn btn-modal">{{ store.labels.pa_salesman_price_enter_again }}</div>
					</template>
					<template v-else-if="webshopStore.updateCart.length && webshopStore.updateCart[0].label_name == 'error_order_with_given_cart_code_already_exists'">
						<div @click="closeCartValues()" class="btn btn-white btn-modal">{{ store.labels.cart_update_order_already_exists_close_btn }}</div>
						<div @click="updateCartValues('restore', 'already_exist')" class="btn btn-modal">{{ store.labels.cart_update_order_already_exists_btn }}</div>
						<div class="btn-special"><span @click="updateCartValues('keep_current', 'already_exist')">{{ store.labels.cart_update_order_already_exists_not_btn }}</span></div>
					</template>
					<template v-else>
						<div @click="updateCartValues('keep_current')" class="btn btn-white btn-modal">{{ store.labels.cart_update_not_btn }}</div>
						<div @click="updateCartValues('update')" class="btn btn-modal">{{ store.labels.cart_update_btn }}</div>
					</template>
				</div>
			</div>
		</div>
	</Modal>
</template>

<script setup>
	import {onMounted, onUnmounted, ref, provide} from 'vue';
	import {useRoute} from 'vue-router';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useToken} from '@/composables/useToken';
	import {useFetch} from '@/composables/useFetch';
	import {useEventBus} from '@/composables/useEventBus';
	import {useEndpoints} from '@/composables/useEndpoints';
	import {useConfig} from '@/composables/useConfig';
	import AddCart from '@/components/webshop/widget/AddCart.vue';
	import AddService from '@/components/webshop/widget/AddService.vue';
	import ResetShipping from '@/components/webshop/widget/ResetShipping.vue';
	import ShoppingCartEntry from '@/components/webshop/ShoppingCartEntry.vue';
	import QuickAddToCart from '@/components/webshop/widget/QuickAddToCart.vue';
	import QuickAddCart from '@/components/webshop/widget/QuickAddCart.vue';
	import Modal from '@/components/Modal.vue';

	const route = useRoute();
	const store = useStore();
	const token = useToken();
	const ep = useEndpoints();
	const {channel} = useConfig();
	const webshopStore = useWebshopStore();
	const modalStatus = ref();
	const {bus} = useEventBus();
	const messageHandlers = [];

	onMounted(async () => {
		// get page title
		const res = await store.fetchCmsPage(route.path);
		if(res.seo_title) {
			document.title = res.seo_title;
		}
		document.body.classList.add('page-cart');
		window.addEventListener('message', getCustomerId);
		messageHandlers.push(getCustomerId());
	});
	onUnmounted(() => {
		document.body.classList.remove('page-cart');
		window.removeEventListener('message', getCustomerId);
		messageHandlers.forEach(handler => {
			window.removeEventListener('message', handler);
		});
	});

	//set customer data
	provide('openUser', openUser);
	provide('openRecipient', openRecipient);
	
	const paOrigin = (window.location.hostname == 'pa.bigbang.si' || window.location.hostname == 'pabigbangprod.marker') ? 'https://asistent.bigbang.si' : 'https://devasistent.bigbang.si';
	let paUserEl;
	let paRecipientEl;
	let externalUserId = '';
	let externalUserIdRecipient = '';

	async function openUser(cart) {
		let time = 0;

		bus.value = {event: null, data: null};

		if (cart?.cart_active == false) {
			time = 1000;
			store.loading = 2;
			await token.generateToken({
				tokenId: cart?.token_id,
				tokenHash: cart?.token_hash,
			});
			webshopStore.fetchCarts();
			store.loading = 0;
		}

		setTimeout(() => {
			if (webshopStore.cart.customer && webshopStore.cart.customer.api_code) {
				externalUserId = 'user/' + webshopStore.cart.customer.api_code + '/';
			}
			paUserEl = window.open(paOrigin + '/people/?channel='+channel+'#/' + externalUserId, '_blank', 'width=1100,height=900');
			externalUserId = '';
		}, time);
	}

	async function openRecipient(cart, items) {
		let time = 0;
		bus.value = {event: 'recipient', data: items};

		if (cart?.cart_active == false) {
			time = 1000;
			store.loading = 2;

			await token.generateToken({
				tokenId: cart?.token_id,
				tokenHash: cart?.token_hash,
			});
			webshopStore.fetchCarts();
			store.loading = 0;
		}

		setTimeout(() => {
			if (webshopStore.cart.customer && webshopStore.cart.customer.api_code) {
				externalUserIdRecipient = webshopStore.cart.customer.api_code;
			}

			paRecipientEl = window.open(paOrigin + '/people/?channel='+channel+'#/user/' + externalUserIdRecipient + '/', '_blank', 'width=1100,height=900');
			externalUserIdRecipient = null;
		}, time);
	}


	function getCustomerId(event) {
		if (event?.origin !== paOrigin) {
			return;
		}

		if (paUserEl) {
			paUserEl.close();
		}
		if (paRecipientEl) {
			paRecipientEl.close();
		}

		const msg = JSON.parse(event.data);

		if (msg.type === 'customer' && msg.event === 'selected') {
			const addressId = msg.addressId ? msg.addressId : null;
			if(bus.value.event == 'recipient') {
				const shoppingCodes = bus?.value?.data?.map(item => item.shopping_cart_code) || null;
				webshopStore.setRecipient({shopping_cart_codes: shoppingCodes, address_id: addressId});
			} else {
				const Uuid = msg.customerUuid || msg.payload.Uuid || '';
				webshopStore.setCustomer({api_code: Uuid, address_id: addressId});
			}
		}

		bus.value.event == null;
	}

	//close message
	function closeMessage() {
		modalStatus.value = 0;
		webshopStore.successMessage = null;
	}

	//update cart data
	async function updateCartValues(mode, urlName) {
		store.loading = 1;
		const urlEp = urlName == 'already_exist' ? ep.endpoints.value._post_hapi_webshop_current_cart_state : ep.endpoints.value._post_hapi_webshop_latest_cart_state;
		await token.generateToken({
			tokenId: webshopStore.updateCart[0].token_id,
			tokenHash: webshopStore.updateCart[0].token_hash,
		});
		await webshopStore.fetchCarts();
		await useFetch({
			url: urlEp,
			method: 'POST',
			body: {
				token_id: webshopStore.updateCart[0].token_id,
				token_hash: webshopStore.updateCart[0].token_hash,
				cart_code: webshopStore.updateCart[1],
				action: mode,
			},
		}).then(res => {
			webshopStore.fetchCarts();
			webshopStore.updateCart = [];
			store.loading = 0;
		});
	}

	//close popup and clear input
	function closeCartValues() {
		bus.value.event = 'clearCartCode';
		webshopStore.updateCart = [];
	}

	//update salesman price
	function updateSalesmanPrice(value) {
		bus.value.event = null;
		webshopStore.salesmanPriceItem = value;
	}
</script>

<style lang="less" scoped>
	.w-cart-update-tooltip-header {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 6px 6px 0;
		padding: 15px 30px;
		background: @red;
		border-radius: @borderRadius;
		font-size: 14px;
		line-height: 1.4;
		color: @white;
		font-weight: bold;
		span {
			padding-left: 26px;
			position: relative;
			&:before {
				.icon-info;
				font: 18px/1 @fonti;
				color: @white;
				position: absolute;
				left: 0;
			}
		}

		@media (max-width: @m) {
			padding: 12px 20px;
			font-size: 12px;
			span {
				padding-left: 24px;
				&:before {
					font-size: 16px;
				}
			}
		}
	}
	.w-cart-update-tooltip-body {
		padding: 30px 60px 35px;
		font-size: 16px;
		font-weight: bold;
		text-align: center;

		@media (max-width: @m) {
			padding: 20px;
			font-size: 14px;
		}
	}
	.w-cart-update-tooltip-btns {
		display: flex;
		align-items: center;
		justify-content: space-between;
		flex-wrap: wrap;
		margin-top: 20px;
		.btn {
			width: 48%;
			min-height: 50px;
			font-size: 14px;
		}
		.btn-special{
			width: 100%; margin-top: 25px; font-size: 12px; text-align: left;
			span{
				text-decoration: underline; text-underline-offset: 3px; cursor: pointer;
				&:hover{text-decoration: none;}
			}
		}

		@media (max-width: @m) {
			margin-top: 15px;
			.btn {
				min-height: 44px;
				font-size: 13px;
			}
		}
	}

	.w-cart-success-message {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 100px 50px 45px;
		line-height: 1.7;
		text-align: center;
		position: relative;
		&:before {
			.icon-check-special;
			font: 45px/1 @fonti;
			font-weight: 600;
			color: @green;
			position: absolute;
			top: 30px;
		}

		@media (max-width: @m) {
			padding: 17px 15px 15px;
			font-size: 12px;
		}
	}

	.floating-nav {
		position: fixed;
		bottom: 60px;
		right: 50px;
		z-index: 1111;
		display: flex;
		gap: 10px;
		@media (max-width: @t) {
			bottom: 50px;
		}
		@media (max-width: @m) {
			background: #fff;
			bottom: 50px;
			right: 0;
			left: 0;
			height: 55px;
			padding: 0 15px;
			justify-content: space-between;
			align-items: center;
			box-shadow: 0 0 10px 0 rgba(0, 34, 67, 0.2);
		}
	}
</style>
