<template></template>

<script setup>
	import {onMounted} from 'vue';
	import {useRouter} from 'vue-router';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';
	import {useFetch} from '@/composables/useFetch';
	import {useEndpoints} from '@/composables/useEndpoints';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const ep = useEndpoints();
	const router = useRouter();

	const url = window.location.href;
	const searchParams = new URL(url).searchParams;
	if (searchParams.get('identificator')) {
		store.loading = 1;
		const urlId = searchParams.get('identificator');
		const cartId = ep.endpoints.value._get_hapi_copy_cart;
		const cart = cartId.replace('%IDENTIFICATOR%', urlId);

		async function cartCopy() {
			await useFetch({
				url: cart,
				method: 'GET',
			}).then(async res => {
				if (res.success) {
					await webshopStore.fetchCarts();
					store.loading = 0;
					router.push({name: 'shoppingCart'});
				}
			});
		}
		cartCopy();
	}
</script>
