<template>
	<div v-if="msg" class="global-error">{{ store.labels[msg] }}</div>
</template>

<script setup>
	import {onMounted, ref} from 'vue';
	import {useRouter} from 'vue-router';
	import {useStore} from '@/stores';
	import {useWebshopStore} from '@/stores/webshop';

	const store = useStore();
	const webshopStore = useWebshopStore();
	const router = useRouter();
	const msg = ref();

	store.loading = 1;

	async function navigateToCart(code) {
		await webshopStore.restoreCart(code).then(res => {
			if (res.success) {
				router.push({name: 'shoppingCart'});
			} else if (res.data.label_name == 'error_cart_with_given_cart_code_already_exists') {
				router.push({name: 'shoppingCart'});
			} else {
				msg.value = res.data.label_name;
			}
		});
	}

	onMounted(() => {
		const url = window.location.href;
		const urlParts = url.split('/');
		const cartIndex = urlParts.indexOf("cart");
		if (cartIndex !== -1 && cartIndex < urlParts.length - 1) {
			const paramAfterCart = urlParts[cartIndex + 1];
			navigateToCart(paramAfterCart);
		}

		store.loading = 0;
	})
</script>


<style scoped>
	.loader {
		width: 30px;
		height: 30px;
		background: #fff url(/media/images/loader.svg) no-repeat center center;
		background-size: 135% auto;
	}
</style>