<template>
	<div class="auth-login">
		<div class="auth-login-wrapper">
			<div class="auth-logo-container">
				<router-link to="/" class="auth-logo"></router-link>
				{{ store.labels.sales_assistant }}
			</div>

			<div class="auth-department-form">
				<select name="location_id" v-model="selectedLocation">
					<option disabled selected value>{{ store.labels.pa_login_location }}</option>
					<option v-for="location in locations" :key="location.id" :value="location.id">{{ location.title }}</option>
				</select>
				<div class="auth-department-btns">
					<button @click="submitLocation()" class="btn-green btn-medium">{{ store.labels.confirm }}</button>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useAuthStore} from '@/stores/auth';
	import {ref, computed, onMounted} from 'vue';
	import {useRouter} from 'vue-router';

	const store = useStore();
	const authStore = useAuthStore();
	const router = useRouter();
	const selectedLocation = ref();
	const locations = computed(() => {
		authStore.locations.find(el => {
			if (el.selected) selectedLocation.value = el.id;
		});

		return authStore.locations;
	});

	onMounted(() => {
		store.loading = 0;
	});

	async function submitLocation() {
		await authStore.submitLocation(selectedLocation.value);
		router.push({name: 'homepage'});
	}
</script>

<style lang="less" scoped>
	.auth-login {
		display: flex;
		align-items: center;
		min-height: 100vh;
		background: linear-gradient(63.43deg, #0050a0 0, #0078b4 100%);
		position: fixed;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		z-index: 11111;
		.error {
			color: @white;
		}

		@media (max-width: @m) {
			padding: 0 25px;
		}
	}
	.auth-login-wrapper {
		width: 500px;
		margin: 0 auto;

		@media (max-width: @m) {
			width: 100%;
			max-width: 500px;
			margin: 25px auto;
		}
	}
	.auth-logo-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin: 0 auto 50px;
		font-size: 14px;
		line-height: 1.2;
		text-transform: uppercase;
		color: @white;
		font-weight: bold;
		letter-spacing: -0.2px;

		@media (max-width: @m) {
			font-size: 11px;
			margin-bottom: 30px;
		}
	}
	.auth-logo {
		width: 215px;
		height: 43px;
		background: url(/media/images/logo.svg) no-repeat;
		background-size: contain;
		display: block;
		flex-shrink: 0;
		margin-bottom: 10px;

		@media (max-width: @m) {
			width: 114px;
			height: 24px;
			margin-bottom: 8px;
		}
	}
	.auth-login-form {
		input {
			border: none;
			box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);
		}
	}
	.submit-container {
		display: flex;
		align-items: center;
		margin-top: 15px;
		p {
			padding-bottom: 0;
		}
		button,
		.btn {
			box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);
		}

		@media (max-width: @m) {
			display: block;
			button,
			.btn {
				width: 100%;
			}
		}
	}
	.auth-links {
		width: 100%;
		text-align: center;
		font-size: 14px;
		line-height: 1.4;
		padding: 0 20px;
		display: flex;
		flex-direction: column;
		a {
			color: @white;
			text-decoration: none;
			&:hover {
				color: @white;
				text-decoration: none;
			}
		}

		@media (max-width: @m) {
			font-size: 13px;
			padding: 20px;
			a {
				text-decoration: underline;
			}
		}
	}
	.auth-department-form {
		select {
			margin-bottom: 15px;
			border: none;
			box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);
		}
		button,
		.btn {
			box-shadow: 5px 5px 25px 0 rgba(3, 32, 62, 0.3);
		}

		@media (max-width: @m) {
			select {
				margin-bottom: 10px;
			}
			button,
			.btn {
				width: 100%;
			}
		}
	}
	.auth-department-btns {
		display: flex;
		justify-content: center;
		margin-top: 10px;

		@media (max-width: @m) {
			margin-top: 15px;
		}
	}
</style>
