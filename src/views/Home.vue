<template>
	<ul class="categories">
		<li v-for="category in categories" :key="category.id" :class="['category', category.code, {'active': category.toggle, 'has-children': category.children}]">
			<a href="javascript:void(0)" @click="redirectCategory(category.children, category.url_without_domain), (category.toggle = !category.toggle)">
				<span v-show="category.main_image" class="category-img">
					<Image loading="lazy" :data="category.main_image_upload_path_thumb" />
				</span>
				<span class="category-title">{{ category.title }}<span class="toggle-icon"></span></span>
			</a>
			<ul v-show="category.children && category.toggle" class="subcategories">
				<li v-for="(subcategory, key) in category.children" :key="key" :class="['subcategory', {'active': subcategory.toggle && subcategory.children, 'has-children': subcategory.children}]">
					<a href="javascript:void(0)" @click="redirectSubCategory(subcategory.children, subcategory.url_without_domain), (subcategory.toggle = !subcategory.toggle)">
						<span v-show="subcategory.main_image" class="subcategory-icon">
							<Image loading="lazy" :src="subcategory.main_image_upload_path" width="20" height="20" />
						</span>
						<span class="subcategory-title">{{ subcategory.title }}<span class="toggle-icon"></span></span>
					</a>
					<ul v-if="subcategory.children" class="subsubcategories">
						<li v-for="(subsubcategory, key) in subcategory.children" :key="key" class="subsubcategory">
							<router-link :to="subsubcategory.url_without_domain">
								{{ subsubcategory.title }}<span class="counter">{{ subsubcategory.total }}</span>
							</router-link>
						</li>
						<li v-if="subcategory.total_discount != '0'" class="subsubcategory">
							<router-link :to="subcategory.url_without_domain + '?discount=1'" class="red">
								{{ store.labels.sale }}<span class="counter">{{ subcategory.total_discount }}</span>
							</router-link>
						</li>
						<li v-if="subcategory.total_new != '0'" class="subsubcategory">
							<router-link :to="subcategory.url_without_domain + '?new=1'" class="blue">
								{{ store.labels.new }}<span class="counter">{{ subcategory.total_new }}</span>
							</router-link>
						</li>
						<li class="subsubcategory">
							<router-link :to="subcategory.url_without_domain">{{ store.labels.show_all }}</router-link>
						</li>
					</ul>
				</li>
			</ul>
		</li>
	</ul>
</template>

<script setup>
	import {computed, onMounted} from 'vue';
	import {useRoute, useRouter} from 'vue-router';
	import {useStore} from '@/stores';
	import {useCatalogStore} from '@/stores/catalog';
	import {useImages} from '@/composables/useImages';
	import Image from '@/components/Image.vue';

	const route = useRoute();
	const router = useRouter();
	const store = useStore();
	const {generateThumbs} = useImages();
	const catalogStore = useCatalogStore();

	// generate category thumbs
	const categories = computed(() => {
		let cat = catalogStore.categories;
		generateThumbs({
			data: cat,
			preset: 'categoryEntry',
		});
		return cat;
	});

	onMounted(async () => {
		// get page title
		store.loading = 0;
		const res = await store.fetchCmsPage(route.path);
		if(res?.seo_title) {
			document.title = res.seo_title;
		}
	});

	function redirectCategory(children, url_basic) {
		if (!children) {
			router.push(url_basic);
		}
	}

	function redirectSubCategory(children, url_basic) {
		let width = window.innerWidth;
		if (width <= 960) {
			if (!children) {
				router.push(url_basic);
			}
		} else {
			router.push(url_basic);
		}
	}
</script>
