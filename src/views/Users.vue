<template>
	<iframe :src="paUser" frameborder="0"></iframe>
</template>

<script setup>
	import {useStore} from '@/stores';
	import {useConfig} from '@/composables/useConfig';

	const store = useStore();
	const {channel} = useConfig();
	const paUser = (window.location.hostname == 'pa.bigbang.si') ? 'https://asistent.bigbang.si/people/?channel='+channel+'#/' : 'https://devasistent.bigbang.si/people/?channel='+channel+'#/';
</script>

<style lang="less" scoped>
	iframe{
		width: 100%; height: calc(~"100vh - 200px");

		@media (max-width: @m){height: calc(~"100vh - 97px");}
	}
</style>