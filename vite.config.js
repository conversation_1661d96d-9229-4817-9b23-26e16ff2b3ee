import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
	build: {
		outDir: 'media',
		emptyOutDir: false
	},
	resolve: {
		alias: {
			'@': fileURLToPath(new URL('./src', import.meta.url))
		}
	},
	css: {
		preprocessorOptions: {
			less: { 
				//additionalData: `@import "./src/assets/_vars"; @import "./src/assets/_mixins";` 
				additionalData: `@import "/media/defaults.less";` 
			}
		}
	}
})
