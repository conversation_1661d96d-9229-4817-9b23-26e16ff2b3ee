<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<!-- FIXME PROG dodati klasu ako je na stranici ubacen iframe -->
<?php if($info['user_id'] == 1): ?>
	<?php $this->block('page_class'); ?> page-iframe<?php $this->endblock('page_class'); ?>
<?php endif; ?>

<?php $this->block('extrahead'); ?>
<script>
  function resizeIframe(obj) {
    obj.style.height = obj.contentWindow.document.documentElement.scrollHeight + 'px';
  }
</script>

<?php $this->endblock('extrahead'); ?>

<?php $this->block('content'); ?>
	<div class="cms-content">
		<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
		<?php echo Arr::get($cms_page, 'content'); ?>

		<?php if(Arr::get($cms_page, 'code') == 'webshop_terms' OR Arr::get($cms_page, 'id') == '21'): ?>
			<?php $terms_pdf = Cms::page($info['lang'], 'webshop_terms', 'code', 'content_pdf'); ?>
			<?php if (Arr::get($terms_pdf, 'content_pdf')): ?>
				<a class="btn btn-download btn-download-pdf" href="<?php echo Utils::file_url($terms_pdf['content_pdf']); ?>" target="_blank"><span><?php echo Arr::get($cmslabel, 'download_terms_pdf'); ?></span></a>
			<?php endif; ?>
		<?php endif; ?>
	</div>
<?php $this->endblock('content'); ?>