<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('main'); ?>
<div class="auth-login">
	<div class="auth-login-wrapper">
		<div class="auth-logo-container">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="auth-logo"></a>
			<?php echo Arr::get($cmslabel, 'sales_assistant'); ?>
		</div>
		<?php if ($message_type AND $message): ?>
			<?php if (is_array($message)): ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></p>
			<?php else: ?>
				<p class="global-<?php echo $message_type; ?>"><?php echo Arr::get($cmslabel, "{$message_type}_{$message}"); ?></p>
			<?php endif; ?>
		<?php endif; ?>

		<?php if ($message_type != 'success' OR (in_array($message, ['confirm_signup', 'reset_password', 'logout', 'forgotten_password']))): ?>
			<form method="post" action="" accept-charset="utf-8" enctype="multipart/form-data" name="login" class="ajax_siteform auth-form form-animated-label auth-login-form">
				<div class="global_error global-error" style="display: none"><?php echo Arr::get($cmslabel, 'form_validation_error'); ?></div>
				<div class="global_success global-success" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'success_forgotten_password'); ?></div>
				<div class="global_error global-error" data-form_name="forgotten_password" style="display: none"><span class="close"></span><?php echo Arr::get($cmslabel, 'error_forgotten_password'); ?></div>
				<fieldset>
					<?php $error = ($message_type AND $message_type != 'success' AND $message) ? "{$message_type}_{$message}" : ""; ?>
					<p class="field email">
						<label for="id_email"><?php echo Arr::get($cmslabel, 'email'); ?></label>
						<input type="email" name="email" id="id_email" />
						<span id="field-error-email" class="field_error error" <?php if (!$error): ?> style="display: none"<?php endif; ?>><?php echo Arr::get($cmslabel, $error, $error); ?></span>
					</p>
					<p class="field password"><label for="id_password"><?php echo Arr::get($cmslabel, 'password'); ?></label><input type="password" name="password" id="id_password" /></p>
				</fieldset>
				<div class="submit-container">
					<button class="btn-green btn-medium" type="submit"><?php echo Arr::get($cmslabel, 'login'); ?></button>
					<div class="auth-links">
						<a class="button2 forgotten" href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'forgotten_password', FALSE); ?>"><?php echo Arr::get($cmslabel, 'pa_forgotten_password'); ?></a>
					</div>
				</div>
			</form>
		<?php endif; ?>
	</div>
</div>
<?php $this->endblock('main'); ?>