<?php $this->extend('default'); ?>

<?php $this->block('main'); ?>
<div class="auth-login">
	<div class="auth-login-wrapper">
		<div class="auth-logo-container">
			<a href="<?php echo Utils::homepage($info['lang']); ?>" class="auth-logo"></a>
			<?php echo Arr::get($cmslabel, 'sales_assistant'); ?>
		</div>

        <form method="GET" class="auth-department-form">
            <select name="location_id" data-change_store="location">
                <option disabled><?php echo Arr::get($cmslabel, 'pa_login_location'); ?></option>
                <?php foreach ($locations AS $location_id => $location_title): ?>
                    <option value="<?php echo $location_id; ?>"  <?php if (!empty($user->store_location->id) AND $location_id == $user->store_location->id): ?> selected<?php endif; ?>><?php echo $location_title; ?></option>
                <?php endforeach; ?>
            </select>
            <?php /*
            <select name="department_id" data-change_store="department">
                <option data-location_id="0" disabled><?php echo Arr::get($cmslabel, 'pa_login_department'); ?></option>
                <?php foreach ($departments AS $department_id => $department_data): ?>
                    <option value="<?php echo $department_id; ?>" data-location_id="<?php echo $department_data['point_id']; ?>"  <?php if (!empty($user->store_locationdepartment->id) AND $department_id == $user->store_locationdepartment->id): ?> selected<?php endif; ?>><?php echo $department_data['title']; ?></option>
                <?php endforeach; ?>
            </select>
            */ ?>
            <div class="auth-department-btns">
                <button type="submit" class="btn-green btn-medium"><?php echo Arr::get($cmslabel, 'confirm'); ?></button>
            </div>
        </form>
    </div>
</div>
<?php $this->endblock('main'); ?>
