<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>">
<head>
	<meta charset="utf-8" />
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<meta name="robots" content="noindex, nofollow">
	<meta name="format-detection" content="telephone=no">
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php echo Html::media('js_gmodernizr', 'js', FALSE); ?>
	<?php echo Html::media('css_gdefault', 'css', FALSE); ?>
	<?php if (Kohana::$environment === 1): ?><?php echo Google::universal_analytics($info['ganalytics_code'], FALSE, $info['controller'].'_'.$info['action']); ?><?php endif; ?>
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
	<style type="text/css">
	body { padding:0; margin:0; background:#fff; height:auto; }
	.terms-cnt { font-size: 9px; line-height: 15px; overflow: auto; height: 90px; }
	.terms-cnt .wrapper { padding: 10px; }
	.terms-cnt h1 { font-size: 14px; line-height: 21px; display: none; }
	.terms-cnt h2, .terms-cnt h3 { font-size: 13px; line-height: 20px; margin: 10px 0 5px; }
	.terms-cnt h4 { margin: 10px 0 5px; font-size: 12px; line-height: 19px; }
	.terms-cnt img { max-height: 30px; width: auto; }
	.first-title { margin-top: 0!important; }
	.wrapper { width:auto; padding: 20px; }
	ul,ol { margin:10px 0 10px 30px; }
	h1 { color:#000; text-align: left; }
	</style>
</head>
<body class="quick">
	<div class="quick-cnt">
		<?php if(isset($_GET['type']) AND $_GET['type'] == 'terms'): ?><div class="terms-cnt"><?php endif; ?>
		<?php $this->block('content_layout'); ?>
			<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
		<?php $this->endblock('content_layout'); ?>
		<?php if(isset($_GET['type']) AND $_GET['type'] == 'terms'): ?></div><?php endif; ?>
	</div>

	<?php echo Html::media('js_gdefault', 'js', FALSE); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
</body>
</html>