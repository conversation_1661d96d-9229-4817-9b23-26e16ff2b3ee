<?php if (Kohana::config('app.webshop.use_coupons')): ?>
	<?php $have_coupon = (isset($shopping_cart_info['total_extra_coupon_code']) AND count($shopping_cart_info['total_extra_coupon_code'])); ?>
	<?php $have_coupon_product = (isset($shopping_cart_info['total_extra_coupon_product_code']) AND count($shopping_cart_info['total_extra_coupon_product_code'])); ?>
	<div class="ww-coupons<?php if ($have_coupon OR $have_coupon_product): ?> active<?php endif; ?>" data-coupon_active="webshop_coupon">
		<!-- Add coupon form -->
		<div class="ww-coupons-form">
			<div class="ww-coupons-add">
				<input type="text" name="coupon_code" id="coupon_code" placeholder="<?php echo Arr::get($cmslabel, 'coupon_enter_code'); ?>" />
				<a class="ww-btn-add" href="javascript:cmscoupon.set('webshop_coupon', '', true)"><span><?php echo Arr::get($cmslabel, 'coupon_btn_add', 'Dodaj'); ?></span></a>
				<div class="coupon_message" style="display: none"></div>
			</div>
		</div>

		<!-- Used coupon -->
		<?php $active_coupons = Arr::get($shopping_cart_info, 'total_extra_coupon_code'); ?>
		<div class="ww-coupons-active">
			<span class="ww-coupons-title"><?php echo str_replace('%COUPON_CODE%', implode(',', $shopping_cart_info['total_extra_coupon_code']), Arr::get($cmslabel, 'coupon_included_code')); ?></span>
			<a class="ww-coupon-delete" href="javascript:cmscoupon.remove('webshop_coupon', '_all', true);"><span><?php echo Arr::get($cmslabel, 'coupon_remove', 'Ukloni'); ?></span></a>
		</div>
	</div>
<?php endif; ?>