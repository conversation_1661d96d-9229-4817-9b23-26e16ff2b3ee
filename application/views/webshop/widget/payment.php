<?php
$payments = Webshop::payments(['lang' => $info['lang']]);
$id_attribute_suffix = !empty($cart['id']) ? '_' . $cart['id'] : '';
?>
<?php foreach ($payments AS $payment_id => $payment): ?>
    <div class="field-shipping-row">
		<span class="field radio-field">
            <input type="radio" data-change="batch" name="payment" value="<?php echo $payment_id; ?>" id="field-payment-<?php echo $payment_id; ?>_<?php echo $id_attribute_suffix; ?>" data-payment_id="<?php echo $payment_id; ?>" data-grouptarget="payment-methods" data-target="" data-js="toggle-visibility" <?php if (!empty($customer_data['payment']) AND $customer_data['payment'] == $payment_id): ?>checked<?php endif; ?> />
		    <label for="field-payment-<?php echo $payment_id; ?>_<?php echo $id_attribute_suffix; ?>"><?php echo $payment['title']; ?></label>
		</span>
    </div>
<?php endforeach; ?>
