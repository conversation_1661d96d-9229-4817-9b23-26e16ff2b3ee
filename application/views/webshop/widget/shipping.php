<?php
$shippings = Webshop::shippings(['lang' => $info['lang']]);
$shipping_address = Html::customer_address($customer_data, $info['lang'], Arr::extract($cmslabel, ['oib']), Kohana::config('app.webshop.customer_data_guest'));
$use_delivery_day_time = Kohana::config('app.webshop.use_delivery_day_time');
$delivery_day_times = Kohana::config('app.webshop.delivery_day_time');

$user_store_location_id = !empty($user->store_location->id) ? $user->store_location->id : 0;
$location_points = ($locationpoints_store) ? array_column($locationpoints_store, 'title', 'id') : [];
$shipping_service_enabled = Kohana::config('app.webshop.shipping_service_enabled');

$id_attribute_suffix = !empty($cart['id']) ? '_' . $cart['id'] : '';

foreach ($shippings as $shipping){
    if (!empty($shipping_service_enabled[$shipping['code']])) {
        $need_services = $shipping_service_enabled[$shipping['code']];
        $exist_services = 0;

        foreach ($need_services AS $need_service_code) {
            if (!empty($shopping_cart_info['products_service_item'][$need_service_code])) {
                $exist_services += $shopping_cart_info['products_service_item'][$need_service_code];
            }
            if ($shipping['code'] == 'hitra_dostava') {
                $exist_services += 1;
            }
        }

        if (empty($exist_services)) {
            unset($shippings[$shipping['id']]);
            continue;
        }
    }
}
?>
<!-- FIXME PROG labels tekstove, maknuti <style> tag, unos datuma i vremena, prikaz greški -->
<style>
    .page-wrapper {
        overflow: visible;
    }
</style>
<div class="w-cart-shipping-tooltip-title"><?php echo Arr::get($cmslabel, 'pa_choose_shipping'); ?></div>
<?php if (!empty($shippings[6])): ?>
    <div class="field-shipping-row">
        <span class="field radio-field">
            <input type="radio" data-change="batch" name="shipping" value="1" id="field-shipping-1_<?php echo $id_attribute_suffix; ?>" data-shipping_id="6" data-pickup_location_id="<?php echo $user_store_location_id; ?>" data-grouptarget="shipping-methods" data-target="" data-js="toggle-visibility">
            <label for="field-shipping-1_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'pa_shipping_1'); ?></label>
        </span>
    </div>

    <?php if (!empty($location_points)): ?>
        <div class="field-shipping-row">
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="shipping" value="2" id="field-shipping-2_<?php echo $id_attribute_suffix; ?>" data-shipping_id="6" data-grouptarget="shipping-methods" data-target=".pickup-locations" data-js="toggle-visibility">
                <label for="field-shipping-2_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'pa_shipping_2'); ?></label>
            </span>
        </div>
        <div class="cd-shipping-subitem pickup-locations" data-group="shipping-methods" data-selector="locations" style="display: none;">
            <?php foreach ($location_points AS $location_id => $location_title): ?>
            <div class="field-shipping-row">
                <span class="field radio-field">
                    <input type="radio" data-change="batch" id="field-shipping-<?php echo $location_id; ?>_<?php echo $id_attribute_suffix; ?>" name="pickup_location" value="<?php echo $location_id; ?>" data-shipping_id="6" data-pickup_location_id="<?php echo $location_id; ?>">
                    <label for="field-shipping-<?php echo $location_id; ?>_<?php echo $id_attribute_suffix; ?>"><?php echo $location_title; ?></label>
                </span>
            </div>
            <?php endforeach; ?>
            <!-- FIXME PROG PAPROD-215 -->
            <?php $item_suppliers = false; ?>
            <?php if(!empty($item_suppliers)): ?>
                <div class="w-cart-shipping-supplier">
                    <div class="w-cart-shipping-supplier-title">EMPORIA FLIP BASIC klasični telefon</div>
                    <span class="cd-shipping-subitem-supplier w-cart-shipping-subitem-supplier">
                        <div class="title"><?php echo Arr::get($cmslabel, 'supplier'); ?></div>
                        <span class="cd-shipping-qty">3</span>
                    </span>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
<?php endif; ?>

<?php if (!empty($shippings[9])): ?>
    <div class="field-shipping-row">
        <span class="field radio-field">
            <input type="radio" data-change="batch" name="shipping" value="3" id="field-shipping-3_<?php echo $id_attribute_suffix; ?>" data-shipping_id="9" data-grouptarget="shipping-methods" data-target=".ship_to_address" data-js="toggle-visibility">
            <label for="field-shipping-3_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'pa_shipping_3'); ?></label>
        </span>
    </div>
    <div class="cd-shipping-subitem ship_to_address" data-group="shipping-methods" data-selector="ship_to_address_form" style="display: none;">
        <div class="field-shipping-row">
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="ship_to_address_change_type" value="1" id="field-ship-to-address-change-type-1_<?php echo $id_attribute_suffix; ?>" data-grouptarget="shipping-address-form" data-target="" data-js="toggle-visibility">
                <label for="field-ship-to-address-change-type-1_<?php echo $id_attribute_suffix; ?>">
                    <span>
                        <?php if (isset($max_shipping_timestamp)): ?>
                            <strong data-js="print-delivery-date"><?php echo date("d.m.Y", $max_shipping_timestamp); ?></strong>
                        <?php endif; ?>
                    </span>
                    <br>
                    <span><?php echo $shipping_address; ?></span>
                </label>
            </span>
        </div>
        <div class="field-shipping-row" >
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="ship_to_address_change_type" value="2" id="field-ship-to-address-change-type-2_<?php echo $id_attribute_suffix; ?>" data-grouptarget="shipping-address-form" data-target=".shipping-address-form-dates" data-js="toggle-visibility">
                <label for="field-ship-to-address-change-type-2_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'new_date'); ?></label>
            </span>
        </div>
        <div class="shipping-address-form-dates" data-selector="shipping-form-2" data-group="shipping-address-form" style="display: none" data-real_time="false">
            <div class="field-shipping-row form-animated-label">
                <p class="field field-shipping_date ffl-floated">
                    <label for="field-shipping_date_only_date_<?php echo $id_attribute_suffix; ?>" class="label-shipping_date"><?php echo Arr::get($cmslabel, 'shipping_date', 'shipping_date'); ?> *</label>
                    <input type="text" id="field-shipping_date_only_date_<?php echo $id_attribute_suffix; ?>" name="shipping_date_only_date" value="" maxlength="45" class="field_string field-datepicker ">
                    <span id="field-error-shipping_date_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <?php if($use_delivery_day_time) : ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="false">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <?php echo Form::select('shipping_time_only_time', $delivery_day_times, null, ['id' => 'shipping_time_only_time_' . $id_attribute_suffix, 'class' => 'field_string'])?>
                        <span id="field-error-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php else: ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="true">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <input type="text" id="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" name="shipping_time_only_time" value="" maxlength="45" class="field_string">
                        <span id="field-error-shipping_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php endif; ?>
            </div>
        </div>
        <!--<div class="field-shipping-row" >
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="ship_to_address_change_type" value="3" id="field-ship-to-address-change-type-3_<?php echo $id_attribute_suffix; ?>" data-grouptarget="shipping-address-form" data-target=".shipping-address-form" data-js="toggle-visibility">
                <label for="field-ship-to-address-change-type-3_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'new_date_address'); ?></label>
            </span>
        </div>
        <div class="shipping-address-form" data-selector="shipping-form-3" data-group="shipping-address-form" style="display: none">
            <span id="field-error-select_products_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
            <span id="field-error-select_shipping_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>

            <div class="field-shipping-row special form-animated-label">
                <p class="field field-shipping_date ffl-floated">
                    <label for="field-shipping_date<?php echo $id_attribute_suffix; ?>" class="label-shipping_date"><?php echo Arr::get($cmslabel, 'shipping_date', 'shipping_date'); ?> *</label>
                    <input type="text" id="field-shipping_date<?php echo $id_attribute_suffix; ?>" name="shipping_date" value="" maxlength="45" class="field_string field-datepicker ">
                    <span id="field-error-shipping_date<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <?php if($use_delivery_day_time) : ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="false">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <?php echo Form::select('shipping_time', $delivery_day_times, null, ['id' => 'shipping_time_only_time_' . $id_attribute_suffix, 'class' => 'field_string'])?>
                        <span id="field-error-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php else: ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="true">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <input type="text" id="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" name="shipping_time" value="" maxlength="45" class="field_string">
                        <span id="field-error-shipping_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php endif; ?>
            </div>
            <div class="field-shipping-row form-animated-label">
                <p class="field field-shipping_first_name ffl-floated">
                    <label for="field-shipping_first_name_<?php echo $id_attribute_suffix; ?>" class="label-shipping_first_name"><?php echo Arr::get($cmslabel, 'first_name', 'first_name'); ?> *</label>
                    <input type="text" id="field-shipping_first_name_<?php echo $id_attribute_suffix; ?>" name="shipping_first_name" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_first_name_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <p class="field field-shipping_last_name ffl-floated">
                    <label for="field-shipping_last_name_<?php echo $id_attribute_suffix; ?>" class="label-shipping_last_name"><?php echo Arr::get($cmslabel, 'last_name', 'last_name'); ?> *</label>
                    <input type="text" id="field-shipping_last_name_<?php echo $id_attribute_suffix; ?>" name="shipping_last_name" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_last_name_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <p class="field field-shipping_address ffl-floated">
                    <label for="field-shipping_address_<?php echo $id_attribute_suffix; ?>" class="label-shipping_address"><?php echo Arr::get($cmslabel, 'address', 'address'); ?> *</label>
                    <input type="text" id="field-shipping_address_<?php echo $id_attribute_suffix; ?>" name="shipping_address" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_address_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                
                <p class="field field-shipping_location ffl-floated">
                    <label for="field-location" class="label-shipping_location"><?php echo Arr::get($cmslabel, 'location', 'location'); ?> *</label>
                    <input type="text" id="field-location" name="location" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-location" class="field_error error" style="display: none"></span>
                    <input type="hidden" id="field-zipcode" name="zipcode" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <input type="hidden" id="field-city" name="city" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                </p>
                <p class="field field-shipping_phone ffl-floated">
                    <label for="field-shipping_phone_<?php echo $id_attribute_suffix; ?>" class="label-shipping_phone"><?php echo Arr::get($cmslabel, 'phone', 'phone'); ?> *</label>
                    <input type="text" id="field-shipping_phone_<?php echo $id_attribute_suffix; ?>" name="shipping_phone" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_phone_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <p class="field field-shipping_message ffl-floated">
                    <label for="field-shipping_message_<?php echo $id_attribute_suffix; ?>" class="label-shipping_message"><?php echo Arr::get($cmslabel, 'message', 'message'); ?></label>
                    <textarea id="field-shipping_message_<?php echo $id_attribute_suffix; ?>" name="shipping_message" class="field_string" data-selector="ship_to_address_fields"></textarea>
                    <span id="field-error-shipping_message_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
            </div>
        </div>-->
    </div>
<?php endif; ?>
<?php if (!empty($shippings[3])): ?>
    <div class="field-shipping-row">
        <span class="field radio-field">
            <input type="radio" data-change="batch" name="shipping" value="4" id="field-shipping-4_<?php echo $id_attribute_suffix; ?>" data-shipping_id="3" data-grouptarget="shipping-methods" data-target=".ship_to_address_bb" data-js="toggle-visibility">
            <label for="field-shipping-4_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'pa_shipping_4'); ?></label>
        </span>
    </div>
    <div class="cd-shipping-subitem ship_to_address_bb" data-group="shipping-methods" data-selector="ship_to_address_form" style="display: none;">
        <div class="field-shipping-row">
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="ship_to_address_change_type" value="1" id="field-ship-to-address-change-type-bb-1_<?php echo $id_attribute_suffix; ?>" data-grouptarget="shipping-address-form" data-target="" data-js="toggle-visibility">
                <label for="field-ship-to-address-change-type-bb-1_<?php echo $id_attribute_suffix; ?>">
                    <span>
                        <?php if (isset($max_shipping_timestamp)): ?>
                            <strong data-js="print-delivery-date"><?php echo date("d.m.Y", $max_shipping_timestamp); ?></strong>
                        <?php endif; ?>
                    </span>
                    <br>
                    <span><?php echo $shipping_address; ?></span>
                </label>
            </span>
        </div>
        <div class="field-shipping-row" >
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="ship_to_address_change_type" value="2" id="field-ship-to-address-change-type-bb-2_<?php echo $id_attribute_suffix; ?>" data-grouptarget="shipping-address-form" data-target=".shipping-address-form-dates-bb" data-js="toggle-visibility">
                <label for="field-ship-to-address-change-type-bb-2_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'new_date'); ?></label>
            </span>
        </div>
        <div class="shipping-address-form-dates-bb" data-selector="shipping-form-2" data-group="shipping-address-form" style="display: none" data-real_time="false">
            <div class="field-shipping-row form-animated-label">
                <p class="field field-shipping_date ffl-floated">
                    <label for="field-shipping_date_only_date_bb_<?php echo $id_attribute_suffix; ?>" class="label-shipping_date"><?php echo Arr::get($cmslabel, 'shipping_date', 'shipping_date'); ?> *</label>
                    <input type="text" id="field-shipping_date_only_date_bb_<?php echo $id_attribute_suffix; ?>" name="shipping_date_only_date" value="" maxlength="45" class="field_string field-datepicker ">
                    <span id="field-error-shipping_date_bb_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <?php if($use_delivery_day_time) : ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="false">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <?php echo Form::select('shipping_time_only_time', $delivery_day_times, null, ['id' => 'shipping_time_only_time_' . $id_attribute_suffix, 'class' => 'field_string'])?>
                        <span id="field-error-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php else: ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="true">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <input type="text" id="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" name="shipping_time_only_time" value="" maxlength="45" class="field_string">
                        <span id="field-error-shipping_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php endif; ?>
            </div>
        </div>
        <!--<div class="field-shipping-row" >
            <span class="field radio-field">
                <input type="radio" data-change="batch" name="ship_to_address_change_type" value="3" id="field-ship-to-address-change-type-bb-3_<?php echo $id_attribute_suffix; ?>" data-grouptarget="shipping-address-form" data-target=".shipping-address-form" data-js="toggle-visibility">
                <label for="field-ship-to-address-change-type-bb-3_<?php echo $id_attribute_suffix; ?>"><?php echo Arr::get($cmslabel, 'new_date_address'); ?></label>
            </span>
        </div>
        <div class="shipping-address-form" data-selector="shipping-form-3" data-group="shipping-address-form" style="display: none">
            <span id="field-error-select_products_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
            <span id="field-error-select_shipping_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>

            <div class="field-shipping-row special form-animated-label">
                <p class="field field-shipping_date ffl-floated">
                    <label for="field-shipping_date_bb_<?php echo $id_attribute_suffix; ?>" class="label-shipping_date"><?php echo Arr::get($cmslabel, 'shipping_date', 'shipping_date'); ?> *</label>
                    <input type="text" id="field-shipping_date_bb_<?php echo $id_attribute_suffix; ?>" name="shipping_date" value="" maxlength="45" class="field_string field-datepicker ">
                    <span id="field-error-shipping_date_bb_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <?php if($use_delivery_day_time) : ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="false">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <?php echo Form::select('shipping_time', $delivery_day_times, null, ['id' => 'shipping_time_only_time_' . $id_attribute_suffix, 'class' => 'field_string'])?>
                        <span id="field-error-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php else: ?>
                    <p class="field field-shipping_time last ffl-floated" data-real_time="true">
                        <label for="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" class="label-shipping_time"><?php echo Arr::get($cmslabel, 'shipping_time', 'shipping_time'); ?> *</label>
                        <input type="text" id="field-shipping_time_only_time_<?php echo $id_attribute_suffix; ?>" name="shipping_time" value="" maxlength="45" class="field_string">
                        <span id="field-error-shipping_time_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                    </p>
                <?php endif; ?>
            </div>
            <div class="field-shipping-row form-animated-label">
                <p class="field field-shipping_first_name ffl-floated">
                    <label for="field-shipping_first_name_<?php echo $id_attribute_suffix; ?>" class="label-shipping_first_name"><?php echo Arr::get($cmslabel, 'first_name', 'first_name'); ?> *</label>
                    <input type="text" id="field-shipping_first_name_<?php echo $id_attribute_suffix; ?>" name="shipping_first_name" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_first_name_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <p class="field field-shipping_last_name ffl-floated">
                    <label for="field-shipping_last_name_<?php echo $id_attribute_suffix; ?>" class="label-shipping_last_name"><?php echo Arr::get($cmslabel, 'last_name', 'last_name'); ?> *</label>
                    <input type="text" id="field-shipping_last_name_<?php echo $id_attribute_suffix; ?>" name="shipping_last_name" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_last_name_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <p class="field field-shipping_address ffl-floated">
                    <label for="field-shipping_address_<?php echo $id_attribute_suffix; ?>" class="label-shipping_address"><?php echo Arr::get($cmslabel, 'address', 'address'); ?> *</label>
                    <input type="text" id="field-shipping_address_<?php echo $id_attribute_suffix; ?>" name="shipping_address" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_address_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>

                <p class="field field-shipping_location ffl-floated">
                    <label for="field-b_location" class="label-shipping_location"><?php echo Arr::get($cmslabel, 'location', 'location'); ?> *</label>
                    <input type="text" id="field-b_location" name="location" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-location" class="field_error error" style="display: none"></span>
                    <input type="hidden" id="field-b_zipcode" name="zipcode" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <input type="hidden" id="field-b_city" name="city" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                </p>
                <p class="field field-shipping_phone ffl-floated">
                    <label for="field-shipping_phone_<?php echo $id_attribute_suffix; ?>" class="label-shipping_phone"><?php echo Arr::get($cmslabel, 'phone', 'phone'); ?> *</label>
                    <input type="text" id="field-shipping_phone_<?php echo $id_attribute_suffix; ?>" name="shipping_phone" value="" maxlength="45" class="field_string" data-selector="ship_to_address_fields">
                    <span id="field-error-shipping_phone_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
                <p class="field field-shipping_message ffl-floated">
                    <label for="field-shipping_message_<?php echo $id_attribute_suffix; ?>" class="label-shipping_message"><?php echo Arr::get($cmslabel, 'message', 'message'); ?></label>
                    <textarea id="field-shipping_message_<?php echo $id_attribute_suffix; ?>" name="shipping_message" class="field_string" data-selector="ship_to_address_fields"></textarea>
                    <span id="field-error-shipping_message_<?php echo $id_attribute_suffix; ?>" class="field_error error" style="display: none"></span>
                </p>
            </div>
        </div>-->
    </div>
<?php endif; ?>

