<?php if (sizeof($products)): ?>
<?php
    $payments_config = Kohana::config('app.webshop.payments_config');
    $payments_config_available = Arr::get($payments_config, 'available');

    $hide_payments = false;
    if (is_array($payments_config_available) AND count($payments_config_available) == 1) {
        $hide_payments = true;
    }
?>
    <div class="w-cart-sw">
        <input class="w-cart-sw-input" name="pa_search_user" data-selector="pa_search_user" type="text" value="<?php echo Arr::get($customer_data, 'cart_reservation_for', ''); ?>" placeholder="<?php echo Arr::get($cmslabel, 'pa_enter_search_user', 'Pretraživanje...'); ?>" />
    </div>

    <div class="w-cart-content" data-selector="remove-active-cart">
        <div class="w-cart-content-title"><?php echo Arr::get($cmslabel, 'pa_cart_content_title'); ?></div>        <?php
        $last_sort_group_position = '';
        $last_sort_group_position2 = '';

        $i=1;
        ?>
        <?php foreach ($products as $product_code => $product_data): ?>
            <?php
            $product_status = $products_status[$product_code];
            $sort_group_position = Arr::get($product_status, 'sort_group_position');

            $next_product = next($products);
            $last_product_in_group = false;
            if($next_product){
                $next_product_shopping_cart_code = $next_product['shopping_cart_code'] ?? $next_product['id'] . '_0';
                if (!empty($products_status[$next_product_shopping_cart_code])) {
                    $next_sort_group_position = Arr::get($products_status[$next_product_shopping_cart_code], 'sort_group_position');
                }
            }
            if((isset($next_sort_group_position) AND $sort_group_position != $next_sort_group_position) OR !$next_product){
                $last_product_in_group = true;
            }
            $group_shipping_extra = 0;
            if (!empty($shopping_cart_info['group_shipping_extra'][$sort_group_position]['total_extra_shipping']) AND $last_product_in_group){
                $group_shipping_extra = $shopping_cart_info['group_shipping_extra'][$sort_group_position]['total_extra_shipping'];
            }

            ?>
            <?php if ($sort_group_position != $last_sort_group_position): ?>
                <?php
                $sort_group_date = Arr::get($product_status, 'sort_group_date');
                if ($sort_group_date) {
                    $sort_group_date = strtotime($sort_group_date);
                    $sort_group_date = date('d.m.Y', $sort_group_date);
                    if ($sort_group_date == date('d.m.Y', time())) {
                        $sort_group_date = Arr::get($cmslabel, 'today', 'Danes');
                    }
                }
                ?>
                <?php
                    $address_full = [];
                    $address_full['shipping_first_name'] = Arr::get($product_data, 'shipping_first_name', '');
                    $address_full['shipping_last_name'] = Arr::get($product_data, 'shipping_last_name', '');
                    $address_full['shipping_address'] = Arr::get($product_data, 'shipping_address', '');
                    $address_full['shipping_zipcode'] = Arr::get($product_data, 'shipping_zipcode', '');
                    $address_full['shipping_city'] = Arr::get($product_data, 'shipping_city', '');
                    foreach ($address_full as $key => $address_field) {
                        if (empty($address_field)){
                            unset($address_full[$key]);
                        }
                    }

                    $chosen_shipping_date = Arr::get($product_data, 'shipping_date', '');
                    $default_shipping_date = Arr::get($product_data, 'shipping_date_default', '');
                    empty($default_shipping_date) ? $default_shipping_date = $chosen_shipping_date : $default_shipping_date;
                    $product_shipping_date = !empty($chosen_shipping_date) ? $chosen_shipping_date : $default_shipping_date;
                    $shipping_date = date('d.m.Y', $product_shipping_date);
                    $date_available = strtotime(Arr::get($product_data, 'date_available', ''));
                    $user_store_location_id = !empty($user->store_location->id) ? $user->store_location->id : 0;

                    $selected_shipping_method_text = '';
                    if (!empty($product_data['shipping_id'])) {
                        switch ($product_data['shipping_id']) {
                            case 6:
                                // osobno preuzimanje - ako je pickup_location_id == current_store_id onda ispišem "Preuzimanje u ovoj poslovnici" ako ne ispišem naziv poslovnice
                                $selected_shipping_method_text = $product_data['pickup_location_id'] == $user_store_location_id ? Arr::get($cmslabel, 'pa_shipping_1') : $product_data['shipping_text'];
                                $selected_shipping_method_text .= " ";
                                break;
                            case 9:
                            case 3:
                                // dostava - ako imam adresu ispišem adresu ako ne ispišem "Dostava"
                                $selected_shipping_method_text = !empty($address_full) ? implode(', ', $address_full) : Arr::get($cmslabel, 'pa_shipping_3');
                                break;
                            default :
                                $selected_shipping_method_text = '';
                        }
                    }

                    $selected_shipping_method_text_badge = '';
                    $selected_shipping_method_text_badge_class = '';
                    if (!empty($product_data['shipping_id'])) {
                        switch ($product_data['shipping_id']) {
                            case 6:
                                // osobno preuzimanje - ako je pickup_location_id == current_store_id onda ispišem "Preuzimanje u ovoj poslovnici" ako ne ispišem naziv poslovnice
                                $selected_shipping_method_text_badge = $product_data['pickup_location_id'] == $user_store_location_id ? Arr::get($cmslabel, 'pa_shipping_badge1') : Arr::get($cmslabel, 'pa_shipping_badge2');
                                $selected_shipping_method_text_badge .= " ";

                                $selected_shipping_method_text_badge_class = $product_data['pickup_location_id'] == $user_store_location_id ? '' : 'badge2';
                                $selected_shipping_method_data = $product_data['pickup_location_id'] == $user_store_location_id ? '1' : '2';
                                break;
                            case 9:
                                // dostava - ako imam adresu ispišem adresu ako ne ispišem "Dostava"
                                $selected_shipping_method_text_badge = Arr::get($cmslabel, 'pa_shipping_badge3');
                                $selected_shipping_method_text_badge_class = 'badge3';
                                $selected_shipping_method_data = '3';
                                break;
                            case 3:
                                //Bigbang dostava
                                $selected_shipping_method_text_badge = Arr::get($cmslabel, 'pa_shipping_badge4');
                                $selected_shipping_method_text_badge_class = 'badge4';
                                $selected_shipping_method_data = '4';
                                break;
                            default :
                                $selected_shipping_method_text_badge = '';
                                $selected_shipping_method_text_badge_class = '';
                                $selected_shipping_method_data = '';
                        }
                    }
                ?>
                <?php $date_available = false; ?>
                <div class="w-cart-group <?php if(!empty($product_status['is_today_pickup'])): ?> w-cart-group-pickup<?php endif; ?><?php if(!empty($product_status['warehouse_pickup'])): ?> w-cart-group-warehouse-pickup<?php endif; ?>" data-shopping_shipping_group="<?php echo $chosen_shipping_date; ?>-<?php echo $selected_shipping_method_data; ?>" data-shopping_shipping_group_today="<?php echo (date('Y-m-d', $chosen_shipping_date) == date('Y-m-d', time())) ? "1" : "0"; ?>" data-sort_group_position="<?php echo $sort_group_position; ?>">
                    <div class="w-cart-group-title"><?php echo Arr::get($product_status, 'sort_group_title'); ?></div>

                    <?php if(empty($product_status['is_today_pickup'])): ?>
                        <?php if(!empty($selected_shipping_method_text_badge)): ?>
                            <div class="w-cart-group-badge<?php if(!empty($selected_shipping_method_text_badge_class)): ?> <?php echo $selected_shipping_method_text_badge_class; ?><?php endif; ?>"><?php echo $selected_shipping_method_text_badge; ?></div>
                        <?php endif; ?>

                        <?php if($info['user_device'] != 'm'): ?><div class="w-cart-group-col2"><?php endif; ?>
                            <div class="w-cart-group-date"><?php echo $sort_group_date; ?></div>
                            <div class="w-cart-group-address<?php if(!empty($selected_shipping_method_text_badge_class)): ?> <?php echo $selected_shipping_method_text_badge_class; ?><?php endif; ?>"><span><?php echo $selected_shipping_method_text; ?></span></div>
                        <?php if($info['user_device'] != 'm'): ?></div><?php endif; ?>
                    <?php endif; ?>
                </div>
                <?php $last_sort_group_position = $sort_group_position; ?>
            <?php endif; ?>
            <?php echo View::factory('webshop/shopping_cart_entry', array('product_code' => $product_code, 'product_data' => $product_data, 'product_status' => $product_status, 'i' => $i, 'cart' => $cart)); ?>
            <?php $i++; ?>

            <?php if ($last_product_in_group): ?>
                 <div class="w-cart-total-shipping" <?php if(empty($group_shipping_extra)){ ?> style="display: none" <?php } ?>>
                    <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'shipping', 'Dostava'); ?>:</span>
                    <span class="w-totals-value strong <?php echo "cart_info_group_shipping_extra_".$sort_group_position?>"><?php echo Utils::currency_format($group_shipping_extra * $currency['exchange'], $currency['display']); ?></span>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
        <div class="w-cart-content-bottom">
            <div class="w-cart-total">
                <div class="cart-total-discount cart_info_total_items_basic_discount_with_tax_box red" <?php if ($shopping_cart_info['total_items_basic_discount_with_tax'] == 0): ?>style="display: none"<?php endif; ?>>
                    <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_discount', 'Popust'); ?>:</span>
                    <span class="w-totals-value fw-b cart_info_total_items_basic_discount_with_tax"><strong><?php echo Utils::currency_format($shopping_cart_info['total_items_basic_discount_with_tax'] * $currency['exchange'], $currency['display']); ?></strong></span>
                </div>
                <div class="ww-total cart-total">
                    <span class="w-totals-label"><?php echo Arr::get($cmslabel, 'total_to_pay', 'Skupaj'); ?>:</span>
                    <span class="w-totals-value fw-b value cart_info_total"><strong><?php echo Utils::currency_format($shopping_cart_info['total'] * $currency['exchange'], $currency['display']); ?></strong></span>
                </div>
            </div>
        </div>
    </div>
    <div class="w-cart-bottom">
        <?php /* ?>
        <div class="w-cart-shipping-choose w-cart-select" data-selector="empty_on_inactive_carts">
            <span><?php echo Arr::get($cmslabel, 'pa_choose_shipping'); ?></span>
            <div class="w-cart-select-tooltip">
                <?php echo View::factory('webshop/widget/shipping', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data, 'max_shipping_timestamp' => $max_shipping_timestamp, 'cart' => $cart)); ?>
            </div>
        </div>
        <?php */ ?>
        <div class="w-cart-coupon">
            <?php echo View::factory('webshop/widget/coupon', array('shopping_cart_info' => $shopping_cart_info)); ?>
        </div>
        <div class="w-card-code">
            <div href="/pa_scan_cart/?mode=quick" class="w-card-code-btn w-card-code-scan fancybox_iframe"></div>
			<div class="w-card-code-btn w-card-code-confirm" data-input_save="pa_cart_code" data-confirm="cart_hash"></div>
            <input class="pa-cart-code w-card-code-input" name="pa_cart_code" id="id_cart_hash" type="text" data-selector="cart_hash" placeholder="<?php echo Arr::get($cmslabel, 'pa_enter_cart_code', 'Kod shop kartice'); ?>" value="<?php echo Arr::get($customer_data, 'cart_hash', ''); ?>" />
        </div>
        <?php if (!$hide_payments): ?>
        <div class="w-cart-payment-choose w-cart-select" data-selector="empty_on_inactive_carts">
            <span><?php echo Arr::get($cmslabel, 'pa_choose_payment'); ?></span>
            <div class="w-cart-select-tooltip">
                <?php echo View::factory('webshop/widget/payment', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data, 'cart' => $cart)); ?>
            </div>
        </div>
        <?php endif; ?>
        <!-- Finish shopping -->
        <a class="btn btn-large w-cart-reservation" id="btn-reserve-card" data-js="reserve-order-button" data-selector="remove_on_inactive_carts" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'review_order') . '?' . time() . '&pa_cart_code=' . Arr::get($customer_data, 'cart_hash', '').'&type=reservation'; ?>">
            <?php if($info['user_device'] == 'm'): ?>
                <span><?php echo Arr::get($cmslabel, 'pa_reserve_order_mobile', 'Rezerviraj'); ?></span>
            <?php else: ?>
                <span><?php echo Arr::get($cmslabel, 'pa_reserve_order', 'Rezerviraj košaricu'); ?></span>
            <?php endif; ?>
        </a>
        <a class="btn btn-large w-btn-finish cart-finish-shopping" id="btn-finish-shopping" data-selector="remove_on_inactive_carts" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'review_order') . '?' . time() . '&pa_cart_code=' . Arr::get($customer_data, 'cart_hash', ''); ?>">
            <?php if($info['user_device'] == 'm'): ?>
                <span><?php echo Arr::get($cmslabel, 'pa_finish_shopping_mobile', 'Dovrši'); ?></span>
            <?php else: ?>
                <span><?php echo Arr::get($cmslabel, 'pa_finish_shopping', 'Dovrši kupovinu'); ?></span>
            <?php endif; ?>
        </a>
        <a class="w-cart-qr-share btn fancybox_iframe" href="<?php echo $cart['qc_qr_b2c_link']; ?>?mode=quick"><?php echo Arr::get($cmslabel, 'pa_cart_qr_share'); ?></a>
        <a href="#tab-w-cart-remove-tooltip_cart-id<?php echo $cart['id']; ?>" class="w-cart-remove btn fancybox"><?php echo Arr::get($cmslabel, 'pa_cart_remove'); ?></a>
        <div class="w-cart-remove-tooltip" id="tab-w-cart-remove-tooltip_cart-id<?php echo $cart['id']; ?>" style="display:none;">
            Ste prepričani, da želite izbrisati <span data-selector="cart_title_modal">“<?php echo (!empty($cart['cart_title'])) ? $cart['cart_title'] : Arr::get($cmslabel, 'pa_default_cart_name');?>”</span>?
            <div class="w-cart-remove-tooltip-btns">
                <a href="javascript:void(0);" class="btn btn-cancel"><?php echo Arr::get($cmslabel, 'pa_cancel'); ?></a>
                <a href="javascript:void(0);" data-js="remove-cart" data-cart_id="<?php echo $cart['id']; ?>" data-cart_token_id="<?php echo $cart['token_id']; ?>" class="btn btn-remove"><?php echo Arr::get($cmslabel, 'pa_remove_cart'); ?></a>
            </div>
        </div>
    </div>
    <div class="w-cart-shipping-bar" data-selector="empty_on_inactive_carts">
        <div class="w-cart-shipping-box">
            <div class="btn w-cart-shipping-choose"><?php echo Arr::get($cmslabel, 'pa_choose_shipping'); ?>&nbsp;<span id="choosenItems">(0)</span></div>
            <div class="w-cart-shipping-box-right">
                <div class="btn btn-white w-cart-shipping-remove"><span><?php echo Arr::get($cmslabel, 'pa_cart_remove_items'); ?></span></div>
                <div class="btn btn-white w-cart-shipping-deselect"></div>
            </div>
        </div>
        <div class="w-cart-shipping-tooltip">
            <div class="w-cart-shipping-tooltip-close-btn w-cart-shipping-tooltip-close"></div>
            <div class="w-cart-shipping-tooltip-top">
                <div class="w-cart-shipping-tooltip-row1">
                    <div class="w-cart-shipping-tooltip-title special"><?php echo Arr::get($cmslabel, 'pa_chosen_shipping_items'); ?> <span class="btn-more"><span></div>
                </div>
                <div class="w-cart-shipping-tooltip-row2">
                    <?php echo View::factory('webshop/widget/shipping', array('shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data, 'max_shipping_timestamp' => $max_shipping_timestamp, 'cart' => $cart)); ?>
                </div>
            </div>
            <div class="w-cart-shipping-tooltip-bottom">
                <div class="btn btn-white w-cart-shipping-tooltip-close"><?php echo Arr::get($cmslabel, 'cancel'); ?></div>
                <a href="javascript:void(0);" class="btn w-cart-shipping-submit disabled"><span><?php echo Arr::get($cmslabel, 'pa_save_shipping'); ?></span></a>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="w-cart-sw inactive">
        <input class="w-cart-sw-input" name="pa_search_user" data-selector="pa_search_user" type="text" value="<?php echo Arr::get($customer_data, 'cart_reservation_for', ''); ?>" placeholder="<?php echo Arr::get($cmslabel, 'pa_enter_search_user', 'Pretraživanje...'); ?>" />
    </div>

    <div class="w-cart-bottom inactive">
        <?php /* ?>
        <div class="w-cart-shipping-choose w-cart-select" data-selector="empty_on_inactive_carts"></div>
        <?php */ ?>
        <a href="#tab-w-cart-remove-tooltip_cart-id<?php echo $cart['id']; ?>" class="w-cart-remove btn fancybox"><?php echo Arr::get($cmslabel, 'pa_cart_remove'); ?></a>
        <div class="w-cart-remove-tooltip" id="tab-w-cart-remove-tooltip_cart-id<?php echo $cart['id']; ?>" style="display:none;">
            Ste prepričani, da želite izbrisati <span data-selector="cart_title_modal">“<?php echo $cart['cart_title'];?>”</span>?
            <div class="w-cart-remove-tooltip-btns">
                <a href="javascript:void(0);" class="btn btn-cancel"><?php echo Arr::get($cmslabel, 'pa_cancel'); ?></a>
                <a href="javascript:void(0);" data-js="remove-cart" data-cart_id="<?php echo $cart['id']; ?>" data-cart_token_id="<?php echo $cart['token_id']; ?>" class="btn btn-remove"><?php echo Arr::get($cmslabel, 'pa_remove_cart'); ?></a>
            </div>
        </div>
    </div>

    <div class="w-cart-sw inactive">
        <input class="w-cart-sw-input" name="pa_search_user" data-selector="pa_search_user" type="text" value="<?php echo Arr::get($customer_data, 'cart_reservation_for', ''); ?>" placeholder="<?php echo Arr::get($cmslabel, 'pa_enter_search_user', 'Pretraživanje...'); ?>" />
    </div>
<?php endif; ?>