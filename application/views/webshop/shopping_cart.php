<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-cart<?php $this->endblock('page_class'); ?>

<?php $this->block('extrahead'); ?>
	<script type="module" crossorigin src="/media/assets/index.174f1536.js"></script>
	<link rel="stylesheet" href="/media/assets/index.65118d8b.css">
<?php $this->endblock('extrahead'); ?>

<?php $this->block('content'); ?>
	<div id="app"></div>


<?php /*if (!empty($error_message)): ?>
	<div class="card-info" id="id_error_message" style="color:red;text-align:center;margin-top:5px;margin-bottom:15px;">
		<?php echo Arr::get($cmslabel, $error_message, $error_message); ?>

		<?php if (!empty($failed_order_id)): ?>
			<?php echo Arr::get($cmslabel, 'failed_order_id', 'Broj neuspješno poslane narudžbe: ') . $failed_order_id; ?>
		<?php endif; ?>
	</div>
<?php endif; ?>

<?php if (!empty($success_message)): ?>
	<div class="card-info" id="id_success_message" style="color:green;text-align:center;margin-top:5px;margin-bottom:15px;">
		<?php echo Arr::get($cmslabel, $success_message, $success_message); ?>

		<?php if (!empty($document_number)): ?>
			<?php echo Arr::get($cmslabel, 'document_number', 'Broj dokumenta: ') . $document_number; ?>
		<?php endif; ?>
	</div>
<?php endif; ?>

<?php
$products = '';
$products_status = '';
$shopping_cart_info = '';
$cart_counter = 0;
?>
<?php if (isset($carts) AND $carts AND sizeof($carts)): ?>
	<?php foreach ($carts as $cart) {
		$cart_counter++;
		$cart_is_active = $cart['cart_active'];
		$shopping_cart_info = $cart['cart_data']['shopping_cart_info'];
		$products = $cart['cart_data']['products'];
		$products_status = $cart['cart_data']['products_status'];

		$cart_class = '';
		$cart_details_class = '';
		$cart_checked = '';
		$max_shipping_timestamp = 0;
		if ($cart_is_active){
			$cart_class = ' active';
			$cart_details_class = ' details-active';
			$cart_checked = ' checked';
			if (!empty($products)) {
				$max_shipping_timestamp = max(array_column($products, 'shipping_date'));
			}
		}
		?>
		<form action="<?php echo Utils::app_absolute_url($info['lang'], 'webshop', 'set_qty', FALSE); ?>" method="post" name="shopping_cart" data-selector="shopping_cart" <?php if ($cart_class): ?> data-selector_active="shopping_cart"<?php endif; ?> data-max_shipping="<?php echo $max_shipping_timestamp; ?>" data-cart_id="<?php echo $cart['id']; ?>" data-cart_token_id="<?php echo $cart['token_id']; ?>" data-webshop_autocomplete="zipcode_city_location" <?php if (!empty($shopping_cart_info['item_count'])): ?>data-cart_empty_reload="1"<?php endif; ?>>
			<!-- Cart table -->
			<div class="w-table" id="items_shoppingcart">
				<div class="w-cart-item<?php echo $cart_class; ?> <?php echo $cart_details_class; ?>">
					<div class="w-cart-header">
						<div class="w-cart-check-button">
							<input type="radio" id="cart_<?php echo $cart['id']; ?>" name="cart" value="<?php echo $cart['token_id']; ?>" data-js="change-cart" <?php echo $cart_checked; ?>>
							<label for="cart_<?php echo $cart['id']; ?>"></label>
						</div>
						<div class="w-cart-name-container">
							<div class="w-cart-name-confirm" data-js="set-cart-name"></div>
							<input class="w-cart-name" type="code" id="code_<?php echo $cart['id']; ?>" name="code_<?php echo $cart['id']; ?>" data-selector="cart_name" placeholder="<?php echo $cart['cart_title']; ?>">
						</div>
						<div class="w-cart-more-btn">
							<span class="show <?php echo $cart_is_active ? ' already-loaded' : ''; ?>" data-js="get-cart-content"><?php echo Arr::get($cmslabel, 'pa_show_details'); ?><span class="toggle-icon"></span></span>
							<span class="hide"><?php echo Arr::get($cmslabel, 'pa_hide_details'); ?><span class="toggle-icon"></span></span>
						</div>
					</div>
					<div class="w-cart-body" data-selector="cart-body">
						<?php if ($cart_is_active): ?>
							<?php echo View::factory('webshop/shopping_cart_content', array('cart' => $cart, 'shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data, 'products' => $products, 'products_status' => $products_status, 'max_shipping_timestamp' => $max_shipping_timestamp)); ?>
						<?php else: ?>
							<?php echo View::factory('webshop/shopping_cart_content_inactive', array('cart' => $cart, 'shopping_cart_info' => $shopping_cart_info, 'customer_data' => $customer_data, 'products' => $products, 'products_status' => $products_status, 'max_shipping_timestamp' => $max_shipping_timestamp)); ?>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</form>
	<?php } ?>
<?php else: ?>
	<div class="empty-cart">
		<?php echo Arr::get($cmslabel, 'empty_shopping_cart'); ?>
	</div>
<?php endif;*/ ?>

<?php /*
<a href="/pa_form_quick_add_item_to_cart/" data-js="#" class="w-scan-item-btn btn-float fancybox_iframe"><span><?php echo Arr::get($cmslabel, 'pa_scan_add_to_cart_button'); ?></span></a>
<a href="/pa_quick_cart/?mode=quick" data-js="#" class="w-scan-btn btn-float fancybox_iframe"></a>
<a href="#" data-js="new-cart" class="w-new-cart btn-float"></a>
*/ ?>
<?php $this->endblock('content'); ?>