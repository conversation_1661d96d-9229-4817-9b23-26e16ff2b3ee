<div class="w-cart-sw inactive">
    <input class="w-cart-sw-input" name="#" readonly type="text" value="<?php echo Arr::get($cart, 'cart_reservation_for', ''); ?>" placeholder="<?php echo Arr::get($cmslabel, 'pa_enter_search_user', 'Pretraživanje...'); ?>" />
</div>
<div class="w-cart-bottom inactive">
    <?php /* ?>
    <div class="w-cart-shipping-choose" data-selector="empty_on_inactive_carts"></div>
    <?php */ ?>
    <a href="#tab-w-cart-remove-tooltip_cart-id<?php echo $cart['id']; ?>" class="w-cart-remove btn fancybox"><?php echo Arr::get($cmslabel, 'pa_cart_remove'); ?></a>
    <div class="w-cart-remove-tooltip" id="tab-w-cart-remove-tooltip_cart-id<?php echo $cart['id']; ?>" style="display:none;">
        <PERSON><PERSON><PERSON><PERSON>, da <PERSON><PERSON><PERSON> izbrisati <span data-selector="cart_title_modal">“<?php echo $cart['cart_title'];?>”</span>?
        <div class="w-cart-remove-tooltip-btns">
            <a href="javascript:void(0);" class="btn btn-cancel"><?php echo Arr::get($cmslabel, 'pa_cancel'); ?></a>
            <a href="javascript:void(0);" data-js="remove-cart" data-cart_id="<?php echo $cart['id']; ?>" data-cart_token_id="<?php echo $cart['token_id']; ?>" class="btn btn-remove"><?php echo Arr::get($cmslabel, 'pa_remove_cart'); ?></a>
        </div>
    </div>
</div>
