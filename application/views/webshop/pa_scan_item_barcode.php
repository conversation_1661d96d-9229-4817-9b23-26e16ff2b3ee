<?php $this->extend('default_quick'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
<div class="wqr-scan-camera">
    <div class="wqr-scan-info scan-barcode"><?php echo Arr::get($cmslabel, 'scan_barcode', 'Skenirajte barkod');?></div>
    <?php if (!empty($error_message)): ?>
        <div class="global-error">
            <?php echo Arr::get($cmslabel, $error_message, $error_message); ?>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="global-success">
            <?php echo Arr::get($cmslabel, $success_message, $success_message); ?>
        </div>
    <?php endif; ?>

    <div class="wqr-scan-camera-select" id="sourceSelectPanel">
        <select id="sourceSelect">
            <option value="" disabled><?php echo Arr::get($cmslabel, 'pa_choose_camera'); ?></option>
        </select>
    </div>
    <div class="wqr-scan-camera-box">
        <video id="video" width="300" height="200"></video>
    </div>
</div>
<div class="wqr-scan-content">
    <form action="<?php echo $submit_url; ?>" method="POST" class="wqr-scan-form physical_cart_code ajax_siteform">
        <label class="wqr-scan-code" for="field-pa-cart-code"></label>
        <input type="hidden" name="product_code" value="<?php echo Arr::get($_GET, 'product_code', '-1'); ?>"/>
        <input type="text" name="barcode" id="field-barcode" value="" placeholder="<?php echo Arr::get($cmslabel, 'scan_barcode', 'Barkod'); ?>" required>
        <?php /* ?>
        <a href="#" class="wqr-scan"></a>
        <?php */ ?>
        <input id="id_submit_scan" class="wqr-scan-send" type="submit"><?php Arr::get($cmslabel, 'submit', 'Submit'); ?></input>
    </form>
</div>
<?php $this->endblock('content'); ?>