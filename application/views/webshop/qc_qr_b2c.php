<?php $this->extend('default_quick'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>

<?php $this->block('content'); ?>
<div class="qr-share-content wqr-scan-content">
    <div class="qr-share-row">
        <div class="qr-share-info"><?php echo Arr::get($cmslabel, 'qr_code_for_customer', 'QR Code For Customer');?></div>
        <img src="<?php echo $b2c_qr_code; ?>" alt="b2c_qr_code_picture"/>
    </div>

    <div class="qr-share-row">
        <div class="qr-share-info"><?php echo Arr::get($cmslabel, 'qr_link_for_customer', 'QR Link For Customer');?></div>
        <a href="<?php echo $b2c_link; ?>" target="_blank"><?php echo $b2c_link; ?></a>
    </div>


    <div class="qr-share-info"><?php echo Arr::get($cmslabel, 'email'); ?></div>
    <form class="form" action="<?php echo $email_url ?>" method="POST" class="ajax_siteform">
        <input class="input" name="url" value="<?php echo $b2c_link; ?>" type="hidden" >
        <input class="input qr-share-input" name="email" type="text" placeholder="<?php echo Arr::get($cmslabel, 'pa_enter_email', 'Enter email'); ?>" />
        <button class="qr-share-btn-send btn" style="width:100%;" type="submit"><?php echo Arr::get($cmslabel, 'send', 'Send'); ?></button>
    </form>
</div>
<?php $this->endblock('content'); ?>
