<?php $this->extend('default_quick'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', array('cms_page' => isset($cms_page) ? $cms_page : array())); ?><?php $this->endblock('seo'); ?>

<?php $type = $_GET['type'] ?? 'barcode'; ?>

<?php $this->block('content'); ?>
<div class="wqr-scan-camera">
    <div class="wqr-scan-info scan-qrcode"><?php echo Arr::get($cmslabel, 'pa_scan_new_item', 'Skenirajte proizvod');?></div>

    <div class="wqr-scan-buttons">
        <button class="wqr-scan-button btn-white<?php if ($type == 'barcode'): ?> active<?php endif; ?>" onclick="changeScanType(<?php echo $form_url; ?>, 'barcode')"><?php echo Arr::get($cmslabel, 'ean_code', 'EAN code'); ?></button>
        <button class="wqr-scan-button btn-white<?php if ($type == 'qr'): ?> active<?php endif; ?>" onclick="changeScanType(<?php echo $form_url; ?>, 'qr')"><?php echo Arr::get($cmslabel, 'qr_code', 'QR code'); ?></button>
    </div>

    <div class="wqr-scan-camera-select" id="sourceSelectPanel">
        <select id="sourceSelect">
            <option value="" disabled><?php echo Arr::get($cmslabel, 'pa_choose_camera'); ?></option>
        </select>
    </div>
    <div class="wqr-scan-camera-box">
        <video id="video" width="300" height="200"></video>
    </div>
</div>

<div class="wqr-scan-content">
    <form action="<?php echo $submit_url; ?>" method="POST" class="wqr-scan-form physical_cart_code ajax_siteform">
        <label class="wqr-scan-code" for="field-pa-cart-code"></label>

        <?php if ($type == 'qr'): ?>
            <input type="text" name="qrcode" id="field-pa-cart-code" value=""
                   placeholder="<?php echo Arr::get($cmslabel, 'scan_product_qr', 'Skenirajte QR kod/link proizvoda'); ?>" required>
            <input type="hidden" name="type" id="type" value="qr">
        <?php else: ?>
            <input type="text" name="barcode" id="field-barcode" value=""
                   placeholder="<?php echo Arr::get($cmslabel, 'scan_product_barcode', 'Skenirajte barkod proizvoda'); ?>" required>
            <input type="hidden" name="type" id="type" value="barcode">
        <?php endif; ?>

        <input id="id_submit_scan" class="wqr-scan-send" type="submit"><?php Arr::get($cmslabel, 'submit', 'Submit'); ?></input>
    </form>
</div>

<script>
    function changeScanType(form_url, type) {
        location.href = form_url + "?type=" + type;
    }
</script>
<?php $this->endblock('content'); ?>
