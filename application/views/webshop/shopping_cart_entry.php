<?php
$address_full = [];
$address_full['shipping_address'] = Arr::get($product_data, 'shipping_address', '');
$address_full['shipping_zipcode'] = Arr::get($product_data, 'shipping_zipcode', '');
$address_full['shipping_city'] = Arr::get($product_data, 'shipping_city', '');
foreach ($address_full as $address_field) {
    if (empty($address_field)){
        $address_full = false;
        break;
    }
}

$chosen_shipping_date = Arr::get($product_data, 'shipping_date', '');
$chosen_shipping_date = !is_string($chosen_shipping_date) ? strtotime('00:00', $chosen_shipping_date) : $chosen_shipping_date;
$default_shipping_date = Arr::get($product_data, 'shipping_date_default', '');
$default_shipping_date = !is_string($default_shipping_date) ? strtotime('00:00', $default_shipping_date) : $default_shipping_date;
empty($default_shipping_date) ? $default_shipping_date = $chosen_shipping_date : $default_shipping_date;
$product_shipping_date = !empty($chosen_shipping_date) ? $chosen_shipping_date : $default_shipping_date;
$shipping_date = date('d.m.Y', $product_shipping_date);
$date_available = strtotime(Arr::get($product_data, 'date_available', ''));
$user_store_location_id = !empty($user->store_location->id) ? $user->store_location->id : 0;
$order_use_avans_item = Kohana::config('app.webshop.order_use_avans_item');
$warehouse_pickup_delivery_enabled = Kohana::config('app.webshop.use_warehouse_pickup');

$selected_shipping_method_text = '';
$selected_shipping_method_data = '';
if (!empty($product_data['shipping_id'])) {
	switch ($product_data['shipping_id']) {
		case 6:
			// osobno preuzimanje - ako je pickup_location_id == current_store_id onda ispišem "Preuzimanje u ovoj poslovnici" ako ne ispišem naziv poslovnice
			$selected_shipping_method_text = $product_data['pickup_location_id'] == $user_store_location_id ? Arr::get($cmslabel, 'pa_shipping_1') : $product_data['shipping_text'];
			$selected_shipping_method_text .= " ".$shipping_date;

			$selected_shipping_method_data = $product_data['pickup_location_id'] == $user_store_location_id ? '1' : '2';
			break;
		case 9:
			// dostava - ako imam adresu ispišem adresu ako ne ispišem "Dostava"
			$selected_shipping_method_text = $address_full ? implode(', ', $address_full) : Arr::get($cmslabel, 'pa_shipping_3');
			if (!empty($product_data['shipping_time'])) {
				$shipping_date .= ', ' . Kohana::config('app.webshop.delivery_day_time.' . $product_data['shipping_time']);
			}
			$selected_shipping_method_text .= '<br>' . $shipping_date;

			$selected_shipping_method_data = '3';
			break;
		default :
			$selected_shipping_method_text = '';
			$selected_shipping_method_data = '';
	}
}

$bb_delivery_product = 0;
foreach ($shopping_cart_status[$product_code]['services'] AS $service){
    if($service['id']  == 3){
        foreach ($service['items'] AS $service_item){
            if($service_item['service_id'] == 324539){
                $bb_delivery_product = 1;
            }
        }
    }
}
?>
<?php $date_available = false; ?>
<div class="wp" id="product_details_<?php echo $product_code; ?>" data-product_id="<?php echo $product_code; ?>" data-max_shipping_date="<?php echo $default_shipping_date; ?>" data-shopping_shipping_date="<?php echo $chosen_shipping_date; ?>" data-bb_shipping="<?php echo $bb_delivery_product ?>" data-available_qty="<?php echo $product_data['available_pa_qty']?>">
	<input type="hidden" name="product_price" value="<?php echo $product_status['price'] ?>" />
	<input type="hidden" name="product_total" value="<?php echo $product_status['total'] ?>" />
	<div class="wp-row1" id="wp-row1_<?php echo $product_code; ?>">
		<div class="wp-order-numb"></div>
		<div class="wp-check-button wp-col1">
            <?php $product_count =  (!empty($cart['cart_data']['products'])) ? count($cart['cart_data']['products']) : 0; ?>
			<input type="checkbox" id="item_<?php echo $product_code; ?>_<?php echo $cart['id']; ?>" name="item_<?php echo $product_code; ?>" value="<?php echo $product_code; ?>" data-element="cart-item" data-id="<?php echo $product_code; ?>" data-shopping_shipping="<?php echo $chosen_shipping_date; ?>-<?php echo $selected_shipping_method_data; ?>" data-availability_date="<?php echo $date_available; ?>">
			<label for="item_<?php echo $product_code; ?>_<?php echo $cart['id']; ?>"></label>
		</div>
		<div class="wp-warehouse-qty wp-col2">
			<div class="wp-warehouse-qty-box <?php if (!empty($product_data['pickup_status'])): ?><?php if ($product_data['pickup_status'] == 's'): ?>not-available-in-store<?php endif; ?><?php if ($product_data['pickup_status'] == 'u'): ?>not-available<?php endif; ?><?php elseif ($product_data['available_qty'] == 0): ?>not-available<?php endif; ?>"></div>
            <?php
            $product_store = [];
            foreach ($locationpoints_store AS $store_id => $store_data) {
                $store_qty = $product_status['warehouses'][$store_id] ?? 0;
                if (empty($store_qty)) {
                    continue;
                }
                $product_store[$store_id] = trim(str_replace(['Big Bang', ','], ['', ''], $store_data['title'])) . ' ('.$store_qty.')';
            }
            ?>
		</div>
		<div class="wp-cnt wp-col3">
			<?php $barcode = !empty($product_status['show_serial_number_field']); ?>
            <h2 class="wp-title">
				<a href="<?php echo $product_data['url']; ?>"><?php echo $product_data['title']; ?></a>
                <?php if(!empty($barcode)): ?>
                    <span class="barcode wp-title-barcode fancybox_iframe"
                          href="/pa_scan_item_barcode/?product_code=<?php echo $product_data['id'] . '_0'; ?>"
                          style="cursor:pointer;"></span>
                <?php endif; ?>
			</h2>
			<div class="wp-codes">
				<div class="wp-code"><strong><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <?php echo($product_data['code']); ?></div>
				<?php if (!empty($product_data['ean_code'])): ?>
					<div class="wp-ean<?php if (!empty($product_data['ean_codes'])): ?> longer<?php endif; ?>">
						<span><?php echo $product_data['ean_code']; ?></span>
						<?php /* ?>
						<div class="wp-ean-codes">
							<div class="wp-ean"><span>1234567891234</span></div>
							<div class="wp-ean"><span>1234567891234</span></div>
							<div class="wp-ean"><span>1234567891234</span></div>
							<div class="wp-ean"><span>1234567891234</span></div>
							<div class="wp-ean"><span>1234567891234</span></div>
							<div class="wp-ean"><span>1234567891234</span></div>
						</div>
						<?php */ ?>
					</div>
				<?php endif; ?>
			</div>
			<?php /* ?>
			<div class="wp-shipping-info<?php if($date_available): ?> date<?php endif; ?>" data-refresh="shipping_type" data-pickup_in_current_store_text="<?php Arr::get($cmslabel, 'pa_shipping_1'); ?>"><?php echo $selected_shipping_method_text; ?></div>
			<?php */ ?>
			<?php if (!empty($product_data['pickup_status']) AND $product_data['pickup_status'] == 'u' AND !empty($product_store)): ?>
                <div class="wp-shipping-info"><?php echo implode(', ', $product_store); ?></div>
            <?php endif; ?>
			
			<?php /* ?>
			<?php if(!empty($product_data['shipping_date'])): ?>
				<?php
					$shipping_date = Arr::get($product_status, 'sort_group_date');
					$shipping_date = strtotime($shipping_date);
					$shipping_date = date('d.m.Y', $shipping_date);
					if ($shipping_date == date('d.m.Y', time())) {
						$shipping_date = Arr::get($cmslabel, 'today', 'Danes');
					}
                ?>
				<div class="wp-shipping-date">
					<?php echo $shipping_date; ?>
				</div>
			<?php endif; ?>
			<?php */ ?>
            <?php if(empty($product_data['store_contains_no_item'])): ?>
                <div class="w-cart-checkbox-section">
					<?php if ($order_use_avans_item OR ($warehouse_pickup_delivery_enabled AND !empty($product_status['show_warehouse_pickup_checkbox']))): ?>
                    	<div class="w-cart-checkbox-section-btn"><?php echo Arr::get($cmslabel, 'pa_options'); ?></div>
					<?php endif; ?>

                    <?php if ($order_use_avans_item AND empty($product_status['warehouse_pickup'])): ?>
                        <div class="w-cart-checkbox-item avans">
							<div class="w-cart-checkbox-avans">
								<input type="checkbox" id="checkboxAvans-<?php echo $product_code ?>" name="checkbox-avans"
									data-js="avans-item"
									code="<?php echo $product_code; ?>" <?php if (!empty($product_data['avans_item'])): ?> checked <?php endif; ?>>
								<label for="checkboxAvans-<?php echo $product_code ?>"><?php echo Arr::get($cmslabel, 'avans_item'); ?></label>
							</div>
                        </div>
                    <?php endif; ?>

                    <?php if (empty($product_data['avans_item']) AND $warehouse_pickup_delivery_enabled AND !empty($product_status['show_warehouse_pickup_checkbox'])): ?>
                        <div class="w-cart-checkbox-item">
                            <input type="checkbox" id="checkboxWarehousePickup-<?php echo $product_code; ?>" name="checkbox-warehouse"
                                   data-js="warehouse-pickup-item"
                                   code="<?php echo $product_code; ?>" <?php if (!empty($product_data['warehouse_pickup'])): ?> checked <?php endif; ?>>
                            <label for="checkboxWarehousePickup-<?php echo $product_code; ?>"><?php echo Arr::get($cmslabel, 'pa_shipping_warehouse', 'Preuzimanje u skladistu'); ?></label>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
		</div>
		<div class="wp-col4" >
			<!-- FIXME PROG doraditi funkcionalnost -->
            <?php if(!empty($product_status['show_serial_number_field'])): ?>
                <div class="wp-serial-number">
                    <div class="wp-serial-number-btn wp-serial-number-clear"></div>
                    <div class="wp-serial-number-btn wp-serial-number-confirm"></div>
                    <input class="wp-serial-number-input" value="<?php echo Arr::get($product_data, 'serial_number',''); ?>" type="code" id="serial_code_<?php echo $product_code; ?>" name="code" placeholder="<?php if(!empty($product_status['serial_number_mandatory'])): echo '*'; endif; echo Arr::get($cmslabel, 'pa_serial_number', 'Serijski broj artikla......'); ?>"
                           code="<?php echo $product_code; ?>" data-mandatory="<?php echo Arr::get($product_status, 'serial_number_mandatory', false); ?>"
                           db_value="<?php echo Arr::get($product_data, 'serial_number',''); ?>">
                </div>
            <?php endif; ?>
			<div class="wp-qty">
                <?php $available_qty = Kohana::config('app.catalog.use_pa_warehouses_ids') ? $product_data['available_pa_qty'] : $product_data['available_qty'] ?>
				<a class="wp-btn-qty wp-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '-',  1, 0, 1);"></a>
				<input class="wp-input-qty product_qty_input" tabindex="<?php echo $i; ?>" type="text" name="qty[<?php echo $product_code; ?>]" value="<?php echo $product_status['qty']; ?>" />
				<a class="wp-btn-qty wp-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $product_code; ?>', '+',  1, 0, 1);"></a>
				<span class="wp-message product_message product_message_<?php echo $product_code; ?>" style="display: none"></span>

				<!-- FIXME PROG razdvajanje na pojedinačne proizvoda -->
				<?php /* ?>
				<a class="wp-qty-split" href="javascript:void(0);"></a>
				<?php */ ?>
			</div>
		</div>
		<div class="wp-total wp-col5">
			<div class="wp-qty-count-tooltip">
				<span class="product_qty" data-shoppingcart_product_qty="<?php echo $product_code; ?>"><?php echo $product_status['qty']; ?></span> kom
			</div>
            <?php $have_discount = ($product_status['total_basic_price_without_service'] - $product_status['total_without_service']) > 0.01; ?>
            <div class="wp-price-container" data-shoppingcart_product_discount_box="<?php echo $product_code; ?>" <?php if (!$have_discount): ?>style="display: none;"<?php endif; ?>>
                <div class="wp-price-old line-through" data-shoppingcart_product_total_basic_price_without_service="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_basic_price_without_service'] * $currency['exchange'], $currency['display']); ?></div>
                <div class="wp-price-discount wp-price-current <?php if(!empty($product_status['is_loyalty_price'])): ?>green<?php else: ?>red<?php endif; ?>" data-shoppingcart_product_total_without_service="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_without_service'] * $currency['exchange'], $currency['display']); ?></div>
				<!-- FIXME PROG doraditi tooltip s prikazom rata -->
				<?php /* ?>
				<div class="wp-installments-tooltip">
					<div class="wp-installments-tooltip-title">Obrestno financiranje</div>
					<div class="wp-installments-tooltip-table">
						<strong>12,08 € </strong> x  24 mesecev<br>
						<strong>15,84 € </strong> x  18 mesecev<br>
						<strong>23,36 € </strong> x  12 mesecev<br>
						<strong>45,92 € </strong> x  6 mesecev
					</div>
					<p>Informativni izračun ne vključuje morebitnih stroškov dostave in zneska pologa, ki je obvezen pri nakupih od 1.000 €. Za obročno plačilo v košarici izberi Leanpay.<br>
					<a href="#"  target="_blank">Več informacij</a> | <a href="#" target="_blank" title="">Preveri svoj limit</a></p>
				</div>
				<?php */ ?>
			</div>
            <div class="wp-price-container" data-shoppingcart_product_normal_box="<?php echo $product_code; ?>" <?php if ($have_discount): ?>style="display: none;"<?php endif; ?>>
				<div class="wp-price-current" data-shoppingcart_product_total_without_service="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['total_without_service'] * $currency['exchange'], $currency['display']); ?></div>
				<!-- FIXME PROG doraditi tooltip s prikazom rata -->
				<?php /* ?>
				<div class="wp-installments-tooltip">
					<div class="wp-installments-tooltip-title">Obrestno financiranje</div>
					<div class="wp-installments-tooltip-table">
						<strong>12,08 € </strong> x  24 mesecev<br>
						<strong>15,84 € </strong> x  18 mesecev<br>
						<strong>23,36 € </strong> x  12 mesecev<br>
						<strong>45,92 € </strong> x  6 mesecev
					</div>
					<p>Informativni izračun ne vključuje morebitnih stroškov dostave in zneska pologa, ki je obvezen pri nakupih od 1.000 €. Za obročno plačilo v košarici izberi Leanpay.<br>
					<a href="#"  target="_blank">Več informacij</a> | <a href="#" target="_blank" title="">Preveri svoj limit</a></p>
				</div>
				<?php */ ?>
			</div>

			<div class="wp-qty-count wp-price-old" data-shoppingcart_product_qty_box="<?php echo $product_code; ?>" <?php if ((float) $product_status['qty'] == 1): ?> style="display: none;"<?php endif; ?>>
				<span class="product_qty product_qty_special" data-shoppingcart_product_qty="<?php echo $product_code; ?>"><?php echo $product_status['qty']; ?></span> x <span data-shoppingcart_product_price_without_service="<?php echo $product_code; ?>" data-product_price="<?php echo $product_code; ?>"><?php echo Utils::currency_format($product_status['price_without_service'] * $currency['exchange'], $currency['display']); ?></span>
			</div>
		</div>
	</div>
	<?php /* ?>
	<div class="wp-item-details"><span><?php echo Arr::get($cmslabel, 'pa_cart_item_detail'); ?></span></div>
	<?php */ ?>
	<div class="wp-row2">
		<?php if (!empty($product_status['services'])): ?>
			<?php $service_selected = 0; ?>
			<div class="wp-extra-benefits">
				<?php foreach ($product_status['services'] AS $item_service_group): ?>
					<?php
					if (!empty($item_service_group['selected'])) {
						$service_selected += $item_service_group['selected'];
					}
					?>
					<?php foreach ($item_service_group['items'] AS $item_service_item_id => $item_service_item): ?>
						<?php if (empty($item_service_item['active'])) {continue;} ?>
						<div class="wp-extra-benefit-item cd-extra-benefit-item">
							<div class="cd-extra-benefit-row">
								<?php if ($item_service_group['type'] == 's'): ?>
									<input type="radio" name="services_<?php echo $product_code; ?>[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $product_code; ?>-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $product_code; ?>"  data-service_extra_category="<?php echo $item_service_group['code']; ?>"  <?php if (!empty($item_service_item['selected'])): ?>checked <?php endif; ?> />
								<?php elseif ($item_service_group['type'] == 'c'): ?>
									<input type="checkbox" name="services_<?php echo $product_code; ?>[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $product_code; ?>-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $product_code; ?>"  data-service_extra_category="<?php echo $item_service_group['code']; ?>"  <?php if (!empty($item_service_item['selected'])): ?>checked <?php endif; ?> />
								<?php endif; ?>
								<label class="cd-extra-benefit" for="service-<?php echo $product_code; ?>-<?php echo $item_service_item_id; ?>">
									<div class="cd-extra-benefit-title">
										<span data-service_extra_title="<?php echo $item_service_item['id']; ?>"><?php echo $item_service_item['title']; ?></span>
									</div>
								</label>
								<div class="cd-extra-benefit-price wp-extra-benefit-price">+ <span data-service_extra_price="<?php echo $item_service_item['id']; ?>"><?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?></span></div>
								<?php if($item_service_item['description']): ?><span class="cd-extra-benefit-icon"></span><?php endif; ?>
							</div>
							<?php if($item_service_item['description']): ?>
								<div class="cd-extra-benefit-desc"><?php echo $item_service_item['description']; ?></div>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>

					<?php if ($item_service_group['type'] == 's'): ?>
						<div class="wp-extra-benefit-item cd-extra-benefit-item" data-service_extra_none="<?php echo $product_code; ?>_<?php echo $item_service_group['code']; ?>" <?php if (!$service_selected): ?>style="display: none"<?php endif; ?>>
							<div class="cd-extra-benefit-row">
								<input type="radio" name="services_<?php echo $product_code; ?>[]" value="0" id="service-<?php echo $product_code; ?>-0" data-service_extra="<?php echo $product_code; ?>"  data-service_extra_category="<?php echo $item_service_group['code']; ?>" />
								<label class="cd-extra-benefit" for="service-<?php echo $product_code; ?>-0">
									<div class="cd-extra-benefit-title" data-service_extra_title="<?php echo $item_service_item['id']; ?>">
										<?php echo Arr::get($cmslabel, 'no_warranty'); ?>
									</div>
								</label>
							</div>
						</div>
					<?php endif; ?>
				<?php endforeach; ?>
			</div>
		<?php endif; ?>
	</div>
</div>