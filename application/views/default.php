<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>" data-active_cart_token_id="<?php echo Arr::get($info, 'token_id'); ?>">
<head>
    <?php if (Kohana::$environment === 1): ?>
        <?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
    <?php endif; ?>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="format-detection" content="telephone=no">
	<meta name="author" content="Marker.hr">
	<link rel="dns-prefetch" href="//www.google-analytics.com">
	<link rel="dns-prefetch" href="//ssl.google-analytics.com">
	<link rel="dns-prefetch" href="//connect.facebook.net">
	<link rel="dns-prefetch" href="//static.ak.facebook.com">
	<link rel="dns-prefetch" href="//s-static.ak.facebook.com">
	<link rel="dns-prefetch" href="//fbstatic-a.akamaihd.net">
	<link rel="dns-prefetch" href="//maps.gstatic.com">
	<link rel="dns-prefetch" href="//maps.google.com">
	<link rel="dns-prefetch" href="//maps.googleapis.com">
	<link rel="dns-prefetch" href="//mt0.googleapis.com">
	<link rel="dns-prefetch" href="//mt1.googleapis.com">
	<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=block" rel="stylesheet">

	<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
	<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
	<link rel="manifest" href="/site.webmanifest">
	<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
	<meta name="msapplication-TileColor" content="#2d89ef">
	<meta name="theme-color" content="#ffffff">

	<title><?php $this->block('title'); ?><?php $this->endblock('title'); ?></title>
	<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
	<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
	<?php echo Html::media('css_gdefault', 'css', FALSE); ?>
	<?php echo Html::media('js_gmodernizr', 'js', FALSE); ?>
    <?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="<?php echo $info['page_class']; ?><?php $this->block('page_class'); ?><?php $this->endblock('page_class'); ?>" id="top" data-module="<?php echo $info['controller']; ?>">
    <?php $this->block('extrabody_top'); ?><?php $this->endblock('extrabody_top'); ?>
    <?php if (Kohana::$environment === 1): ?>
        <?php echo Google::tag_manager($info['gtagmanager_code'], 'body'); ?>
    <?php endif; ?>
    <div class="page-wrapper">
		<?php $this->block('main'); ?>
			<div class="main">
				<div class="main-col sidebar">
					<div class="sidebar-header">
						<a href="<?php echo Utils::homepage($info['lang']); ?>" class="logo"></a>
						<div class="sidebar-label"><?php echo Arr::get($cmslabel, 'sales_assistant'); ?></div>
					</div>
					
					<?php $active_menu_item = Utils::active_urls($info['lang'], $info['cmspage_url']); ?>
                    <?php $main_menu = Widget_Cms::menu(array('lang' => $info['lang'], 'code' => 'pa_main', 'selected' => $active_menu_item, 'level_range' => '1.2', 'hierarhy_by_position' => true)); ?>
                    <?php
                    $curr_category = trim(Utils::extract_segments($info['lang'], $info['basic_url'], 1, 1, FALSE), '/');
                    ?>
                    <?php if($main_menu): ?>
						<ul class="sidebar-nav">
							<?php foreach ($main_menu as $menu_item): ?>
								<?php $main_menu_subitems = (!empty($menu_item['children'])) ? $menu_item['children'] : []; ?>
								<li class="<?php echo $menu_item['anchor_text']; ?><?php if(!empty($main_menu_subitems)): ?> has-children<?php endif; ?>">
									<a <?php if($info['basic_url'] == $menu_item['url']): ?> class="active"<?php endif; ?> href="<?php echo !empty($curr_category) && $menu_item['url'] == '/akcije-popusti/' ? $menu_item['url'].'?category=' . $curr_category : $menu_item['url']; ?>" title="<?php echo $menu_item['anchor_text']; ?>" <?php if($menu_item['target_blank']): ?> target="_blank"<?php endif; ?>>
										<?php if(!empty($menu_item['image'])): ?>
											<span class="sidebar-nav-icon">
												<?php $fileExtension = pathinfo($menu_item['image'], PATHINFO_EXTENSION); ?>
												<?php if($fileExtension == 'svg'): ?>
													<img src="<?php echo Utils::file_url($menu_item['image']); ?>" alt="<?php echo $menu_item['title']; ?>">
												<?php else: ?>
													<img <?php echo Thumb::generate($menu_item['image'], array('width' => 22, 'height' => 22, 'html_tag' => TRUE, 'srcset' => '44x44c 2x')); ?> alt="<?php echo Text::meta($menu_item['title']); ?>" />
												<?php endif; ?>
											</span>
										<?php endif; ?>
										<span><?php echo $menu_item['title']; ?></span>
									</a>
									<?php if(!empty($main_menu_subitems)): ?>
										<ul class="sidebar-submenu">
											<?php foreach ($main_menu_subitems as $main_menu_subitem): ?>
												<li><a href="<?php echo $main_menu_subitem['url']; ?>"><?php echo $main_menu_subitem['title']; ?></a></li>
											<?php endforeach; ?>
										</ul>
									<?php endif; ?>
								</li>
							<?php endforeach; ?>
							<li class="sidebar-settings-m">
								<a href="javascript:void(0);"><?php echo Arr::get($cmslabel, 'pa_settings'); ?><span class="sidebar-nav-icon"></span></a>
								<ul></ul>
							</li>
						</ul>
					<?php endif; ?>

					<div class="sidebar-footer">
                        <?php if (!empty($info['user_id']) AND !empty($user)): ?>
                            <div class="sidebar-user"><?php echo $user->full_name; ?></div>
                            <ul class="sidebar-footer-nav">
                                <?php if (!empty($user->store_location->id)): ?>
                                    <li class="sidebar-location">
										<a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'change_store', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>">
											<span class="m">
												<?php echo Arr::get($cmslabel, 'pa_store_change'); ?>
											</span>
											<span class="d">
												<?php echo $user->store_location->title_si; ?>
											</span>
										</a>
									</li>
                                <?php endif; ?>
                                <?php /*if (!empty($user->store_locationdepartment->id)): ?>
                                    <li class="sidebar-department">
										<a class="btn-department-change" href="#tab-department-change">
											<span class="m">
												<?php echo Arr::get($cmslabel, 'pa_department_change'); ?>
											</span>
											<span class="d">
												<?php echo $user->store_locationdepartment->title_si; ?>
											</span>
										</a>
									</li>
                                <?php endif;*/ ?>
                                <li class="sidebar-logout"><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'logout', FALSE); ?>?redirect=<?php echo urlencode('/'); ?>"><?php echo Arr::get($cmslabel, 'logout'); ?></a></li>
                            </ul>
                        <?php else: ?>
                            <div class="sidebar-user"><?php echo Arr::get($cmslabel, 'sales_assistant'); ?></div>
                            <ul class="sidebar-footer-nav">
                                <li class="sidebar-login"><a href="<?php echo Utils::app_absolute_url($info['lang'], 'auth', 'login', FALSE); ?>?redirect=<?php echo $info['redirect_url']; ?>"><?php echo Arr::get($cmslabel, 'login'); ?></a></li>
                            </ul>
                        <?php endif; ?>
					</div>
				</div>

                <?php if (!empty($info['user_id']) AND !empty($user->store_location->id)): ?>
                    <?php $departments = Widget_Location::departments(['lang' => $info['lang'], 'point_id' => $user->store_location->id]); ?>
                    <?php if (!empty($departments)): ?>
                        <div class="departments-change-box" id="tab-department-change" style="display:none;">
                            <div class="department-change-title"><?php echo Arr::get($cmslabel, 'pa_department_choose'); ?></div>
                            <?php foreach ($departments AS $department): ?>
                            <p>
                                <input type="radio" id="department<?php echo $department['id']; ?>" name="department" value="<?php echo $department['id']; ?>" <?php if (!empty($user->store_locationdepartment->id) AND $department['id'] == $user->store_locationdepartment->id): ?> checked<?php endif; ?> onClick="javascript:window.location='<?php echo $department['change_department_url']; ?>';">
                                <label for="department<?php echo $department['id']; ?>"><?php echo $department['title']; ?></label><br>
                            </p>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

				<?php $this->block('content_layout'); ?>
					<div class="main-col main-content">
						<div class="main-header">
							<?php echo View::factory('search/widget/form'); ?>
							<?php echo View::factory('catalog/widget/compare'); ?>
							<?php echo View::factory('webshop/widget/shopping_cart'); ?>
						</div>

						<div class="content-section">
							<?php $this->block('content'); ?><?php $this->endblock('content'); ?>
						</div>
					</div>
				<?php $this->endblock('content_layout'); ?>
			</div>
		<?php $this->endblock('main'); ?>

		<?php $this->block('footer'); ?><?php $this->endblock('footer'); ?>
	</div>
	
	<?php echo View::factory('cms/widget/support_details'); ?>
	<?php echo Html::media('js_gdefault', 'js', FALSE); ?>
	<?php //echo View::factory('gdpr/widget/configurator'); ?>
	<?php //echo Html::media('js_gcookie', 'js', FALSE); ?>
	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>

    <?php $this->block('keycloak'); ?><?php echo View::factory('auth/widget/keycloak'); ?><?php $this->endblock('keycloak'); ?>

<?php if ($info['user_id'] == 1 AND (int)Arr::get($_GET, 'cmsdebug', 0) === 1): ?><?php echo '<br/ class="clear">' . View::factory('profiler/stats'); ?><?php endif; ?>

</body>
</html>