<!DOCTYPE html>
<html lang="<?php echo Arr::get(Kohana::config('app.html_lang'), $info['lang'], $info['lang']); ?>" data-default_lang="<?php echo Arr::get(Kohana::config('app.html_lang'), Kohana::config('app.language'), Kohana::config('app.language')); ?>" data-currency_code="<?php echo $currency['code']; ?>" data-currency_display="<?php echo $currency['display']; ?>" data-currency_exchange="<?php echo $currency['exchange']; ?>" data-webshop_min_order="<?php echo intval(Arr::get($shopping_cart, 'total_extra_shipping_min_total', '0')); ?>" data-active_cart_token_id="<?php echo Arr::get($info, 'token_id'); ?>">
	<head>
		<?php if (Kohana::$environment === 1): ?>
			<?php echo Google::tag_manager($info['gtagmanager_code'], 'head'); ?>
		<?php endif; ?>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
		<meta name="format-detection" content="telephone=no">
		<meta name="author" content="Marker.hr">
		<link rel="dns-prefetch" href="//www.google-analytics.com">
		<link rel="dns-prefetch" href="//ssl.google-analytics.com">
		<link rel="dns-prefetch" href="//connect.facebook.net">
		<link rel="dns-prefetch" href="//static.ak.facebook.com">
		<link rel="dns-prefetch" href="//s-static.ak.facebook.com">
		<link rel="dns-prefetch" href="//fbstatic-a.akamaihd.net">
		<link rel="dns-prefetch" href="//maps.gstatic.com">
		<link rel="dns-prefetch" href="//maps.google.com">
		<link rel="dns-prefetch" href="//maps.googleapis.com">
		<link rel="dns-prefetch" href="//mt0.googleapis.com">
		<link rel="dns-prefetch" href="//mt1.googleapis.com">
		<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=block" rel="stylesheet">

		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
		<link rel="manifest" href="/site.webmanifest">
		<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
		<meta name="msapplication-TileColor" content="#2d89ef">
		<meta name="theme-color" content="#ffffff">

		<title>Prodajni asistent</title>
		<?php if (Kohana::$environment !== 1): ?><meta name="robots" content="noindex, nofollow"><?php endif; ?>
		<?php $this->block('seo'); ?><?php $this->endblock('seo'); ?>
		<?php //echo Html::media('/media/style.css', 'css', FALSE); ?>
		<script type="module" crossorigin src="/media/assets/index-715AKfMZ.js"></script>
		<link rel="stylesheet" crossorigin href="/media/assets/index-Dt9VQbKj.css">
	</head>

	<body>
		<div id="app"></div>
		
		<?php echo View::factory('auth/widget/keycloak'); ?>
	</body>
</html>