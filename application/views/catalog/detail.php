<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('page_class'); ?> page-catalog-detail<?php if(empty($list_recommendation)): ?> modal-fancybox-small<?php endif; ?><?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
<?php
$list_recommendation = Widget_Catalog::products(['lang' => $info['lang'], 'related_code' => 'bought_together', 'related_item_id' => $item['id'], 'related_item_add' => $item, 'only_available' => TRUE]);
$images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']);

if ($item['type'] == 'advanced' AND !empty($item["type_config"])) {
    foreach ($item["type_config"] AS $item_variation_code => $item_variation) {
        if (!empty($item_variation['products'])) {
            foreach ($item_variation['products'] AS $item_variation_product) {
                if (!empty($item_variation_product['main_image'])) {
                    $item_variation_product_images = Utils::get_files('catalogproduct', $item_variation_product['id'], 'image', $info['lang']);
                    foreach ($item_variation_product_images AS $item_variation_product_image) {
                        array_push($images,
                            Arr::merge($item_variation_product_image, ['variation_code' => $item_variation_code])
                        );
                    }
                }
            }
        }
    }
}

$user_store_location_id = !empty($user->store_location->id) ? $user->store_location->id : 0;
$location_points_my_and_nearby = $locationpoints_store;
$location_points_my_and_nearby_ids = ($location_points_my_and_nearby) ? array_column($location_points_my_and_nearby, 'id') : [];

$attributes_special = $item['attributes_special'];
$superbenefits = 0;
$cd_img = 0;

$product_priorities = Kohana::config('app.catalog.product_priorities');
$use_pa_warehouses = Kohana::config('app.catalog.use_pa_warehouses_ids');
$priority = Arr::get($product_priorities, $item['priority_2']);
$badges_special = (!empty($item['badges'])) ? $item['badges'] : [];
$badges_special_1 = [];
$badges_special_2 = [];
$badge_tabs = [];
$badge_gifts = [];
$badge_coupon = [];
$badge_recommended = [];
$badge_others = [];
$badge_installments = [];
$badge_desc = false;

if (!empty($badges_special)) {
    foreach ($badges_special AS $badge_special_id => $badge_special_data) {
        if ((int)$badge_special_data['category'] <= 5) {
            if ($badge_special_data['category'] == 1) {
                // preorder
                if (!empty($item['status']) AND !empty($item['date_available']) AND $item['status'] == '5') {
                    $badges_special_1[$badge_special_id] = $badge_special_data;
                }
            } elseif ($badge_special_data['category'] == 2) {
                // coupon
                $item_coupons = Catalog::product_with_list_coupon($item['id']);
                if (!empty($item_coupons)) {
                    $item_coupon = reset($item_coupons);
                    $badge_special_data['coupon_code'] = $item_coupon['coupon_code'];
                    $badge_special_data['coupon_discount_percent'] = (int) $item_coupon['coupon_discount_percent'];
                    $badge_special_data['coupon_active_to'] = $item_coupon['coupon_active_to'];

                    $badge_coupon[$badge_special_id] = $badge_special_data;
                    $badges_special_1[$badge_special_id] = $badge_special_data;
                }
            } else {
                $badges_special_1[$badge_special_id] = $badge_special_data;
            }
        } elseif ($badge_special_data['category'] != 9) {
            $badges_special_2[$badge_special_id] = $badge_special_data;
        }

        if ((int)$badge_special_data['category'] == 3) {
            $badge_gifts[$badge_special_id] = $badge_special_data;
        }

        if ((int)$badge_special_data['category'] == 7) {
            $badge_tabs[$badge_special_id] = $badge_special_data;
        }

        if ((int)$badge_special_data['category'] == 5) {
            $badge_recommended[$badge_special_id] = $badge_special_data;
        }
        if (count($badge_recommended) > 1) {
            $badge_recommended = array_slice($badge_recommended, 0, 1, true);
        }

        if ((int)$badge_special_data['category'] == 6) {
            $badge_others[$badge_special_id] = $badge_special_data;
        }

        if ((int)$badge_special_data['category'] == 6 AND !empty($badge_special_data['content'])) {
            $badge_desc = true;
        }

        if ((int)$badge_special_data['category'] == 9) {
            $badge_installments[$badge_special_id] = $badge_special_data;
        }
    }
    if (count($badge_coupon) > 1) {
        $badge_coupon = array_slice($badge_coupon, 0, 1, true);
    }
}


$is_loyalty = false;
$installments_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['regular'])) ? reset($item['installments_calculation']['regular']) : 0 * $currency['exchange'], $currency['display']);
$installment_price = Arr::get($item, 'installments_calculation')['regular'];
$installment_loyalty_price = Arr::get($item, 'installments_calculation')['loyalty'];

 if (is_array($installment_price)) {
     $installment_price = reset($installment_price);
 }
 if (is_array($installment_loyalty_price)) {
     $installment_loyalty_price = reset($installment_loyalty_price);
 }
 if (!empty($installment_loyalty_price) AND $is_loyalty) {
     $installment_price = $installment_loyalty_price;
 }

// odprodaja
$sale_list_items = Catalog::listitems('webcatalog_115719', [$item['id']]);
$items_attributes = [];
$items_attributes_unit = [];
foreach($item['attributes'] as $item_attribute) {
    if(!in_array($item_attribute['attribute_code'], ['attribute_badges', 'superbenefits', 'katalog-9001'])) {
        $items_attributes[$item_attribute['attribute_title']][] = $item_attribute['title'];
        $items_attributes_unit[$item_attribute['attribute_title']] = (!empty($item_attribute['attribute_unit'])) ? $item_attribute['attribute_unit'] : '';
    }
}


if (empty($item['warehouses'])) {
    $item['warehouses'] = $location_points_my_and_nearby;
}
$qty_warehouse = [];
foreach ($item['warehouses'] as $warehouse) {
    if($warehouse['id'] == $user_store_location_id) {
        $qty_warehouse = $warehouse;
    }
}
$qty_warehouse_available_qty = [];

if(!empty($qty_warehouse['available_qty'])) {
    $qty_warehouse_available_qty = $qty_warehouse['available_qty'];
} else {
    $qty_warehouse['available_qty'] = 0;
    $qty_warehouse_available_qty = 0;
}

$shippings = Webshop::shippings(['lang' => $info['lang']]);


if (!empty($item['warehouses'])) {
    $location_points = $item['warehouses'];
    $location_points = array_filter(array_map(function($element) use ($location_points_my_and_nearby_ids) {
        return (in_array($element['id'], $location_points_my_and_nearby_ids)) ? $element : null;
    }, $location_points));

    foreach ($location_points as &$location_point_item) {
        $location_point_item['available_qty'] = !empty($location_point_item['available_qty']) ? $location_point_item['available_qty'] : 0;
        $location_point_item['has_available_item'] = (bool)$location_point_item['available_qty'];
    }
}

$qty_warehouse_available = (!empty($qty_warehouse['available_qty']) AND $qty_warehouse['available_qty'] > 0);

$item_shipping_date = date("d.m.Y", (int)Arr::get($item, 'shipping_date'));

// product price
$product_output_price = '';
$product_price_number = '';
if (($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))){
    $product_output_price = Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']);
    $product_price_number = Utils::currency_format($item['price_custom'] * $currency['exchange'], '%s', 2, '.', '');
} elseif ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']){
    $product_output_price = Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']);
    $product_price_number = Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], '%s', 2, '.', '');
} else {
    $product_output_price = Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']);
    $product_price_number = Utils::currency_format($item['price_custom'] * $currency['exchange'], '%s', 2, '.', '');
}
?>

<?php
if(Kohana::config('app.catalog.use_pa_warehouses_ids')){
    $connected_warehouses_qty = 0;
    if(!empty($item['warehouses_pa_ids'])){
        $warehouses_arr = explode(',', $item['warehouses_pa_ids']);
        foreach ($warehouses_arr as $warehouse){
            if(!empty($warehouse)){
                $warehouse = explode('=', $warehouse);
                $warehouse_id = $warehouse[0];
                $warehouse_qty = $warehouse[1];
                if(!empty($locationpoints_store[$warehouse_id]) AND $warehouse_id != $user_store_location_id){
                    $connected_warehouses_qty += $warehouse_qty;
                }
            }
        }
    }
}
?>

	<div class="cd-row">
		<a class="cd-back-btn btn" href="javascript:history.back()">Nazaj</a>

		<div class="cd-col1">
			<span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span>
			<span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
			<span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
			<span style="display: none;" data-product_price="1"><?php echo Arr::get($item, 'price'); ?></span>

			<div class="cd-top">
				<?php if (empty(Arr::get($item, 'temporary_unavailable'))): ?>
					<div class="cd-badges">
						<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
							<div class="cd-badge cp-badge cp-badge-discount discount">
								<span>-<?php echo $item['discount_percent_custom']; ?> %</span>
								<div class="cd-badge-tooltip cp-badge-tooltip cp-badge-tooltip-discount"><?php echo Arr::get($cmslabel, 'pa_discount_badge_tooltip'); ?></div>
							</div>
						<?php endif; ?>

						<?php if ($priority): ?>
							<div class="cd-badge cp-badge cp-badge-new new">
								<span><?php echo $priority['title']; ?></span>
								<div class="cd-badge-tooltip cp-badge-tooltip cp-badge-tooltip-new"><?php echo Arr::get($cmslabel, 'pa_new_badge_tooltip'); ?></div>
							</div>
						<?php endif; ?>

						<?php foreach($badges_special_1 as $badge): ?>
							<?php if ($badge['category'] == 3): ?>
								<div class="cd-badge cp-badge cp-badge-gift gift">
									<?php if(!empty($badge['label_title'])): ?>
										<span>-<?php echo $badge['label_title']; ?> %</span>
									<?php endif; ?>
									<div class="cd-badge-tooltip cp-badge-tooltip cp-badge-tooltip-gift"><?php echo Arr::get($cmslabel, 'pa_gift_badge_tooltip'); ?></div>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>

						<?php foreach($badges_special_2 as $badge_special): ?>
							<div class="cd-badge cp-badge cp-badge-coupon coupon">
								<?php if(!empty($badge_special['label_title'])): ?>
									<span>-<?php echo $badge_special['label_title']; ?> %</span>
								<?php endif; ?>
								<div class="cd-badge-tooltip cp-badge-tooltip cp-badge-tooltip-coupon"><?php echo Arr::get($cmslabel, 'pa_coupon_badge_tooltip'); ?></div>
							</div>
							<?php break; ?>
						<?php endforeach; ?>

						<div class="cd-badge cd-badge-qty cp-badge<?php if((empty($qty_warehouse) AND $item['is_available'] AND $item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] == 0 AND $item['status'] != '5')): ?> not-available-in-store<?php endif; ?><?php if($item['status'] == '5' OR (!$item['is_available'] AND (empty($qty_warehouse) OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] < 1)))): ?> not-available<?php endif; ?>">
							<?php if(($item['is_available'] AND $item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0)): ?>
								<?php echo $qty_warehouse_available_qty; ?>
								(<?php echo Kohana::config('app.catalog.use_pa_warehouses_ids') ? max(0, floor($connected_warehouses_qty)) : max(0, floor($item['available_qty'])); ?>)
							<?php elseif(!empty($item['date_available'])): ?>
								<?php echo substr($item['date_available'], 0, -4); ?>
							<?php else: ?>
								-
							<?php endif; ?>
						</div>
					</div>
				<?php endif; ?>

				<?php if(!empty($item['ean_code'])): ?>
					<div class="cd-ean-box">
						<div class="cd-ean<?php if (!empty($item['ean_codes'])): ?> longer<?php endif; ?>">
							<span><?php echo $item['ean_code']; ?></span>
							<?php /* ?>
							<div class="cd-ean-codes">
								<div class="cd-ean"><span><?php echo $item['ean_code']; ?></span></div>
								<div class="cd-ean"><span><?php echo $item['ean_code']; ?></span></div>
								<div class="cd-ean"><span><?php echo $item['ean_code']; ?></span></div>
								<div class="cd-ean"><span><?php echo $item['ean_code']; ?></span></div>
								<div class="cd-ean"><span><?php echo $item['ean_code']; ?></span></div>
								<div class="cd-ean"><span><?php echo $item['ean_code']; ?></span></div>
							</div>
							<?php */ ?>
						</div>
					</div>
				<?php endif; ?>
			</div>

			<div class="cd-header">
				<?php if ($item['type'] == 'advanced'): ?>
					<?php $item_type_advanced_selected = key($item["type_config"]); ?>
					<?php if (!empty($item['primary_product_data'])): ?>
						<h1 class="cd-title" data-product_title="1" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>><?php echo $item['seo_h1'] ?></h1>
					<?php endif; ?>
					<?php if (!empty($item["type_config"])): ?>
						<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
							<h1 class="cd-title" data-product_title="<?php echo Arr::get($item_variation, 'shopping_cart_code_multiple_string', $item_variation['code']) ?>" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
								<?php echo $item['seo_h1'] ?><?php if (!empty($item_variation['title_extra'])): ?>, <?php echo $item_variation['title_extra']; ?><?php endif; ?>
							</h1>
						<?php endforeach; ?>
					<?php endif; ?>
				<?php else: ?>
					<h1 class="cd-title" data-product_title="1"><?php echo $item['seo_h1'] ?></h1>
				<?php endif; ?>
				<div class="cd-info">
					<!-- Product code number & cart attributes -->
					<?php if ($item['type'] == 'advanced'): ?>
						<?php if (!empty($item['primary_product_data'])): ?>
							<div class="cd-code" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>>
								<div><strong class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <span data-product_code="1"><?php echo $item['primary_product_data']['code']; ?></span></div>
							</div>
						<?php endif; ?>
						<?php if (!empty($item["type_config"])): ?>
							<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
								<div class="cd-code" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
									<div><strong class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <span data-product_code="<?php echo Arr::get($item_variation, 'shopping_cart_code_multiple_string', $item_variation['code']) ?>">
										<?php if (!empty($item['primary_product_data'])): ?><?php echo $item['primary_product_data']['code']; ?><?php if (!empty($item_variation['code_extra'])): ?>, <?php endif; ?><?php endif; ?><?php echo $item_variation['code_extra']; ?>
									</span></div>
								</div>
							<?php endforeach; ?>
						<?php endif; ?>
					<?php elseif ($item['type'] == 'configurable' AND !empty($item['primary_product_data'])): ?>
						<div class="cd-code" data-variation_code="<?php echo $item['code']; ?>">
							<div><strong class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <span data-product_code="1"><?php echo $item['primary_product_data']['code']; ?></span></div>
						</div>
					<?php else: ?>
						<div class="cd-code">
							<div><strong class="label"><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <span data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span></div>
						</div>
					<?php endif; ?>
					<div class="cd-category"><a href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a></div>
				</div>
			</div>

			<div class="cd-header-extra">
				<div class="cd-header-extra-col1">
					<div class="cd-legend"><?php echo View::factory('catalog/widget/legend'); ?></div>
				</div>
				<div class="cd-header-extra-col2">
					<div class="cd-images-m">
						<?php if (!empty($item['main_image'])): ?>
							<div class="cd-hero-image">
								<a class="cd-hero-slide product-gallery" href="<?php echo $item['url']; ?>?mode=gallery&index=0">
									<img <?php echo Thumb::generate($item['main_image'], array('width' => 490, 'height' => 490, 'default_image' => '/media/images/no-image-490.jpg', 'html_tag' => TRUE, 'srcset' => '1080c 2x')); ?> data-product_main_image="1"/>
								</a>
							</div>
						<?php else: ?>
							<div class="cd-no-image"><img src="/media/images/no-image-490.jpg" alt="" data-product_main_image="1"></div>
						<?php endif; ?>
					</div>
				</div>
			</div>

			<?php if (!empty($badges_special) AND !empty($badge_gifts)): ?>
				<?php foreach($badge_gifts as $badge_gift): ?>
					<?php if(!empty($badge_gift['title']) OR !empty($badge_gift['short_description'])): ?>
						<div class="cd-gift-section cd-col1-box<?php if(empty($badge_gift['gift_image'])): ?> no-image<?php endif; ?>">
							<?php if(!empty($badge_gift['gift_image'])): ?>
								<div class="cd-gift-img-zoom">
									<?php $fileExtension4 = pathinfo($badge_gift['gift_image'], PATHINFO_EXTENSION); ?>
									<?php if($fileExtension4 == 'svg'): ?>
										<img src="<?php echo Utils::file_url($badge_gift['gift_image']); ?>" alt="<?php echo $badge_gift['label_title']; ?>">
									<?php else: ?>
										<img <?php echo Thumb::generate($badge_gift['gift_image'], array('width' => 510, 'height' => 510, 'html_tag' => TRUE, 'srcset' => '1020r 2x')); ?> alt="<?php echo Text::meta($badge_gift['label_title']); ?>" />
									<?php endif; ?>
								</div>
							<?php endif; ?>
							<?php if(!empty($badge_gift['gift_image'])): ?>
								<div class="cd-gift-img">
									<?php $fileExtension4 = pathinfo($badge_gift['gift_image'], PATHINFO_EXTENSION); ?>
									<?php if($fileExtension4 == 'svg'): ?>
										<img src="<?php echo Utils::file_url($badge_gift['gift_image']); ?>" alt="<?php echo $badge_gift['label_title']; ?>">
									<?php else: ?>
										<img <?php echo Thumb::generate($badge_gift['gift_image'], array('width' => 60, 'height' => 60, 'html_tag' => TRUE, 'srcset' => '120r 2x')); ?> alt="<?php echo Text::meta($badge_gift['label_title']); ?>" />
									<?php endif; ?>
								</div>
							<?php endif; ?>
							<div class="cd-gift-cnt">
								<?php if(!empty($badge_gift['title'])): ?>
									<div class="cd-gift-title"><?php echo ($badge_gift['title']); ?></div>
								<?php endif; ?>
								<?php if(!empty($badge_gift['short_description'])): ?>
									<div class="cd-gift-desc"><?php echo ($badge_gift['short_description']); ?></div>
								<?php endif; ?>
							</div>
						</div>
					<?php endif; ?>
				<?php endforeach; ?>
			<?php endif; ?>

			<?php if(!empty($items_attributes)): ?>
				<div class="cd-attributes-section cd-col1-box">
					<div class="cd-attributes-header">
						<div class="cd-attributes-title cd-subtitle"><?php echo Arr::get($cmslabel, 'tab_specs'); ?></div>
						<!-- FIXME PROG omogućiti uređivanje atributa -->
						<?php /* ?>
						<a class="cd-attributes-edit" href="#"></a>
						<?php */ ?>
					</div>
					<div class="cd-attributes-table">
						<!-- Attributes -->
						<?php foreach ($items_attributes as $item_attribute_title => $item_attribute_values): ?>
							<div class="cd-attributes">
								<div class="cd-attributes-title"><?php echo $item_attribute_title; ?></div>
								<div class="cd-attributes-desc">
									<?php echo implode(', ', $item_attribute_values); ?>
									<?php if (!empty($items_attributes_unit[$item_attribute_title])): ?>
										<?php echo $items_attributes_unit[$item_attribute_title]; ?>
									<?php endif; ?>
								</div>
							</div>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>

			<?php
			$item_content = '';
            $description_length = 0;
			$short_description_text = trim(strip_tags($item['short_description']));
			$content_text = trim(strip_tags($item['content']));
			if (!empty($item['short_description']) AND strlen($short_description_text) >= 10 AND $short_description_text != $content_text AND !Text::starts_with($content_text, $short_description_text)) {
				$item_content .= $item['short_description'];
				$description_length = strlen($short_description_text);
			}
			if (!empty($item['content']) AND strlen($content_text) >= 10) {
				if (!empty($item_content)) {
					$item_content .= '<p>&nbsp;</p>';
				}
				$item_content .= $item['content'];
                $description_length = $description_length + strlen($content_text);
			}

			$product_pim_min_chars = (int)App::config_single('product_pim_min_chars');
			if ($description_length < $product_pim_min_chars) {
			    $ean_code = Arr::get($item, 'ean_code');
				$manufacturer = Arr::get($item, 'manufacturer_title');

                if (Kohana::$environment === 1) {
                    $is_in_loadbee_list = Catalog::listitems('webcatalog_157271', [$item['id']]);
                    $is_in_flixmedia_list = Catalog::listitems('webcatalog_157270', [$item['id']]);
					$is_in_logitech_list = Catalog::listitems('webcatalog_201949', [$item['id']]);
                } else {
                    $is_in_loadbee_list = Catalog::listitems('webcatalog_149843', [$item['id']]);
                    $is_in_flixmedia_list = Catalog::listitems('webcatalog_149842', [$item['id']]);
					$is_in_logitech_list = Catalog::listitems('webcatalog_185936', [$item['id']]);
                }

                $item_content_api = '';
                if (!empty($is_in_flixmedia_list) AND empty($is_in_loadbee_list)) {
                    $cs_provider = 'Flixmedia';
                    $item_content_api .= '<div id="flix-inpage"></div>';
                    $item_content_api .= '<script type="text/javascript" src="//media.flixfacts.com/js/loader.js"
                                                    data-flix-distributor="10251"
                                                    data-flix-language="sl"
                                                    data-flix-brand="' . Text::meta($manufacturer) . '"
                                                    data-flix-ean="' . $ean_code . '"
                                                    data-flix-sku=""
                                                    data-flix-button="flix-minisite"
                                                    data-flix-inpage="flix-inpage"
                                                    data-flix-button-image=""
                                                    data-flix-fallback-language="en"
                                                    data-flix-autoload="inpage"
                                                    data-flix-price="" async>
                                            </script>';
                } elseif (!empty($is_in_loadbee_list)) {
                    $cs_provider = 'Loadbee';
                    $item_content_api .= '<script type = "text/javascript">
                                            loadbeeApiKey = "CBTGtm5pktttLraG5zsAZRmvfmKdzbfZ";
                                        </script>';
                    $item_content_api .= '<div class="loadbeeTabContent">
                                        <div class="loadbeeTab"
                                            data-loadbee-manufacturer="EAN"
                                            data-loadbee-product="' . $ean_code . '"
                                            data-loadbee-language="sl_SI"
                                            data-loadbee-css="default"
                                            data-loadbee-button="default"
                                            data-loadbee-template="default">
                                        </div>
                                    </div>';
                    $item_content_api .= '<script async type="text/javascript" src="http://button.loadbee.com/js/v2/loadbee.js"></script>';
                } elseif (!empty($is_in_logitech_list)) {
					$cs_provider = 'Logitech';
                    $item_content_api .= '<iframe width="100%" src="https://bigbang.parhelion.hr/?ean=' . $ean_code . '" frameborder="0" scrolling="auto" id="parhelion-frames"></iframe>';
				}
            }
			?>

			<?php if(!empty($item_content) OR !empty($item_content_api)): ?>
				<div class="cd-desc cd-col1-box">
					<div class="cd-desc-header cd-attributes-header">
						<div class="cd-desc-title cd-subtitle cd-attributes-title"><?php echo Arr::get($cmslabel, 'tab_product_description'); ?></div>
						<!-- FIXME PROG omogućiti uređivanje opisa -->
						<?php /* ?>
						<a class="cd-desc-edit cd-attributes-edit" href="#"></a>
						<?php */ ?>
					</div>
					<div class="cd-desc-content">
						<?php if (!empty($item_content_api)) : ?>
							<?php if(!empty($cs_provider) AND $cs_provider == 'Logitech'): ?>
								<div data-type_of_content="cs_api_content" data-cs_provider="<?php echo $cs_provider ?>" style="display: none;">
									<?php echo $item_content_api; ?>
								</div>
							<?php else: ?>
								<div data-type_of_content="cs_api_content" data-cs_provider="<?php echo $cs_provider ?>" style="display: none;">
									<?php echo $item_content_api; ?>
								</div>
							<?php endif; ?>
						<?php endif; ?>
						<?php if (!empty($item_content)) : ?>
							<div data-type_of_content="pim_content" data-content_length="<?php echo $description_length; ?>">
								<?php echo $item_content; ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			<?php endif; ?>
		</div>

		<div class="cd-col2">
			<?php if (empty(Arr::get($item, 'temporary_unavailable'))): ?>
				<!-- Product prices -->
				<?php if($item['price_custom'] > 0): ?>
					<?php $is_loyalty = false; ?>

					<div class="cd-price">
						<div class="cd-price-row1" data-variation_box="price">
							<?php if ($item['type'] == 'advanced'): ?>
								<div class="cd-price-cnt<?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?> inactive<?php endif; ?>">
									<div class="cd-old-price"><?php echo Arr::get($cmslabel, 'price'); ?></div>
									<div class="cd-current-price" data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></div>
									<?php if (!empty($item["type_config"])): ?>
										<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
											<div class="cd-current-price" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>><?php echo Utils::currency_format($item_variation['price'] * $currency['exchange'], $currency['display']); ?></div>
										<?php endforeach; ?>
									<?php endif; ?>
								</div>
							<?php else: ?>
								<div class="cd-price-cnt<?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?> inactive<?php endif; ?>" data-variation_item_price="<?php echo $item['shopping_cart_code']; ?>">
									<?php if (($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
										<div class="cd-old-price"<?php if(!$is_loyalty): ?>  data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><span class="line-through"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span><strong class="red"> (-<?php echo $item['discount_percent_custom']; ?>%)</strong></div>
										<div class="cd-current-price red" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
											<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
											<?php echo (!empty($installment_price)) ? '<span class="cd-installments-price">'. str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'installments_price_text')).'</span>' : ""; ?>
										</div>
									<?php elseif($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price_custom']): ?>
										<div class="cd-old-price line-through"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
										<div class="cd-current-price green" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
											<?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?>
											<?php echo (!empty($installment_price)) ? '<span class="cd-installments-price">'. str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'installments_price_text')).'</span>' : ""; ?>
										</div>
									<?php else: ?>
										<div class="cd-old-price"><?php echo Arr::get($cmslabel, 'price'); ?></div>
										<div class="cd-current-price" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
											<?php echo (!empty($installment_price)) ? '<span class="cd-installments-price">'. str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'installments_price_text')).'</span>' : ""; ?>
										</div>
									<?php endif; ?>
								</div>
							<?php endif; ?>
						</div>

						<?php if (!empty($item['loyalty_price_custom']) OR !empty($installment_price)): ?>
							<div class="cd-price-row2">
								<!-- Loyalty installments -->
								<div class="cd-loyalty-info">
									<?php $loyalty_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['loyalty'])) ? (float)reset($item['installments_calculation']['loyalty']) : 0 * $currency['exchange'], $currency['display']); ?>
									<?php $loyalty_price = Utils::currency_format(Arr::get($item, 'loyalty_price_custom') * $currency['exchange'], $currency['display']); ?>
									<?php if (!empty($item['installments_calculation']) AND (!empty($item['installments_calculation']['loyalty'])) AND empty($item['loyalty_price_custom'])): ?>
										<span class="cd-loyalty-info-label"><?php echo str_replace("%PRICE%", $loyalty_min_price, Arr::get($cmslabel, 'loyalty_price_installments')); ?></span>
										<span class="cd-loyalty-info-tooltip">
											<span class="cd-loyalty-info-close"></span>
											<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$item['price_custom'], $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'pa_loyalty_installments_text') : Arr::get($cmslabel, 'pa_loyalty_installments_text_no_rates')); ?>
										</span>
									<?php elseif (!empty($item['loyalty_price_custom'])): ?>
										<span class="cd-loyalty-info-label"><?php echo $loyalty_price; ?></span>
										<span class="cd-loyalty-info-tooltip">
											<span class="cd-loyalty-info-close"></span>
											<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'pa_loyalty_installments_text') : Arr::get($cmslabel, 'pa_loyalty_installments_text_no_rates')); ?>
										</span>
									<?php elseif(!empty($item['installments_calculation']['loyalty']) AND !$is_loyalty): ?>
										<?php //if loyalty price is not set but another badge has installment_price=0 and this badge has visible only for loyalty flag ?>
										<span class="cd-loyalty-info-label"><?php echo str_replace("%PRICE%", $loyalty_min_price, Arr::get($cmslabel, 'loyalty_price_installments')); ?></span>
										<span class="cd-loyalty-info-tooltip">
											<span class="cd-loyalty-info-close"></span>
											<?php echo str_replace(['%PRICE%', '%INSTALLMENT_PRICE%'], [$loyalty_price, $loyalty_min_price], (!empty($item['is_rate'])) ? Arr::get($cmslabel, 'pa_loyalty_installments_text') :  Arr::get($cmslabel, 'pa_loyalty_installments_text_no_rates')); ?>
										</span>
									<?php endif; ?>
								</div>
							</div>
						<?php endif; ?>
						<?php //echo View::factory('catalog/widget/legend'); ?>
					</div>
				<?php endif; ?>
			<?php endif; ?>

			<?php if (!empty($item['discount_expire'])): ?>
				<div class="cd-discount-expire"><?php echo str_replace('%d%', date('d.m.Y.', $item['discount_expire']), Arr::get($cmslabel, 'pa_discount_expire')) ?></div>
			<?php endif; ?>

			<?php if (!empty($item['date_available'])): ?>
				<div class="cd-preorder-info cd-discount-expire"><span><?php echo str_replace('%d%', $item['date_available'], Arr::get($cmslabel, 'pa_date_available')) ?></span></div>
			<?php endif;  ?>

			<?php if (!empty($installments_min_price) AND (!empty($item['installments_calculation']['regular']) OR !empty($item['installments_calculation']['regular']))): ?>
				<div class="cd-installments-note">
					<span class="cd-installments-note-title"><?php echo Arr::get($cmslabel, 'installments_calculate'); ?></span>
					<span class="cd-installments-note-tooltip">
						<span class="cd-installments-note-close"></span>
						<?php if (!empty($item['installments_list_data']['payment_logo'])): ?>
							<span class="cd-installments-tooltip-logo">
								<?php $fileExtension = pathinfo($item['installments_list_data']['payment_logo'], PATHINFO_EXTENSION); ?>
								<?php if($fileExtension == 'svg'): ?>
									<img src="<?php echo Utils::file_url($item['installments_list_data']['payment_logo']); ?>" alt="">
								<?php else: ?>
									<img <?php echo Thumb::generate($item['installments_list_data']['payment_logo'], array('width' => 75, 'height' => 22, 'html_tag' => TRUE)); ?> alt="" />
								<?php endif; ?>
							</span>
						<?php endif; ?>
						<?php if (!empty($item['installments_list_data']['payment_title'])): ?>
							<span class="cd-installments-tooltip-title"><?php echo $item['installments_list_data']['payment_title']; ?></span>
						<?php endif; ?>
						<?php $installments_loop = $item['installments_calculation']['regular']; ?>
						<?php if(!empty($item['installments_calculation']['loyalty']) AND $is_loyalty): ?>
							<?php $installments_loop = $item['installments_calculation']['loyalty']; ?>
						<?php endif; ?>
						<?php if($installments_loop): ?>
							<span class="cd-installments-tooltip-table">
								<?php foreach ($installments_loop as $rate => $rate_price): ?>
									<?php echo str_replace(['%RATE%', '%RATE_PRICE%'],[$rate, Utils::currency_format($rate_price, '%s')], Arr::get($cmslabel, 'installment_single_rate')) . "<br />"; ?>
								<?php endforeach; ?>
							</span>
						<?php endif; ?>
						<?php if (!empty($item['installments_list_data']['payment_description'])): ?>
							<?php echo $item['installments_list_data']['payment_description']; ?>
						<?php else: ?>
							<?php echo Arr::get($cmslabel, 'informativni_izracun'); ?>
						<?php endif; ?>
					</span>
				</div>
			<?php endif; ?>

			<?php if(!empty($item['attributes_special'])): ?>
				<div class="cd-energy-info">
					<?php
					$attr_title = [];
					$attr_img = [];
					foreach($item['attributes_special'] as $attr) {
						if(in_array($attr['attribute_code'], ['ucinek-pranja-in-su-100218739'])){
							$attr_title[0] = $attr['title'];
							$attr_img[0] = $attr['image'];
						}
						if(in_array($attr['attribute_code'], ['razred-energijske-u-100215480'])){
							$attr_title[1] = $attr['title'];
							$attr_img[1] = $attr['image'];
						}
						if (in_array($attr['attribute_code'], ['razred-energijske-u-100176542'])) {
							$attr_title[2] = $attr['title'];
							$attr_img[2] = $attr['image'];
						}
						if (in_array($attr['attribute_code'], ['razred-energij-ucinkov-35'])) {
							$attr_title[3] = $attr['title'];
							$attr_img[3] = $attr['image'];
						}
					}
					?>

					<?php if(!empty($attr_img) OR !empty($attr_title)): ?>
						<?php
						ksort($attr_img);
						ksort($attr_title);
						$attr_img = reset($attr_img);
						$attr_title = reset($attr_title);
						?>
						<?php if(!empty($item['energy_image'])): ?><a href="#tab-energy" class="cd-energy cd-energy-link fancybox"><?php else: ?><div class="cd-energy"><?php endif; ?>
								<?php if(!empty($attr_img)): ?>
									<img loading="lazy" src="<?php echo Utils::file_url($attr_img); ?>" alt="<?php echo $attr_title; ?>">
								<?php endif; ?>
								<?php if(!empty($attr_title) AND empty($attr_img)): ?>
									<?php echo $attr_title; ?>
								<?php endif; ?>
								<span><?php echo Arr::get($cmslabel, 'energy_title'); ?></span>
							<?php if(!empty($item['energy_image'])): ?></a><?php else: ?></div><?php endif; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if(!empty($item['energy_image'])): ?>
				<div id="tab-energy" class="cd-energy-container" style="display: none;">
					<div class="cd-energy-container-title"><?php echo Arr::get($cmslabel, 'energy_title'); ?></div>
					<div class="cd-energy-container-img"><img loading="lazy" <?php echo Thumb::generate($item['energy_image'], array('width' => 300, 'height' => 585, 'html_tag' => TRUE, 'srcset' => '600r 2x')); ?> alt="<?php echo Arr::get($cmslabel, 'energy_title'); ?>" /></div>
				</div>
			<?php endif; ?>

			<div id="product_images" class="cd-images" data-bxslider_var_name="catalog_details_bxlider">
				<?php if ($images): ?>
					<div class="cd-hero-image<?php if(count($images) < 2): ?> single<?php endif; ?>">
						<div class="cd-hero-slider">
							<?php $i = 0; ?>
							<?php foreach ($images as $file): ?>
								<a class="cd-hero-slide product-gallery" href="<?php echo $item['url']; ?>?mode=gallery&index=<?php echo $i; ?>" title="<?php echo Text::meta($file['title']); ?>" <?php if (!empty($file['variation_code'])): ?> data-variation_code="<?php echo $file['variation_code']; ?>" style="display: none"<?php endif; ?>>
									<span>
										<img loading="lazy" <?php echo Thumb::generate($file['file'], array('width' => 490, 'height' => 490, 'default_image' => '/media/images/no-image-490.jpg', 'html_tag' => TRUE, 'srcset' => '1280c 2x')); ?> title="<?php echo Text::meta($file['title']); ?>" alt="<?php echo Text::meta($file['description']); ?>"<?php if ($i == 0): ?> data-product_main_image="1"<?php endif; ?>/>
									</span>
								</a>
								<?php $i++; ?>
							<?php endforeach; ?>
						</div>
					</div>
					<?php if(count($images) > 1): ?>
						<div class="cd-thumbs cd-thumbs-slider<?php if(count($images) <= 5): ?> no-slider<?php endif; ?>">
							<?php $t = 0; ?>
							<?php foreach ($images as $file): ?>
								<a href="javascript:void(0);" data-slide-index="<?php echo $t; ?>" class="cd-thumb" <?php if (!empty($file['variation_code'])): ?> data-variation_code="<?php echo $file['variation_code']; ?>" style="display: none"<?php endif; ?>>
									<span><img loading="lazy" <?php echo Thumb::generate($file['file'], array('width' => 80, 'height' => 80, 'default_image' => '/media/images/no-image-70.jpg', 'html_tag' => TRUE, 'srcset' => '160c 2x')); ?> alt="<?php echo Text::meta($file['description']); ?>" /></span>
								</a>
								<?php $t++; ?>
							<?php endforeach; ?>
						</div>
					<?php endif; ?>
				<?php else: ?>
					<div class="cd-no-image"><img loading="lazy" src="/media/images/no-image-490.jpg" alt="no-image" data-product_main_image="1"></div>
				<?php endif; ?>
			</div>

			<?php
			$item_type_config = [];
			if ($item['type'] == 'configurable') {
				$item_type_config = $item['type_config'];
			} else if (($item['type'] == 'advanced' OR $item['type'] == 'standard') AND !empty($item['parent_configurable_type_config'])) {
				$item_type_config = $item['parent_configurable_type_config'];
			}
			?>

			<?php if (!empty($item_type_config['attribute_data']) AND !empty($item_type_config['product_data'])): ?>
				<div class="cd-variations variations">

					<?php if (!empty($item_type_config['product_data_summary'])): ?>
						<?php foreach ($item_type_config['product_data_summary'] AS $item_attribute_id => $item_attributeitem_datas): ?>
							<?php if ($item_attribute_id == 'service'): ?>

							<?php else: ?>
								<?php $item_attribute_data = $item_type_config['attribute_data'][$item_attribute_id]; ?>
								<div class="variation">
									<div class="variation-title"><?php echo $item_attribute_data['title']; ?>:</div>
									<div class="variation-items variation-item_<?php echo $item_attribute_data['code']; ?>">
										<?php foreach ($item_attributeitem_datas AS $item_attributeitem_id => $item_attributeitem): ?>
											<?php $item_variation_selected = ($item["primary_product_id"] == $item_attributeitem['data']['active'] OR $item['id'] == $item_attributeitem['data']['active']); ?>
											<a href="<?php echo $item_attributeitem['data']['url']; ?>" class="attribute variation-attribute variation-<?php echo $item_attributeitem['data']['code']; ?> <?php if ($item_variation_selected): ?>active<?php endif; ?>">
												<?php if (!empty($item_attributeitem['data']['image'])): ?>
													<span class="img"><img loading="lazy" src="<?php echo Utils::file_url($item_attributeitem['data']['image']); ?>" /></span>
												<?php endif; ?>
												<span class="title"><?php echo $item_attributeitem['data']['title']; ?></span>
												<span class="price"><?php echo Utils::currency_format($item_attributeitem['data']['price'] * $currency['exchange'], $currency['display']); ?></span>
											</a>
										<?php endforeach; ?>
									</div>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php else: ?>
						<?php foreach ($item_type_config['attribute_data'] AS $item_variation_attribute_code => $item_variation_attribute): ?>
							<div class="variation">
								<div class="variation-title"><?php echo $item_variation_attribute['title']; ?>:</div>
								<div class="variation-items variation-item_<?php echo $item_variation_attribute['code']; ?>">
									<?php if ($item_variation_attribute_code == 'service'): ?>
										<?php foreach ($item_variation_attribute['options'] AS $item_variation): ?>
											<?php $item_variation_selected = false; ?>
											<a href="<?php echo $item_variation_attribute['url']; ?>" class="attribute variation-attribute variation-<?php echo $item_variation['code']; ?>">
												<span class="title"><?php echo $item_variation['title']; ?></span>
												<span class="price"><?php echo Utils::currency_format($item_variation['price'] * $currency['exchange'], $currency['display']); ?></span>
											</a>
										<?php endforeach; ?>
									<?php else: ?>
										<?php foreach ($item_type_config['product_data'] AS $item_variation): ?>
											<?php foreach ($item_variation['configurable_attributes'] AS $item_variation_configurable_attribute): ?>
												<?php if (!empty($item_variation_configurable_attribute['attribute_id']) AND $item_variation_configurable_attribute['attribute_id'] == $item_variation_attribute['id']): ?>
													<?php $item_variation_selected = ($item["primary_product_id"] == $item_variation['product_id'] OR $item['id'] == $item_variation['product_id']); ?>
													<a href="<?php echo $item_variation['url']; ?>" class="attribute variation-attribute variation-<?php echo $item_variation_attribute['code']; ?> <?php if ($item_variation_selected): ?>active<?php endif; ?>">
														<?php if (!empty($item_variation_configurable_attribute['image'])): ?>
															<span class="img"><img loading="lazy" src="<?php echo Utils::file_url($item_variation_configurable_attribute['image']); ?>" /></span>
														<?php endif; ?>
														<span class="title"><?php echo $item_variation_configurable_attribute['title']; ?></span>
														<span class="price"><?php echo Utils::currency_format($item_variation['price'] * $currency['exchange'], $currency['display']); ?></span>
													</a>
												<?php endif; ?>
											<?php endforeach; ?>
										<?php endforeach; ?>
									<?php endif; ?>
								</div>
							</div>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if ($item['type'] == 'advanced'): ?>
				<?php if (!empty($item["type_config"])): ?>
					<div class="cd-variations variations">
						<div class="variation">
							<div class="variation-title"><?php echo Arr::get($item, 'attribute_title')?>:</div>
							<div class="variation-items variation-item_kapacitet">
								<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
									<a href="javascript:setProductVariation('<?php echo $item_variation['code']; ?>')" data-variation_active_code="<?php echo $item_variation['code']; ?>" class="attribute variation-attribute variation-size <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected == $item_variation_code): ?>active<?php endif; ?>" <?php if (!empty($item_variation['title_extra'])): ?>title="<?php echo Text::meta($item_variation['title_extra']); ?>"<?php endif; ?>>
										<span class="title"><?php echo $item_variation['title']; ?></span>
										<span class="price"><?php echo Utils::currency_format($item_variation['price'] * $currency['exchange'], $currency['display']); ?></span>
									</a>
								<?php endforeach; ?>
							</div>
						</div>
					</div>
				<?php endif; ?>
			<?php endif; ?>

			<?php if ($item['is_available'] AND !empty($item['services'])): ?>
				<div class="cd-extra-benefits cd-col2-box">
					<?php foreach ($item['services'] AS $item_service): ?>
						<?php if (empty($item_service['items_available'])) {continue;} ?>
						<div class="cd-extra-benefits-header">
							<span class="cd-subtitle cd-extra-benefits-title"><?php echo $item_service['title']; ?></span>
						</div>
						<?php $b = 0; ?>
						<?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
							<?php if (empty($item_service_item['active'])) {continue;} ?>
							<div class="cd-extra-benefit-item<?php if($b == 0): ?> no-border<?php endif; ?>">
								<div class="cd-extra-benefit-row">
									<?php if ($item_service['type'] == 's'): ?>
										<input type="radio" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $item['shopping_cart_code'] ?>" data-service_extra_category="<?php echo $item_service['code']; ?>" data-sum_to_price="" data-service_extra_price_number="<?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], '%s', 2, '.', ''); ?>" />
									<?php elseif ($item_service['type'] == 'c'): ?>
										<input type="checkbox" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $item['shopping_cart_code'] ?>" data-service_extra_category="<?php echo $item_service['code']; ?>" data-sum_to_price="" data-service_extra_price_number="<?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], '%s', 2, '.', ''); ?>" />
									<?php endif; ?>
									<label class="cd-extra-benefit" for="service-<?php echo $item_service_item_id; ?>">
										<div class="cd-extra-benefit-title">
											<span data-service_extra_title="<?php echo $item_service_item['id']; ?>"><?php echo $item_service_item['title']; ?></span>
										</div>
									</label>
									<div class="cd-extra-benefit-price">+ <span data-service_extra_price="<?php echo $item_service_item['id']; ?>"><?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?></span></div>
									<?php if($item_service_item['description']): ?><span class="cd-extra-benefit-icon"></span><?php endif; ?>
								</div>
								<?php if($item_service_item['description']): ?>
									<div class="cd-extra-benefit-desc"><?php echo $item_service_item['description']; ?></div>
								<?php endif; ?>
							</div>
							<?php $b++; ?>
						<?php endforeach; ?>

						<?php if ($item_service['type'] == 's'): ?>
							<div class="cd-extra-benefit-item"  data-service_extra_none="<?php echo $item_service['code']; ?>" style="display: none;">
								<div class="cd-extra-benefit-row">
									<input type="radio" name="services[]" value="" id="service-0" data-service_extra="<?php echo $item['shopping_cart_code']; ?>"  data-service_extra_category="<?php echo $item_service['code']; ?>" data-sum_to_price="" data-service_extra_price_number="0" />
									<label class="cd-extra-benefit" for="service-0">
										<div class="cd-extra-benefit-title" data-service_extra_title="<?php echo $item_service_item['id']; ?>">
											<?php echo Arr::get($cmslabel, 'no_warranty'); ?>
										</div>
									</label>
								</div>
							</div>
							<?php $none_insurance = true; ?>
						<?php endif; ?>
					<?php endforeach; ?>
				</div>

				<div data-shoppingcart_product_service_TMP="1" style="display: none;">
					<?php foreach ($item['services'] AS $item_service): ?>
						<?php $none_insurance = false; ?>
						<?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
							<?php if ($item_service['type'] == 's' AND !$none_insurance AND !empty($item_service['code']) AND $item_service['code'] == 'insurance'): ?>
								<div class="modal-extra-service-item">
									<input type="radio" name="services[]" value="" id="service-0" />
									<label class="modal-extra-service" for="service-0">
										<div class="modal-extra-service-title">
											<?php echo Arr::get($cmslabel, 'no_warranty'); ?>
										</div>
									</label>
								</div>
								<?php $none_insurance = true; ?>
							<?php endif; ?>

							<div class="modal-extra-service-item">
								<div class="modal-extra-service-title">
									<?php echo $item_service_item['title']; ?>
								</div>
								<?php if ($item_service['type'] == 's'): ?>
									<input type="radio" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>"  />
								<?php elseif ($item_service['type'] == 'c'): ?>
									<input type="checkbox" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" />
								<?php endif; ?>
								<label class="modal-extra-service" for="service-<?php echo $item_service_item_id; ?>">
									<div class="modal-extra-service-price">
										+ <?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?>
									</div>
								</label>
							</div>
						<?php endforeach; ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

			<?php if ($item['is_available'] OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0)): ?>
                <div class="cd-shipping cd-col2-box">
                    <div class="cd-subtitle"><?php echo Arr::get($cmslabel, 'shipping'); ?></div>
                    <div class="cd-shipping-items">
                        <?php if (!empty($shippings[6])): ?>
                            <?php
                            $my_warehouse_qty = (!empty($qty_warehouse)) ? (int)$qty_warehouse['available_qty'] : 0;
                            $my_location_point_availability_info = $qty_warehouse_available ? $my_warehouse_qty : $item_shipping_date;
                            $last_class_my_warehouse = ($my_warehouse_qty === 1 OR !$qty_warehouse_available) ? ' last ' : '';
                            ?>
                            <p class="cd-shipping-item">
                                <input type="radio" id="field-shipping-1" name="shipping" value="1" data-shipping_id="6" data-pickup_location_id="<?php echo $user_store_location_id; ?>" checked>
                                <label for="field-shipping-1"><?php echo Arr::get($cmslabel, 'pa_shipping_1'); ?></label>
                                <span class="cd-shipping-qty<?php echo $last_class_my_warehouse; ?>"><?php echo $my_location_point_availability_info; ?></span>
                            </p>
                            <?php $qty_warehouse_available = true; ?>
                            <?php if (!empty($location_points) AND count($location_points) > 1): ?>
                                <p class="cd-shipping-item">
                                    <input type="radio" id="field-shipping-2" name="shipping" value="2" data-shipping_id="6">
                                    <label for="field-shipping-2"><?php echo Arr::get($cmslabel, 'pa_shipping_2'); ?></label>
                                    <?php foreach ($location_points AS $location_point): ?>
                                        <?php
                                        $location_available_qty = (int)$location_point['available_qty'];
                                        $location_point_availability_info = $location_point['has_available_item'] ? $location_available_qty : $item_shipping_date;
                                        $last_class = ($location_available_qty === 1 OR !$location_point['has_available_item']) ? ' last ' : '';
                                        ?>
                                        <?php if(!empty($qty_warehouse) AND !empty($qty_warehouse['id']) AND $qty_warehouse['id'] != $location_point['id']): ?>
                                            <p class="cd-shipping-subitem">
                                                <input type="radio" id="field-shipping-<?php echo $location_point['id']?>" name="pickup_location" value="<?php echo $location_point['id']?>"  <?php if(empty($qty_warehouse_available)): ?>checked<?php $qty_warehouse_available = true; ?><?php endif; ?>>
                                                <label for="field-shipping-<?php echo $location_point['id']?>"><?php echo $location_point['title']; ?></label>
                                                <span class="cd-shipping-qty<?php echo $last_class; ?>"><?php echo $location_point_availability_info; ?></span>
                                            </p>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
									<?php if(!empty($item['element_suppliers'])): ?>
										<span class="cd-shipping-subitem-supplier">
											<div class="title"><?php echo Arr::get($cmslabel, 'supplier'); ?> <?php //echo $item['element_suppliers']; ?></div>
                                            <?php if (!empty($item['available_qty_supplier_type'])): ?>
                                                <?php if (isset($item['available_qty_supplier']) AND !empty((int) $item['available_qty_supplier'])): ?>
                                                    <span class="cd-shipping-qty"><?php echo (int) $item['available_qty_supplier']; ?></span>
                                                <?php else: ?>
                                                    <span class="cd-shipping-qty">0</span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php if (isset($item['available_qty_supplier']) AND !empty((int) $item['available_qty_supplier'])): ?>
                                                    <span class="cd-shipping-qty special"><?php echo Arr::get($cmslabel, 'supplier_qty_1'); ?></span>
                                                <?php else: ?>
                                                    <span class="cd-shipping-qty special"><?php echo Arr::get($cmslabel, 'supplier_qty_0'); ?></span>
                                                <?php endif; ?>
                                            <?php endif; ?>
										</span>
									<?php endif; ?>
                                </p>
                            <?php endif; ?>
                        <?php endif; ?>
                        <?php if (!empty($shippings[9])): ?>
                            <p class="cd-shipping-item">
                                <input type="radio" id="field-shipping-3" name="shipping" value="3" data-shipping_id="9" <?php if(empty($qty_warehouse_available)): ?>checked<?php endif; ?>>
                                <label for="field-shipping-3"><?php echo Arr::get($cmslabel, 'pa_shipping_3'); ?></label>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

			<!-- Bought together -->
			<?php $recommendation_total_selected = $item['price_custom']; ?>
			<?php $recommendation_products_selected = 1; ?>
			<?php $recommendation_products_total = 1; ?>
			<?php $total_items = sizeof($list_recommendation); ?>

			<?php if(!empty($list_recommendation)): ?>
				<?php $recommendation_total = $list_recommendation['_basic'];
					unset($list_recommendation['_basic']);
					$r = 1;
				?>

				<div class="bought-together cd-bought-together" id="bought-together">
					<div class="cd-subtitle bought-related-title"><?php echo Arr::get($cmslabel, 'pa_bought_together'); ?></div>
					<div id="product_special_list_recommendation" class="items-list">
						<div class="catalog-product-related-labels">
							<?php foreach ($list_recommendation as $related_item): ?>
								<?php $recommendation_total_selected += $related_item['price_custom']; ?>
								<?php $recommendation_products_selected += 1; ?>
								<?php $recommendation_products_total += 1; ?>

								<div id="product_special_list_recommendation-descriptions-<?php echo $related_item['shopping_cart_code']; ?>" class="cd-bought-together-item<?php if($related_item['code'] == $item['code']): ?> main<?php endif; ?><?php if($r == $total_items): ?> last<?php endif; ?> clear">
									<div class="cp-col1 cd-bt-item-content">
										<div class="cp-col1-top">
											<div class="cp-info-top">
												<?php $priority = Arr::get($product_priorities, $related_item['priority_2']); ?>
												<div class="cp-badges">
													<?php if ($related_item['discount_percent_custom'] > 0 OR $related_item['price_custom'] < $related_item['basic_price_custom']): ?>
														<div class="cp-badge cp-badge-discount discount"><span>-<?php echo $related_item['discount_percent_custom']; ?>%</span></div>
													<?php endif; ?>

													<?php if ($priority): ?>
														<div class="cp-badge cp-badge-new new"><span><?php echo Arr::get($priority, 'title'); ?></span></div>
													<?php endif; ?>
												</div>
												<h2 class="cp-title cp-bt-title" data-product_title="<?php echo $related_item['shopping_cart_code']; ?>">
													<a href="<?php echo $related_item['url']; ?>"><?php echo $related_item['title']; ?></a>
												</h2>
												<div class="cp-info">
													<div class="cp-code" data-product_code="<?php echo $related_item['shopping_cart_code']; ?>"><strong><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <?php echo $related_item['code']; ?></div>
													<?php if(!empty($related_item['category_title'])): ?>
														<div class="cp-category"><a href="<?php echo $related_item['category_url']; ?>"><?php echo $related_item['category_title']; ?></a></div>
													<?php endif; ?>
												</div>

												<div class="cp-info-top-right">
													<div class="cd-bt-item-image cd-bt-item-image-m">
														<a href="<?php echo $related_item['url']; ?>" title="<?php echo Text::meta($related_item['title']); ?>">
															<img <?php echo Thumb::generate($related_item['main_image'], ['width' => 60, 'height' => 60, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-60.jpg', 'srcset' => '120c 2x']); ?> alt="<?php echo $related_item['title']; ?>" title="<?php echo Text::meta($related_item['main_image_title']); ?>" />
														</a>
													</div>
													<div class="cp-available-qty cp-available-qty-m<?php if((empty($qty_warehouse) AND $related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] == 0) AND $related_item['status'] != '5'): ?> not-available-in-store<?php endif; ?><?php if($related_item['status'] == '5' OR (!$related_item['is_available'] AND (empty($qty_warehouse) OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] < 1)))): ?> not-available<?php endif; ?>">
														<?php if(($related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0)): ?>
															<?php if(!empty($qty_warehouse['available_qty'])): ?>
																<?php echo $qty_warehouse['available_qty']; ?>
															<?php else: ?>
																0
															<?php endif; ?>
															(<?php echo max(0, floor($related_item['available_qty'])); ?>)
														<?php elseif(!empty($related_item['date_available'])): ?>
															<?php echo substr($related_item['date_available'], 0, -4); ?>
														<?php else: ?>
															-
														<?php endif; ?>
													</div>
												</div>
											</div>

											<!-- FIXME PROG ispisati sve atribute -->
											<?php if(!empty($related_item['attributes_special'])): ?>
												<div class="cp-attributes">
													<?php foreach($related_item['attributes_special'] as $item_attr): ?>
														<span class="cp-attribute"><?php echo $item_attr['attribute_title']; ?> <strong><?php echo $item_attr['title']; ?></strong><span class="comma">, <span><span>
													<?php endforeach; ?>
												</div>
											<?php endif; ?>
										</div>

										<div class="cp-price cd-bt-item-price">
											<div class="cd-bt-price-row1">
												<?php if ($related_item['discount_percent_custom'] > 0): ?>
													<span class="cp-old-price line-through cp-old-price-small"><?php echo Utils::currency_format($related_item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
													<span class="cp-discount-price cp-current-price red" data-currency_format="full_price_currency"><?php echo Utils::currency_format($related_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
												<?php else: ?>
													<span class="cp-current-price" data-currency_format="full_price_currency"><?php echo Utils::currency_format($related_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
												<?php endif; ?>
												<?php if (!empty($related_item['discount_expire'])): ?>
													<div class="cp-discount-expire cd-bt-discount-expire"><?php echo str_replace('%d%', date('d.m.Y.', $related_item['discount_expire']), Arr::get($cmslabel, 'pa_discount_expire')) ?></div>
												<?php endif; ?>
											</div>
											<?php if (!empty($related_item['loyalty_price_custom'])): ?>
												<div class="cp-current-price cp-loyalty-price" data-product_price="<?php echo $related_item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($related_item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></span></div>
											<?php endif; ?>
										</div>
										<span class="bought-together-message product_message product_message_<?php echo $related_item['shopping_cart_code']; ?>" style="display: none"></span>
									</div>

									<div class="cd-bt-col2">
										<div class="cd-bt-item-image">
											<a href="<?php echo $related_item['url']; ?>" title="<?php echo Text::meta($related_item['title']); ?>">
												<img <?php echo Thumb::generate($related_item['main_image'], ['width' => 60, 'height' => 60, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-60.jpg', 'srcset' => '120c 2x']); ?> alt="<?php echo $related_item['title']; ?>" title="<?php echo Text::meta($related_item['main_image_title']); ?>" />
											</a>
										</div>
										<div class="cp-available-qty<?php if((empty($qty_warehouse) AND $related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] == 0 AND $related_item['status'] != '5')): ?> not-available-in-store<?php endif; ?><?php if($related_item['status'] == '5' OR (!$related_item['is_available'] AND (empty($qty_warehouse) OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] < 1)))): ?> not-available<?php endif; ?>">
											<?php if(($related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0)): ?>
												<?php if(!empty($qty_warehouse['available_qty'])): ?>
													<?php echo $qty_warehouse['available_qty']; ?>
												<?php else: ?>
													0
												<?php endif; ?>
												(<?php echo max(0, floor($related_item['available_qty'])); ?>)
											<?php elseif(!empty($related_item['date_available'])): ?>
												<?php echo substr($related_item['date_available'], 0, -4); ?>
											<?php else: ?>
												-
											<?php endif; ?>
										</div>
										<div class="cd-bt-item-checkbox-container">
											<input type="hidden" name="price[<?php echo $related_item['shopping_cart_code']; ?>]" value="<?php echo $related_item['price_custom']; ?>" />
											<input type="checkbox" name="product_special_list_recommendation" id="product_special_list_recommendation-<?php echo $related_item['shopping_cart_code']; ?>" value="<?php echo $related_item['shopping_cart_code']; ?>" onclick="javascript:cmswebshop.shopping_cart.calculate_special_list('recommendation');" <?php if($related_item['code'] == $item['code']): ?>checked="checked"<?php endif; ?> <?php if($related_item['code'] != $item['code']): ?>autocomplete="off"<?php endif; ?> />
											<label for="product_special_list_recommendation-<?php echo $related_item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></label>
										</div>
									</div>
								</div>
								<?php $r++; ?>
							<?php endforeach; ?>
						</div>
					</div>
				</div>

				<div class="cd-bt-category">
					<div class="cd-subtitle cd-bt-category-title"><?php echo Arr::get($cmslabel, 'pa_bought_together_categories'); ?></div>
					<div class="cd-bt-category-items">
						<?php foreach ($list_recommendation as $related_item): ?>
							<a href="<?php echo $related_item['category_url']; ?>" class="cd-bt-category-item"><?php echo $related_item['category_title']; ?></a>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>

			<div class="cd-add-to">
				<div class="cd-add-to-section">
					<!-- Compare list -->
					<?php if (isset($item['compare_widget'])): ?>
						<?php echo View::factory('catalog/widget/set_compare', ['content' => $item['compare_widget']['content'], 'active' => $item['compare_widget']['active']]); ?>
					<?php endif; ?>
                    <?php if (empty(Arr::get($item, 'temporary_unavailable')) AND $item['type'] != 'configurable'): ?>
						<!-- Add to cart section (if no variations) -->
						<?php if ((float)$item['price_custom'] AND ($item['is_available'] OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0))): ?>
							<div class="add-to-cart-container">
								<span style="display: none;" data-product_category_title="1"><?php echo Arr::get($item, 'category_title'); ?></span>
								<span style="display: none;" data-product_manufacturer_title="1"><?php echo Arr::get($item, 'manufacturer_title'); ?></span>
								<?php $available_qty = $use_pa_warehouses ? $item['available_pa_qty'] : ($item['available_qty'] + (int) $qty_warehouse_available_qty); ?>
								<?php $availability_condition = $use_pa_warehouses ? ($item["available_pa_qty"] < 1) : ($item["available_qty"] + (int) $qty_warehouse_available_qty < 1); ?>
								<?php $single_item_condition = $use_pa_warehouses ? ($item["available_pa_qty"] == 1) : ($item["available_qty"] + (int) $qty_warehouse_available_qty == 1); ?>
								<?php if($availability_condition AND empty($my_location_point_availability_info)): ?>
									<div class="cd-not-available"><?php echo Arr::get($cmslabel, 'not_available')?></div>
								<?php elseif ($item['type'] == 'advanced' AND !empty($item["type_config"])): ?>
                                    <?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
                                        <div class="cd-qty-container<?php if($item['available_qty'] == 1): ?> cd-single<?php endif; ?> <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected == $item_variation_code): ?>active<?php endif; ?>" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
                                            <div class="cd-qty">
                                                <a class="cd-btn-qty cd-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('variation_<?php echo $item_variation['code']; ?>', '-', 1, 0, 1);"></a>
                                                <input type="text" name="qty[variation_<?php echo $item_variation['code']; ?>]" class="cd-input-qty product_qty_input" value="1" />
                                                <a class="cd-btn-qty cd-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('variation_<?php echo $item_variation['code']; ?>', '+', 1, 0, 1);"></a>
                                                <?php if (!empty($item_variation['shopping_cart_code_multiple'])): ?>
                                                    <?php foreach ($item_variation['shopping_cart_code_multiple'] AS $item_variation_shopping_cart_code): ?>
                                                        <input type="hidden" name="product_special_list_variation_<?php echo $item_variation['code']; ?>" value="<?php echo $item_variation_shopping_cart_code; ?>" />
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <a class="btn btn-green cd-btn-add" href="javascript:cmswebshop.shopping_cart.add_special_list('variation_<?php echo $item_variation['code']; ?>', 'all', '8', 'input', 'simple_loader', '_tracking:detail,_service_extra,_shipping')" data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>>
                                           <?php if ($item['status'] == '5'): ?>
												<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart_preorder'); ?></span>
											<?php else: ?>
												<?php if ($item['type'] == 'advanced'): ?>
													<span data-variation_code="<?php echo $item['code']; ?>" <?php if (!empty($item_type_advanced_selected)): ?>style="display: none"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
													<?php if (!empty($item["type_config"])): ?>
														<?php foreach ($item["type_config"] AS $item_variation_code => $item_variation): ?>
															<span data-variation_code="<?php echo $item_variation['code']; ?>" <?php if (!empty($item_type_advanced_selected) AND $item_type_advanced_selected != $item_variation_code): ?>style="display: none;"<?php endif; ?>><?php echo Utils::currency_format($item_variation['price'] * $currency['exchange'], $currency['display']); ?></span>
														<?php endforeach; ?>
													<?php endif; ?>
												<?php else: ?>
													<span data-product_price_number="<?php echo $product_price_number; ?>">
														<?php echo $product_output_price; ?>
													</span>
												<?php endif; ?>
											<?php endif; ?>
                                        </a>
                                    <?php endforeach; ?>
								<?php else: ?>
									<div class="cd-qty-container<?php if ($single_item_condition): ?> single<?php endif; ?>">
										<div class="cd-qty">
											<a class="cd-btn-qty cd-btn-dec" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '-', 1, 0, 1);"></a>
											<input type="text" name="qty[<?php echo $item['shopping_cart_code']; ?>]" class="cd-input-qty product_qty_input" value="1" readonly/>
											<a class="cd-btn-qty cd-btn-inc" href="javascript:cmswebshop.shopping_cart.change_qty('<?php echo $item['shopping_cart_code']; ?>', '+', 1, 0, 1);"></a>
										</div>
									</div>
									<a class="btn btn-green cd-btn-add" href="<?php if(!empty($list_recommendation)): ?>javascript:cmswebshop.shopping_cart.add_special_list('recommendation:<?php echo $item['shopping_cart_code']; ?>|<?php echo $recommendation_total['list_id']; ?>', 'checkbox', 8, 'input', 'simple_loader', '_tracking:detail,_service_extra,_shipping')<?php else: ?>javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:detail,_service_extra,_shipping', 'simple_loader', 'input', 8)<?php endif;?>">
										<?php if ($item['status'] == '5'): ?>
											<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart_preorder'); ?></span>
										<?php else: ?>
											<span data-product_price_number="<?php echo $product_price_number; ?>">
												<?php echo $product_output_price; ?>
											</span>
										<?php endif; ?>
									</a>
									<?php endif; ?>
								<?php
								/*if(!empty($list_recommendation)): ?>
									<div class="cd-bought-together-list-message product_message_list"></div>
								<?php endif;*/ ?>
							</div>
						<?php endif; ?>
					<?php endif; ?>
					<div class="cd-product-message product_message product_message_<?php echo $item['shopping_cart_code'] ?>" style="display: none"></div>
				</div>
			</div>
		</div>
	</div>
<?php $this->endblock('content'); ?>