<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-landing-catalog<?php $this->endblock('page_class'); ?>

<?php $search_fields = Widget_Catalog::search_filters(array('filters' => $filters, 'hide_total_0' => TRUE, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1), 'price_display' => $currency['display'], 'price_exchange' => $currency['exchange'])); ?>
<?php $active_filters = (isset($search_fields['_basic']['selected']) AND $search_fields['_basic']['selected']) ? $search_fields['_basic']['selected'] : array(); ?>

<?php $this->block('content'); ?>
	<?php echo View::factory('catalog/widget/categories'); ?>
<?php $this->endblock('content'); ?>