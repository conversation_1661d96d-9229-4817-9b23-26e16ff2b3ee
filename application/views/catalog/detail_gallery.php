<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<title><?php echo $item['seo_title']; ?></title>
	<?php echo Html::media('fancybox,standard', 'css', FALSE); ?>
	<?php echo Html::media('modernizr', 'js', FALSE); ?>
	<meta name="format-detection" content="telephone=no" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>
<body class="gallery">

<div class="slider" id="slider" data-elem="slider">
	<div class="gallery-caption"><?php echo Arr::get($cmslabel, 'scroll_to_zoom'); ?></div>
	<div class="slides" data-elem="slides" data-options="preloaderUrl:/media/images/loader.svg;loop:false;maxZoom:3"></div>
	
	<?php 
	$images = Utils::get_files('catalogproduct', $item['id'], 'image', $info['lang']); 
	if (!empty($item['energy_image'])) {
		array_push($images, [
			"file" => $item['energy_image'], 
			"url" => Utils::file_url($item['energy_image']), 
			"kind" => "image", 
			"title" => Arr::get($cmslabel, 'energy_image_title'), 
			"description" => $item['energy_version'],
		]);
	}

    if ($item['type'] == 'advanced' AND !empty($item["type_config"])) {
        foreach ($item["type_config"] AS $item_variation_code => $item_variation) {
            if (!empty($item_variation['products'])) {
                foreach ($item_variation['products'] AS $item_variation_product) {
                    if (!empty($item_variation_product['main_image'])) {
                        $item_variation_product_images = Utils::get_files('catalogproduct', $item_variation_product['id'], 'image', $info['lang']);
                        foreach ($item_variation_product_images AS $item_variation_product_image) {
                            array_push($images,
                                Arr::merge($item_variation_product_image, ['variation_code' => $item_variation_code])
                            );
                        }
                    }
                }
            }
        }
    }
	?>
	
	<div class="gsControl gsPrev midLeft" data-elem="prev" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0; cursor:default"> </div>
	<div class="gsControl gsNext midRight" data-elem="next" data-on="autoAlpha:1; cursor: pointer;" data-off="autoAlpha:0; cursor:default"> </div>

	<ul data-elem="items">
		<?php foreach($images as $image): ?>
			<li><img loading="lazy" src="<?php echo Utils::file_url($image['file']); ?>" alt="<?php echo $image['title']; ?>"></li>
		<?php endforeach; ?>
	</ul>
</div>

<?php echo Html::media('js_gallery', 'js', FALSE); ?>
<script>
$(window).load(function() {
	var gallery = TouchNSwipe.getSlider("slider");
	var slideIndex = <?php echo (!empty($_GET['index'])) ? $_GET['index'] : 0; ?>;
	gallery.index(slideIndex);
});
</script>
<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
</body>
</html>