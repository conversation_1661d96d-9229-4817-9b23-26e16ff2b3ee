<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
	<title><?php echo $item['seo_title']; ?></title>
	<?php echo Html::media('fancybox,standard', 'css', FALSE); ?>
	<?php echo Html::media('modernizr', 'js', FALSE); ?>
	<meta name="format-detection" content="telephone=no" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=block" rel="stylesheet">
	<?php $this->block('extrahead'); ?><?php $this->endblock('extrahead'); ?>
</head>

<body>
	<?php $list_recommendation_modal = Widget_Catalog::products(['lang' => $info['lang'], 'related_code' => 'bought_together', 'related_item_id' => $item['id'], 'related_item_add' => $item, 'only_available' => TRUE]); ?>
	<?php $recommendation_total_selected = $item['price_custom']; ?>
	<?php $recommendation_products_selected = 1; ?>
	<?php $recommendation_products_total = 1; ?>
	<?php $total_items = sizeof($list_recommendation_modal); ?>

	<?php 
	$product_priorities = Kohana::config('app.catalog.product_priorities');
	$priority = Arr::get($product_priorities, $item['priority_2']);
	?>

	<div class="cm">
		<?php if(!empty($list_recommendation_modal)): ?>
			<div class="cm-col cm-col1">
				<?php ?>
				<?php $recommendation_total = $list_recommendation_modal['_basic'];
					unset($list_recommendation_modal['_basic']); 
					$r = 1; 
				?>

				<!-- FIXME PROG omoguciti odabir proizvoda s liste u modalnom prozoru -->
				<div class="bought-together cm-bought-together" id="bought-together">
					<div class="cm-bought-related-title"><?php echo Arr::get($cmslabel, 'pa_bought_together'); ?></div>
					<div id="product_special_list_recommendation" class="cm-items-list items-list">
						<?php foreach ($list_recommendation_modal as $related_item): ?>
							<?php $recommendation_total_selected += $related_item['price_custom']; ?>
							<?php $recommendation_products_selected += 1; ?>
							<?php $recommendation_products_total += 1; ?>

							<div id="product_special_list_recommendation-descriptions-<?php echo $related_item['shopping_cart_code']; ?>" class="cd-bought-together-item<?php if($related_item['code'] == $item['code']): ?> main<?php endif; ?><?php if($r == $total_items): ?> last<?php endif; ?> clear">
								<div class="cp-col1 cd-bt-item-content">
									<div class="cp-col1-top">
										<div class="cp-info-top">
											<?php $priority = Arr::get($product_priorities, $related_item['priority_2']); ?>
											<div class="cp-badges">
												<?php if ($related_item['discount_percent'] > 0 OR $related_item['price_custom'] < $related_item['basic_price']): ?>
													<div class="cp-badge cp-badge-discount discount"><span>-<?php echo $related_item['discount_percent']; ?>%</span></div>
												<?php endif; ?>

												<?php if ($priority): ?>
													<div class="cp-badge cp-badge-new new"><span><?php echo Arr::get($priority, 'title'); ?></span></div>
												<?php endif; ?>
											</div>
											<h2 class="cp-title cp-bt-title" data-product_title="<?php echo $related_item['shopping_cart_code']; ?>">
												<a href="<?php echo $related_item['url']; ?>"  target="_parent"><?php echo $related_item['title']; ?></a>
											</h2>
											<div class="cp-info">
												<div class="cp-code" data-product_code="<?php echo $related_item['shopping_cart_code']; ?>"><strong><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <?php echo $related_item['code']; ?></div>
												<?php if(!empty($related_item['category_title'])): ?>
													<div class="cp-category"><a href="<?php echo $related_item['category_url']; ?>" target="_parent"><?php echo $related_item['category_title']; ?></a></div>	
												<?php endif; ?>
											</div>

											<div class="cp-info-top-right">
												<a href="<?php echo $related_item['url']; ?>" target="_parent" class="cp-no-image"><img src="/media/images/no-image-60.jpg" alt=""></a>
												<div class="cp-available-qty cp-available-qty-m<?php if((empty($qty_warehouse) AND $related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] == 0) AND $related_item['status'] != '5'): ?> not-available-in-store<?php endif; ?><?php if($related_item['status'] == '5' OR (!$related_item['is_available'] AND (empty($qty_warehouse) OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] < 1)))): ?> not-available<?php endif; ?>">
													<?php if(($related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0)): ?>
														<?php if(!empty($qty_warehouse['available_qty'])): ?>
															<?php echo $qty_warehouse['available_qty']; ?> 
														<?php else: ?>
															0
														<?php endif; ?>
														(<?php echo floor($related_item['available_qty']); ?>)
													<?php elseif(!empty($related_item['date_available'])): ?>
														<?php echo substr($related_item['date_available'], 0, -4); ?>
													<?php else: ?>
														-
													<?php endif; ?>
												</div>
											</div>
										</div>

										<!-- FIXME PROG ispisati sve atribute -->
										<?php if(!empty($related_item['attributes_special'])): ?>
											<div class="cp-attributes">
												<?php foreach($related_item['attributes_special'] as $item_attr): ?>
													<span class="cp-attribute"><?php echo $item_attr['attribute_title']; ?> <strong><?php echo $item_attr['title']; ?></strong><span class="comma">, <span><span>
												<?php endforeach; ?>
											</div>
										<?php endif; ?>
									</div>

									<div class="cp-price cd-bt-item-price">
										<div class="cd-bt-price-row1">
											<?php if ($related_item['discount_percent'] > 0): ?>
												<span class="cp-old-price line-through cp-old-price-small"><?php echo Utils::currency_format($related_item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></span>
												<span class="cp-discount-price cp-current-price red" data-currency_format="full_price_currency"><?php echo Utils::currency_format($related_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
											<?php else: ?>
												<span class="cp-current-price" data-currency_format="full_price_currency"><?php echo Utils::currency_format($related_item['price_custom'] * $currency['exchange'], $currency['display']); ?></span>
											<?php endif; ?>
											<?php if (!empty($related_item['discount_expire'])): ?>
												<div class="cp-discount-expire cd-bt-discount-expire"><?php echo str_replace('%d%', date('d.m.Y.', $related_item['discount_expire']), Arr::get($cmslabel, 'pa_discount_expire')) ?></div>
											<?php endif; ?>
										</div>
										<?php if (!empty($related_item['loyalty_price_custom'])): ?>
											<div class="cp-current-price cp-loyalty-price" data-product_price="<?php echo $related_item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($related_item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?></span></div>
										<?php endif; ?>
									</div>
									<span class="bought-together-message product_message product_message_<?php echo $related_item['shopping_cart_code']; ?>" style="display: none"></span>
								</div>
								
								<div class="cd-bt-col2">
									<div class="cd-bt-item-image">
										<a href="<?php echo $related_item['url']; ?>" target="_parent" title="<?php echo Text::meta($related_item['title']); ?>">
											<img <?php echo Thumb::generate($related_item['main_image'], ['width' => 60, 'height' => 60, 'html_tag' => TRUE, 'default_image' => '/media/images/no-image-60.jpg', 'srcset' => '120c 2x']); ?> alt="<?php echo $related_item['title']; ?>" title="<?php echo Text::meta($related_item['main_image_title']); ?>" />
										</a>
									</div>
									<div class="cp-available-qty<?php if((empty($qty_warehouse) AND $related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] == 0 AND $related_item['status'] != '5')): ?> not-available-in-store<?php endif; ?><?php if($related_item['status'] == '5' OR (!$related_item['is_available'] AND (empty($qty_warehouse) OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] < 1)))): ?> not-available<?php endif; ?>">
										<?php if(($related_item['is_available'] AND $related_item['status'] != '5') OR (!empty($qty_warehouse) AND $qty_warehouse['available_qty'] > 0)): ?>
											<?php if(!empty($qty_warehouse['available_qty'])): ?>
												<?php echo $qty_warehouse['available_qty']; ?> 
											<?php else: ?>
												0
											<?php endif; ?>
											(<?php echo floor($related_item['available_qty']); ?>)
										<?php elseif(!empty($related_item['date_available'])): ?>
											<?php echo substr($related_item['date_available'], 0, -4); ?>
										<?php else: ?>
											-
										<?php endif; ?>
									</div>
									<div class="cd-bt-item-checkbox-container">
										<input type="hidden" name="price[<?php echo $related_item['shopping_cart_code']; ?>]" value="<?php echo $related_item['price_custom']; ?>" />
										<input type="checkbox" name="product_special_list_recommendation" id="product_special_list_recommendation-<?php echo $related_item['shopping_cart_code']; ?>" value="<?php echo $related_item['shopping_cart_code']; ?>" onclick="javascript:cmswebshop.shopping_cart.calculate_special_list('recommendation');" <?php if($related_item['code'] == $item['code']): ?>checked="checked"<?php endif; ?> <?php if($related_item['code'] != $item['code']): ?>autocomplete="off"<?php endif; ?> />
										<label for="product_special_list_recommendation-<?php echo $related_item['shopping_cart_code']; ?>"><?php echo Arr::get($cmslabel, 'bought_together_label'); ?></label>
									</div>
								</div>
							</div>
							<?php $r++; ?>
						<?php endforeach; ?>
					</div>
				</div>
				<?php ?>
			</div>
		<?php endif; ?>

		<div class="cm-col cm-col2">
			<div class="cm-message"><?php echo Arr::get($_GET, 'global_label'); ?></div>
			<h2 class="cm-title" data-product_title="1"><?php echo $item['seo_h1'] ?></h2>
			<div class="cm-code" data-variation_box="description" data-variations="1" data-variation_active_id="<?php echo Arr::get($active_variation, 'id'); ?>">
				<div data-variation_item_description="<?php echo $item['shopping_cart_code']; ?>"><strong><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <span data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['code']; ?></span></div>
			</div>

			<?php if($item['price_custom'] > 0): ?>
				<?php $is_loyalty = (!empty($user->loyalty_code)); ?>
				<?php $installments_min_price = Utils::currency_format((!empty(Arr::get($item, 'installments_calculation')['regular'])) ? reset($item['installments_calculation']['regular']) : 0 * $currency['exchange'], $currency['display']); ?>
				<?php $installment_price = Arr::get($item, 'installments_calculation')['regular']; ?>
				<?php $installment_loyalty_price = Arr::get($item, 'installments_calculation')['loyalty']; ?>
				<?php if (is_array($installment_price)) : $installment_price = reset($installment_price); endif; ?>
				<?php if (is_array($installment_loyalty_price)) : $installment_loyalty_price = reset($installment_loyalty_price); endif; ?>
				<?php if (!empty($installment_loyalty_price) AND $is_loyalty): $installment_price = $installment_loyalty_price; endif;?>

				<div class="cm-price"<?php if(!empty($item['installments_calculation']['regular'])): ?> installments-price<?php endif; ?>" data-variation_box="price">
					<div class="cm-price-cnt<?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?> inactive<?php endif; ?>" data-variation_item_price="<?php echo $item['shopping_cart_code']; ?>">
						<?php if (($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
							<div class="cd-old-price line-through"<?php if(!$is_loyalty): ?>  data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
							<div class="cd-current-price red" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
								(-<?php echo $item['discount_percent']; ?>%) <?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo (!empty($installment_price)) ? '<span class="cd-installments-price">'. str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'pa_installments_price_text')).'</span>' : ""; ?>
							</div>
						<?php elseif($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price']): ?>
							<div class="cd-old-price line-through"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
							<div class="cd-current-price green" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>>
								<?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo (!empty($installment_price)) ? '<span class="cd-installments-price">'. str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'pa_installments_price_text')).'</span>' : ""; ?>
							</div>
						<?php else: ?>
							<div class="cd-current-price" <?php if (!empty($item['price_custom_prices_cart']) AND !empty($item['price_custom_prices_cart_expire'])): ?>data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"<?php elseif(!$is_loyalty): ?>data-product_price="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?>><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
								<?php echo (!empty($installment_price)) ? '<span class="cd-installments-price">'. str_replace("%PRICE%", Utils::currency_format($installment_price * $currency['exchange'], $currency['display']), Arr::get($cmslabel, 'pa_installments_price_text')).'</span>' : ""; ?>
							</div>
						<?php endif; ?>
					</div>
				</div>
			<?php endif; ?>
			
			<!-- FIXME PROG omoguciti odabir servisa u modalnom prozoru -->
			<?php if ($item['is_available'] AND !empty($item['services'])): ?>
				<div class="cm-extra-benefits-section">
					<div class="cm-extra-benefits">
						<?php foreach ($item['services'] AS $item_service): ?>	
							<?php if (empty($item_service['items_available'])) {continue;} ?>
							<div class="cd-extra-benefits-header">
								<span class="cm-subtitle cm-extra-benefits-title"><?php echo $item_service['title']; ?></span>
							</div>
							<?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
								<?php if (empty($item_service_item['active'])) {continue;} ?>
								<div class="cd-extra-benefit-item cm-extra-benefit-item">
									<div class="cd-extra-benefit-title">
										<span data-service_extra_title="<?php echo $item_service_item['id']; ?>"><?php echo $item_service_item['title']; ?><?php if($item_service_item['description']): ?><span class="cd-extra-benefit-icon"></span><?php endif; ?></span>
										<?php if($item_service_item['description']): ?>
											<div class="cd-extra-benefit-tooltip">
												<span class="cd-tooltip-close"></span>
												<?php echo $item_service_item['description']; ?>
											</div>
										<?php endif; ?>
									</div>
                                    <?php
                                        $checked = false;
                                        $shopping_cart_services = isset($shopping_cart_status[$item['shopping_cart_code']]) ? $shopping_cart_status[$item['shopping_cart_code']]['services'] : false;
                                        if(!empty($shopping_cart_services)){
                                            if(in_array($item_service_item_id, $shopping_cart_services[$item_service['id']]['selected_ids'])){
                                                $checked = true;
                                            }
                                        }
                                    ?>
									<?php if ($item_service['type'] == 's'): ?>
										<input type="radio" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $item['shopping_cart_code'] ?>" data-service_extra_category="<?php echo $item_service['code']; ?>" <?php if($checked){ ?> checked <?php } ?>/>
									<?php elseif ($item_service['type'] == 'c'): ?>
										<input type="checkbox" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" data-service_extra="<?php echo $item['shopping_cart_code'] ?>" data-service_extra_category="<?php echo $item_service['code']; ?>" <?php if($checked){ ?> checked <?php } ?>/>
									<?php endif; ?>
									<label class="cd-extra-benefit" for="service-<?php echo $item_service_item_id; ?>">
										<div class="cd-extra-benefit-price">+ <span data-service_extra_price="<?php echo $item_service_item['id']; ?>"><?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?></span></div>														
									</label>
								</div>
							<?php endforeach; ?>

							<?php if ($item_service['type'] == 's'): ?>
								<div class="cd-extra-benefit-item special"  data-service_extra_none="<?php echo $item_service['code']; ?>" style="display: none;">
									<input type="radio" name="services[]" value="" id="service-0" data-service_extra="<?php echo $item['shopping_cart_code']; ?>"  data-service_extra_category="<?php echo $item_service['code']; ?>" />
									<label class="cd-extra-benefit" for="service-0">
										<div class="cd-extra-benefit-title" data-service_extra_title="<?php echo $item_service_item['id']; ?>">
											<?php echo Arr::get($cmslabel, 'no_warranty'); ?>
										</div>
									</label>

									</div>
								<?php $none_insurance = true; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					</div>
				</div>

				<div data-shoppingcart_product_service_TMP="1" style="display: none;">
					<?php foreach ($item['services'] AS $item_service): ?>	
						<?php $none_insurance = false; ?>
						<?php foreach ($item_service['items'] AS $item_service_item_id => $item_service_item): ?>
							<?php if ($item_service['type'] == 's' AND !$none_insurance AND !empty($item_service['code']) AND $item_service['code'] == 'insurance'): ?>
								<div class="modal-extra-service-item">
									<input type="radio" name="services[]" value="" id="service-0" />
									<label class="modal-extra-service" for="service-0">
										<div class="modal-extra-service-title">
											<?php echo Arr::get($cmslabel, 'no_warranty'); ?>
										</div>
									</label>
								</div>
								<?php $none_insurance = true; ?>
							<?php endif; ?>

							<div class="modal-extra-service-item">
								<div class="modal-extra-service-title">
									<?php echo $item_service_item['title']; ?>
								</div>
								<?php if ($item_service['type'] == 's'): ?>
									<input type="radio" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>"  />
								<?php elseif ($item_service['type'] == 'c'): ?>
									<input type="checkbox" name="services[]" value="<?php echo $item_service_item_id; ?>" id="service-<?php echo $item_service_item_id; ?>" />
								<?php endif; ?>
								<label class="modal-extra-service" for="service-<?php echo $item_service_item_id; ?>">
									<div class="modal-extra-service-price">
										+ <?php echo Utils::currency_format($item_service_item['price'] * $currency['exchange'], $currency['display']); ?>
									</div>
								</label>
							</div>
						<?php endforeach; ?>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>

			<?php if(!empty($list_recommendation_modal)): ?>
				<div class="cm-bt-category">
					<div class="cm-subtitle cm-bt-category-title"><?php echo Arr::get($cmslabel, 'pa_bought_together_categories'); ?></div>
					<div class="cd-bt-category-items">
						<?php foreach ($list_recommendation_modal as $related_item): ?>
							<a href="<?php echo $related_item['category_url']; ?>"  target="_parent" class="cd-bt-category-item"><?php echo $related_item['category_title']; ?></a>
						<?php endforeach; ?>
					</div>
				</div>
			<?php endif; ?>

			<div class="cm-btn-container">
				<a class="btn btn-green cm-btn-add" href="<?php echo Utils::app_absolute_url($info['lang'], 'webshop'); ?>" target="_parent">
					<?php if ($item['status'] == '5'): ?>
						<span><?php echo Arr::get($cmslabel, 'cd_add_to_shopping_cart_preorder'); ?></span>
					<?php else: ?>
						<span>
							<?php if (($item['discount_percent'] > 0 OR $item['price_custom'] < $item['basic_price']) AND ((empty($item['loyalty_price_custom']) OR !$is_loyalty) OR ($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['price_custom'] < $item['loyalty_price_custom']))): ?>
								<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php elseif($is_loyalty AND !empty($item['loyalty_price_custom']) AND $item['loyalty_price_custom'] < $item['basic_price']): ?>
								<?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php else: ?>
								<?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?>
							<?php endif; ?>
						</span>
					<?php endif; ?>
				</a>
			</div>
		</div>
	</div>

	<?php $this->block('extrabody'); ?><?php $this->endblock('extrabody'); ?>
</body>
</html>