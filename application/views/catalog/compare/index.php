<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(($kind) ? $kind['seo_title'].((isset($kind['parents']) AND $kind['parents']) ? ' - '.implode(' - ', array_map(function($element) {return $element['title'];}, $kind['parents'])) : '') : Arr::get($cms_page, 'seo_title')); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($pagination->current_page > 1): ?><?php echo sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages); ?><?php endif; ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => isset($cms_page) ? $cms_page : [],'kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-compare<?php $this->endblock('page_class'); ?>


<?php $this->block('content'); ?>
	<div class="compare-cnt">
		<div class="wrapper wrapper-compare">
			<?php 
			$all_attributes = [];
			foreach ($items as $item) {
				if (!empty($item['attributes'])) {
					$all_attributes = Arr::merge($all_attributes, $item['attributes']);
				}
			} 

			$attributes = [];
			$last_category_cpde = '';
			$last_attribute_code = '';
			foreach ($all_attributes AS $attribute) {
				if ($attribute['attribute_code'] != $last_attribute_code) {
					$attributes[$attribute['attribute_code']] = $attribute;
					$last_attribute_code = $attribute['attribute_code'];
				}
			}
			?>

			<div class="compare-sidebar-header">
				<div class="page-compare-title-container">
					<h1 class="page-compare-title"><?php echo Arr::get($cmslabel, 'compare_products_title', 'Usporedba proizvoda'); ?></h1>
				</div>
				<?php if(count($items) > 0): ?>
					<div class="compare-pager">
						<?php $i = 0; ?>
						<?php foreach ($items as $item): ?>
							<div class="compare-page<?php if($i == 0): ?> active<?php endif; ?>" data-index="<?php echo $i; ?>"><?php echo $i + 1; ?></div>
							<?php $i++; ?>
							<?php //provjera ako postoji više od 3 proizvoda u usporedilici ?>
							<?php if(!($i >= 0 AND $i <= 2)): ?>
								<?php break; ?>
							<?php endif; ?>
						<?php endforeach; ?>
						<?php if (count($items) < 3): ?>
							<div class="btn-compare-add compare-page" data-index="<?php echo $i; ?>"><?php echo $i + 1; ?></div>
						<?php endif ?>
					</div>
				<?php endif; ?>
			</div>
			
			<div class="fz0 clear c-compare-items<?php if (count($items) > 0): ?> compare-slider<?php else: ?> no-compare-slider<?php endif; ?>">
				<?php if (!empty($items)): ?>
					<?php echo View::factory('catalog/index_entry_compare', ['items' => $items, 'compare_page' => 1, 'attributes' => $attributes]); ?>
				<?php endif; ?>

				<?php if(count($items) < 3): ?>
					<article class="cp-special cp-compare cp-new-compare<?php if(!$items): ?> no-compare-items<?php endif; ?>">
						<div class="cp-compare-header">
							<form class="cp-compare-form" action="?special_view=compare" method="GET">
								<input class="cp-compare-input" name="compare_autocomplete-new" type="text" placeholder="<?php echo Arr::get($cmslabel, 'compare_add_title', 'Upišite naziv proizvoda'); ?>" data-id_exclude="<?php echo ($items) ? implode(',', array_keys($items)) : ''; ?>">
							</form>
						</div>
					</article>
				<?php endif; ?>
			</div>

			<?php if($items): ?>	
				<div class="c-compare-sidebar-attributes" data-total-attributes="<?php echo count($all_attributes); ?>">
					<?php if(count($items) > 1): ?>	
						<div class="c-compare-btns-cnt">
							<a href="javascript:void(0);" data-compare_mode="all" class="c-compare-btn c-compare-btn-all active"><span><?php echo Arr::get($cmslabel, 'compare_all_details'); ?></span></a>
							<a href="javascript:void(0);" data-compare_mode="diff" class="c-compare-btn c-compare-btn-diff"><span><?php echo Arr::get($cmslabel, 'compare_difference'); ?></span></a>
						</div>
					<?php endif; ?>
					<?php if ($all_attributes): ?>
						<table class="table-cp-attributes table-c-all-attributes<?php if(count($items) == 1): ?> all-attributes-border<?php endif; ?>">
							<?php $last_category_cpde = ''; ?>
							<?php $i = 1; ?>
							<?php foreach ($attributes AS $attribute): ?>
								<tr class="attr-row active attr-row<?php echo $i; ?>" data-row="<?php echo $i; ?>" data-compare_attribute="<?php echo $attribute['attribute_code']; ?>" data-compare_attribute_header="1">
									<td class="col-title"><?php echo $attribute['attribute_title']; ?></td>
								</tr>
								<?php $i++; ?>
							<?php endforeach; ?>
						</table>
					<?php endif; ?>
				</div>
			<?php endif; ?>
		</div>
	</div>
<?php $this->endblock('content'); ?>
