<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(Arr::get($cms_page, 'seo_title')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/default', ['cms_page' => isset($cms_page) ? $cms_page : []]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-manufacturers<?php $this->endblock('page_class'); ?>


<?php $this->block('content'); ?>
	<div class="m-header">
		<h1><?php echo Arr::get($cms_page, 'seo_h1'); ?></h1>
	</div>

	<?php $manufacturers_alphabet = Widget_Catalog::manufacturers(array('lang' => $info['lang'], 'limit' => 0, 'sort' => 'title', 'hierarhy_by_alphabet' => true)); ?>
	<div class="m-items">
		<?php if (sizeof($manufacturers_alphabet)): ?>
			<?php foreach($manufacturers_alphabet as $alphabet => $manufacturers): ?>
				<div class="m-column">
					<div class="m-letter" id="<?php echo $alphabet; ?>"><span><?php echo $alphabet; ?></span></div>
					<?php $i = 1; ?>
					<div class="m-list-section">
						<ul class="m-list">
							<?php foreach($manufacturers as $manufacturer): ?>
								<li><a href="<?php echo $manufacturer['url']; ?>"><?php echo $manufacturer['title']; ?></a></li>
								
								<?php $i++; ?>
							<?php endforeach; ?>
						</ul>
					</div>
				</div>
			<?php endforeach; ?>
			<div class="clear"></div>
		<?php else: ?>
			<?php echo Arr::get($cmslabel, 'no_manufacturers'); ?>
		<?php endif; ?>
	</div>
<?php $this->endblock('content'); ?>