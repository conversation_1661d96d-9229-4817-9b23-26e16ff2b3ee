<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['cms_page' => (!empty($cms_page) ? $cms_page : []), 'kind' => $kind, 'extra_kind' => (!empty($extra_kind) ? $extra_kind : []), 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-catalog<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>
<?php $search_fields = Widget_Catalog::search_filters(array('lang' => $info['lang'], 'filters' => $filters, 'hide_total_0' => TRUE, 'search_id' => ((isset($kind['search_id']) AND $kind['search_id']) ? $kind['search_id'] : 1), 'price_display' => $currency['display'], 'price_exchange' => $currency['exchange'])); ?>
<?php $active_filters = (!empty($search_fields['_basic']['selected'])) ? $search_fields['_basic']['selected'] : []; ?>

<div class="c-header">
	<h1 class="c-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?> <?php echo $q; ?><?php endif; ?></h1>
	
	<!-- Catalog toolbar -->
	<div class="c-toolbar">
		<?php if($items_total > 0 OR !empty($_GET['discount']) OR !empty($_GET['with_qty'])): ?>
			<div class="ci-checkbox-container">
				<?php if($items_total > 0 OR !empty($_GET['discount'])): ?>
					<div class="ci-checkbox ci-discount">
						<?php $discount_base_url = Url::query($_GET, FALSE, 'page,discount'); ?>
						<?php $discount_base_url .= ($discount_base_url)  ? '&' : '?'; ?>

						<?php if (!empty($_GET['discount'])): ?>
							<a href="<?php echo $discount_base_url; ?>" class="discount red c-btn-filter active"><?php echo Arr::get($cmslabel, 'pa_discounted_products', 'Akcija'); ?></a>
						<?php else: ?>
							<a href="<?php echo $discount_base_url; ?>discount=1" class="discount red c-btn-filter c-btn-filter-sale"><?php echo Arr::get($cmslabel, 'pa_discounted_products', 'Akcija'); ?></a>
						<?php endif; ?>
					</div>	
				<?php endif; ?>

				<?php if($items_total > 0 OR !empty($_GET['with_qty'])): ?>
					<div class="ci-checkbox ci-available">
						<?php $qty_base_url = Url::query($_GET, FALSE, 'page,with_qty'); ?>
						<?php $qty_base_url .= ($qty_base_url)  ? '&' : '?'; ?>
						<?php if (!empty($_GET['with_qty'])): ?>
							<a href="<?php echo $qty_base_url; ?>" class="green active"><?php echo Arr::get($cmslabel, 'pa_with_qty', 'Samo dostupni artikli'); ?></a>
						<?php else: ?>
							<a href="<?php echo $qty_base_url; ?>with_qty=1" class="green"><?php echo Arr::get($cmslabel, 'pa_with_qty', 'Samo dostupni artikli'); ?></a>
						<?php endif; ?> 
					</div>
				<?php endif; ?>
			</div>
		<?php endif; ?>

		<?php if($items_total > 0): ?>
			<?php echo View::factory('catalog/widget/legend'); ?>
		<?php endif; ?>

		<?php if ($items_total > 1): ?>
			<div class="c-toolbar-sort-container">
				<!-- Sort options -->
				<div class="sort c-sort">
					<?php $sort_base_url = Url::query($_GET, FALSE, 'page,sort'); ?>
					<?php $sort_base_url .= ($sort_base_url)  ? '&' : '?'; ?>
					<?php $selected_sort = Arr::get($_GET, 'sort', ''); ?>
					<select onchange="window.location.href=this.options[this.selectedIndex].value">
						<option value="<?php echo $sort_base_url; ?>"><?php echo Arr::get($cmslabel, 'ordering_priority', 'Priority'); ?></option> 
						<option value="<?php echo $sort_base_url; ?>sort=rates"<?php if ($selected_sort == 'rates'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_top_rated', 'Top rated'); ?></option> 
						<option value="<?php echo $sort_base_url; ?>sort=new"<?php if ($selected_sort == 'new'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_recent', 'Najnovije'); ?></option> 
						<option value="<?php echo $sort_base_url; ?>sort=old"<?php if ($selected_sort == 'old'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_older', 'Najstarije'); ?></option> 
						<option value="<?php echo $sort_base_url; ?>sort=expensive"<?php if ($selected_sort == 'expensive'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_expensive', 'Najskuplje'); ?></option>
						<option value="<?php echo $sort_base_url; ?>sort=cheaper"<?php if ($selected_sort == 'cheaper'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_cheaper', 'Najjeftinije'); ?></option>
						<option value="<?php echo $sort_base_url; ?>sort=az"<?php if ($selected_sort == 'az'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_az', 'A-Z'); ?></option>
						<option value="<?php echo $sort_base_url; ?>sort=za"<?php if ($selected_sort == 'za'): ?> selected="selected"<?php endif; ?>><?php echo Arr::get($cmslabel, 'ordering_za', 'Z-A'); ?></option>
					</select>
				</div>
			</div>
		<?php endif; ?>
	</div>
</div>

<div class="c-row">
	<div class="c-filters-section">
		<!-- Filter -->
		<?php if (!empty($search_fields['_basic']['available_fields'])): ?>
			<div class="cf">
				<form action="" method="get" id="attribute_filters_select" data-search_seofriendly_basic_full_url="<?php echo $info['search_seofriendly_basic_full_url']; ?>">		
					<?php echo $search_fields['_basic']['field']; ?>
					<?php if(!$q AND $kind_content != 'manufacturer'): ?>
						<div class="ci-categories">
							<?php if(empty($kind) OR $kind_content == 'list' OR $kind_content == 'manufacturer' AND !empty($cat_position)): ?>
								<?php $categories = Widget_Catalog::categories(['lang' => $info['lang'], 'level_range' => '1.1']); ?>
								<div class="ci-title cf-title"><?php echo Arr::get($cmslabel, 'filter_categories_title'); ?><span class="toggle-icon"></span></div>
								<div class="ci-title ci-title-m cf-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?> <?php echo $q; ?><?php endif; ?><span class="toggle-icon"></span></div>
							<?php else: ?>
								<?php $cat_position = !empty($kind['parents']) ? reset($kind['parents'])['position_h'] : $kind['position_h']; ?>
								<?php $cat_title = !empty($kind['parents']) ? reset($kind['parents'])['title'] : $kind['title']; ?>
								<?php $categories = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $cat_position, 'level_range' => '2.2']); ?>
								
								<div class="ci-title cf-title"><?php echo $cat_title; ?><span class="toggle-icon"></span></div>
								<div class="ci-title ci-title-m cf-title"><?php echo ($kind) ? $kind['seo_h1'] : Arr::get($cms_page, 'seo_h1'); ?><?php echo (isset($extra_kind) AND $extra_kind AND $extra_kind['seo_title']) ? ' - '.Text::meta($extra_kind['seo_title']) : ''; ?><?php if ($q): ?> <?php echo $q; ?><?php endif; ?><span class="toggle-icon"></span></div>
							<?php endif; ?>

							<?php if($categories): ?>
								<?php $curr_cat = Utils::extract_segments($info['lang'], $info['basic_url'], 3, 0, TRUE); ?>
								<ul class="ci-item-wrapper cf-item-wrapper" data-current_url="<?php echo $curr_cat; ?>">
									<?php foreach($categories as $category): ?>
										<?php if(!empty($kind)): ?>
											<?php $range = $category['level'] + 1; ?>
											<?php $range = $range.'.'.$range; ?>
											<?php $subcategories = Widget_Catalog::categories(['lang' => $info['lang'], 'start_position' => $category['position_h'], 'level_range' => $range]); ?>
											<li class="<?php if($subcategories): ?>has-children<?php endif; ?><?php if($category['url'] == $info['url']): ?> active<?php endif; ?>">
												<a href="<?php echo $category['url']; ?>">
													<span class="title"><?php echo $category['title']; ?></span>
													<span class="toggle-icon"></span>
												</a>
												<?php if($subcategories): ?>
													<ul>
														<?php foreach($subcategories as $subcategory): ?>
															<li>
																<a<?php if($subcategory['url'] == $info['url']): ?> class="active"<?php endif; ?> href="<?php echo $subcategory['url']; ?>">
																	<?php echo $subcategory['title']; ?>
																	<?php if($subcategory['total'] > 0): ?>
																		<span class="ci-counter"><?php echo $subcategory['total']; ?></span>
																	<?php endif; ?>
																</a>
															</li>
														<?php endforeach; ?>
														<li>
															<a href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all'); ?></a>
														</li>
													</ul>
												<?php endif; ?>
											</li>
										<?php else: ?>
											<li><a href="<?php echo $category['url']; ?>"><?php echo $category['title']; ?></a></li>
										<?php endif; ?>
									<?php endforeach; ?>
								</ul>
							<?php endif; ?>
						</div>
					<?php endif; ?>
					<?php foreach ($search_fields AS $search_filter_field => $search_filter): ?>
						<?php $options = Arr::get($search_filter, 'options_details'); ?>
						<?php $template = Arr::get($search_filter, 'template'); ?>
						<?php if (!$options AND $search_filter_field !== 'categories' OR (isset($search_filter['options_total_available']) AND $search_filter['options_total_available'] == 0 AND empty($search_filter['options_total_selected']))) {continue;} ?>
						<?php if ($search_filter_field == 'categories' AND $kind_content != 'manufacturer' AND !$q): ?>
							<?php $categories_tree = Widget_Catalog::categories(['lang' => $info['lang'], 'generate_tree' => true]); ?>
							<?php echo $categories_tree; ?>
						<?php else: ?>		
							<?php if (!$options AND $search_filter_field == 'categories' OR (isset($search_filter['options_total_available']) AND $search_filter['options_total_available'] == 0 AND empty($search_filter['options_total_selected']))) {continue;} ?>
							<div class="cf-item cf-item-<?php echo $search_filter_field; ?><?php if($search_filter['options_show']): ?> active<?php endif; ?><?php if($kind_content == 'manufacturer' OR $q): ?>cf-item-notitle<?php endif; ?>">
								
								<!-- Filter title  -->
								<?php if($search_filter_field != 'categories'): ?>
									<div class="cf-title" onClick="toggleBox('.cf-item-<?php echo $search_filter_field; ?>');">
										<?php $filter_title = Arr::get($cmslabel, 'f_filter_'.$search_filter_field); ?>
										<?php if($filter_title): ?>
											<?php echo $filter_title; ?>
										<?php else: ?>
											<?php echo Arr::get($search_filter, 'label', Arr::get($cmslabel, $search_filter_field, $search_filter_field)); ?>
										<?php endif; ?>
										<span class="toggle-icon"></span>	
									</div>
								<?php endif; ?>

								<div class="cf-item-wrapper">
									<?php if($search_filter['layout'] == 'sl' OR $search_filter['layout'] == 'sf'): ?>
										<div class="cf-row cf-row-<?php echo $search_filter_field; ?>">
											<div class="cf-range">
												<?php echo $search_filter['field']; ?>
											</div>
										</div>
									<?php else: ?>
										<?php $options_total = count($options); ?>
										<?php $i = 1; ?>
										<?php $visible_rows = 9; ?>
										<?php foreach ($options AS $option_id => $option): ?>
											<?php if ( ! $option_id) {$options_total--; continue;} ?>
											<?php if($options_total >= $visible_rows AND $i == $visible_rows): ?><div class="cf-row-expand cf-row-expand-<?php echo $search_filter_field; ?>"><?php endif; ?>
										<?php if ($search_filter_field == 'category' AND $option['total'] <= 0): ?>
										<?php if($options_total >= $visible_rows AND $i == $options_total): ?></div><a class="btn-cf-expand btn-cf-expand-<?php echo $search_filter_field; ?> active" href="javascript:toggleBox(['.cf-row-expand-<?php echo $search_filter_field; ?>', '.btn-cf-expand-<?php echo $search_filter_field; ?>']);"><?php echo Arr::get($cmslabel, 'show_all'); ?></a><?php endif; ?>
										<?php $i++; ?>
										<?php continue; ?>
									<?php endif; ?>
											<div class="cf-row<?php if(!empty($option['level'])): ?> cf-row-level<?php echo $option['level']; ?><?php endif; ?><?php if($option['total_available'] <= 0): ?> cf-row-not-available<?php endif; ?>">
												<?php if(!empty($option['level']) AND $option['level'] > 1 OR $search_filter_field != 'categories'): ?>
													<input<?php if($option['total'] <= 0): ?> disabled<?php endif; ?> type="checkbox" id="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>" name="<?php echo $search_filter_field; ?>" value="<?php echo $option_id; ?>"
													data-name_seo="<?php echo Arr::get($search_filter, 'slug'); ?>" data-name_ignore_seo="<?php echo Arr::get($search_filter, 'ignore_slug'); ?>" data-value_seo="<?php echo Arr::get($option, 'slug'); ?>"
														<?php if ($option['selected']): ?> checked<?php endif; ?><?php if (Arr::get($search_filter, 'element_multivalue')): ?> data-element_multivalue="1"<?php endif; ?><?php if ($search_filter_field == 'category' AND !empty($option['position'])): ?> data-filter_hierarhy_position="<?php echo $option['position']; ?>"<?php endif; ?> <?php if(count(explode('.', Arr::get($option, 'position'))) == 2): ?> data-filter_category_parent="1"<?php endif; ?>
													<?php if($search_filter_field == 'category' AND !empty(Arr::get($option, 'total'))): ?>data-total_items="<?php echo $option['total']; ?>"<?php endif; ?>>
												<?php endif; ?>
												<label class="<?php if($search_filter_field == 'a_barva-48' OR $search_filter_field == 'a_barva-pim-6698538'): ?>cf-label-color<?php endif; ?>" for="search-<?php echo $search_filter_field; ?>-<?php echo $i; ?>"><?php echo $option['title']; ?>
													<?php if($search_filter_field == 'a_barva-48' OR $search_filter_field == 'a_barva-pim-6698538'): ?>
														<span class="cf-color-img">
															<img <?php echo Thumb::generate($option['image'], array('width' => 20, 'height' => 20, 'crop' => true, 'default_image' => '/media/images/no-color.jpg', 'html_tag' => true, 'srcset' => '40c 2x')); ?> alt="" />
														</span>
													<?php endif; ?>
												</label>
												<?php if ($search_filter_field == 'category'): ?>
													<?php if($option['total'] > 0): ?><span class="cf-counter" <?php if(count(explode('.', Arr::get($option, 'position'))) == 2): ?> id="filter_category_parent-<?php echo $option['position']; ?>"<?php endif; ?>><?php echo $option['total']; ?></span><?php endif; ?>
												<?php else: ?>
											<?php if($option['total_available'] > 0): ?><span class="cf-counter"><?php echo $option['total_available']; ?></span><?php endif; ?>
												<?php endif; ?>
											</div>
											<?php if($options_total >= $visible_rows AND $i == $options_total): ?></div><a class="btn-cf-expand btn-cf-expand-<?php echo $search_filter_field; ?> active" href="javascript:toggleBox(['.cf-row-expand-<?php echo $search_filter_field; ?>', '.btn-cf-expand-<?php echo $search_filter_field; ?>']);"><?php echo Arr::get($cmslabel, 'show_all'); ?></a><?php endif; ?>
											<?php $i++; ?>
										<?php endforeach; ?>
									<?php endif; ?>
								</div>
							</div>
						<?php endif; ?>
					<?php endforeach; ?>	
					<input type="hidden" name="search_q" value="<?php echo Arr::get($_GET, 'search_q', ''); ?>" />
					
					<?php if (!empty($search_fields['discount']['field'])): ?>
						<div class="cf-item cf-item-sale">
							<?php echo $search_fields['discount']['field']; ?> <label for="searchfield-discount"><?php echo Arr::get($cmslabel, 'sale_item'); ?></label>
						</div>
					<?php endif; ?>

					<div class="cf-btn-container<?php if($active_filters): ?> active<?php endif; ?> clear">
						<?php if($active_filters): ?>
							<a href="<?php if($q): ?>?search_q=<?php echo $q ?><?php else: ?>?<?php endif; ?>" class="btn btn-white clear-filters">
								<span>
									<?php echo Arr::get($cmslabel, 'clear_filtering_btn'); ?>
								</span>
							</a>
						<?php endif; ?>

						<button class="btn btn-green confirm-filters" data-cmsfilter_manual_submit="1" data-cmsfilter_element="1" <?php if (empty($search_data)): ?> data-cmsfilter_empty="1" <?php endif; ?> type="submit">
							<span><?php echo Arr::get($cmslabel, 'confirm_filters'); ?></span>									
						</button>
					</div>
				</form>
			</div>
		<?php endif; ?>
	</div>

	<div class="c-items-section">
		<!-- Selected filters -->
		<?php if ($active_filters): ?>
			<div class="cf-active<?php if($q): ?> cf-active-search<?php endif; ?>">
				<?php foreach ($active_filters AS $active_filter_code => $active_filter): ?>
					<?php if ($active_filter['field'] == 'price' AND !empty($q)): ?>
						<a class="cf-active-item" href="<?php echo $active_filter['remove_url_seo']."?search_q=".$q; ?>">
							<span><?php echo $active_filter['title']; ?></span>
						</a>
					<?php else: ?>
					<a class="cf-active-item" href="<?php echo $active_filter['remove_url_seo'].$active_filter['remove_url']; ?>">
						<span><?php echo $active_filter['title']; ?></span>
					</a>
					<?php endif; ?>
				<?php endforeach; ?>
				<div class="cf-active-btn-section">
					<a href="<?php echo (!empty($search_fields['_basic']['reset_url'])) ? $search_fields['_basic']['reset_url'] : '?';?>" class="cf-active-item btn-cf-active-clear"><span><?php echo Arr::get($cmslabel, 'pa_clear_filtering'); ?></span></a>
				</div>
			</div>
		<?php endif; ?>

		<?php if(!empty($kind['level']) OR $q OR $kind_content == 'manufacturer' OR $kind_content == 'list'): ?>
			<div id="items_catalog_layout">
				<?php echo View::factory('catalog/index_layout', [
					'cms_page' => $cms_page,
					'kind' => $kind,
					'extra_kind' => $extra_kind,
					'q' => $q,
					'items' => $items,
					'items_per_page' => $items_per_page,
					'items_all' => $items_all,
					'items_layout_sufix' => $items_layout_sufix,
					'items_total' => $items_total,
					'pagination' => $pagination,
					'selected_sort' => $selected_sort,
				]); ?>
			</div>
		<?php endif; ?>
	</div>
</div>
<?php $this->endblock('content'); ?>