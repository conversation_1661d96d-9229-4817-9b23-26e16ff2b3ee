f<?php 
$mode = (isset($mode)) ? $mode : ''; 
$product_priorities = (isset($product_priorities)) ? $product_priorities : Kohana::config('app.catalog.product_priorities');
$i = (!empty($pagination)) ? (($pagination->current_page - 1) * $pagination->items_per_page) + 1 : 1;

// coupon
list($list_coupons, $item_coupon_ids) = Catalog::listitems_coupon(array_keys($items));
// odprodaja
$sale_list_items = Catalog::listitems('webcatalog_115719', array_keys($items));
$is_loyalty = (!empty($user->loyalty_code));
?>

<?php foreach ($items as $item): ?>
	<article class="clear cp-special">
        <span style="display: none;" data-product_manufacturer_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'manufacturer_title')); ?></span>
        <span style="display: none;" data-product_category_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'category_title')); ?></span>
        <span style="display: none;" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo trim(Arr::get($item, 'title')); ?></span>
        <span style="display: none;" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'code'); ?></span>
		<span style="display: none;" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Arr::get($item, 'price'); ?></span>

        <?php 
			$attributes_special = (!empty($item['attributes_special'])) ? $item['attributes_special'] : []; 

			$priority = Arr::get($product_priorities, (isset($item['priority_2']) ? $item['priority_2'] : ''));
			$badges_special = (!empty($item['badges'])) ? $item['badges'] : []; 
			$badges_special_1 = [];

			if ((!empty($priority['code']) AND $priority['code'] == 'action' AND $item['discount_percent_custom'] > 0) OR $item['discount_percent_custom'] > 0) {
				$badges_special_1[0]['category'] = '0';
			}

			$badges_special_2 = [];
			if (!empty($badges_special)) {
				foreach ($badges_special AS $badge_special_id => $badge_special_data) {
					if ((int)$badge_special_data['category'] <= 5) {
						if ((int)$badge_special_data['category'] == 5 AND !empty($priority['code']) AND $priority['code'] == 'new') {
							$badges_special_1[4]['category'] = '4';
						}
						
						if ($badge_special_data['category'] == 1) {
							// preorder
							if (!empty($item['status']) AND !empty($item['date_available']) AND $item['status'] == '5') {
								$badges_special_1[$badge_special_id] = $badge_special_data;
							}
						} else if ($badge_special_data['category'] == 2) {
							// coupon
							if ($list_coupons) {
								$coupon_ids = Arr::get($item_coupon_ids, $item['id']);
								if (!empty($coupon_ids)) {
									$coupon_ids = array_filter(explode(',', $coupon_ids));
									$item_coupons = Arr::extract($list_coupons, $coupon_ids);
									$item_coupon = reset($item_coupons);
									$badge_special_data['coupon_code'] = $item_coupon['coupon_code'];
									$badge_special_data['coupon_discount_percent'] = (int) $item_coupon['coupon_discount_percent'];
									$badge_special_data['coupon_active_to'] = $item_coupon['coupon_active_to'];
									
									$badges_special_1[$badge_special_id] = $badge_special_data;
								}
							}
						} else {
							$badges_special_1[$badge_special_id] = $badge_special_data;
						}
					} else {
						$badges_special_2[$badge_special_id] = $badge_special_data;
					}
				}

				if (!empty($priority['code']) AND $priority['code'] == 'new' AND !empty($badges_special_1[4])) {
					$badges_special_1[4]['category'] = '4';
				}

				if (count($badges_special_1) > 2) {
					$badges_special_1 = array_slice($badges_special_1, 0, 2, true);
				}
				if (count($badges_special_2) > 3) {
					$badges_special_2 = array_slice($badges_special_2, 0, 3, true);
				}
			} else {
				if (!empty($priority['code']) AND $priority['code'] == 'new') {
					$badges_special_1[4]['category'] = '4';
				}
			}
		?>

		<?php
			$user_warehouse = !empty($user->store_location->id) ? $user->store_location->id : 0;
			$qty_warehouse = [];
			foreach ($item['warehouses'] as $warehouse) {
				if($warehouse['id'] == $user_warehouse) {
					$qty_warehouse = $warehouse;
				}
			}
			$qty_warehouse_number = [];
		?>
		<?php if(!empty($qty_warehouse['available_qty'])): ?>
			<?php $qty_warehouse_number = $qty_warehouse['available_qty']; ?> 
		<?php else: ?>
			<?php $qty_warehouse_number = '0'; ?>
		<?php endif; ?>

		<?php if (!empty($compare_page)): ?>
			<div class="cp-compare-header">
				<form class="cp-compare-form" action="?special_view=compare" method="GET">
					<input class="cp-compare-input" name="compare_autocomplete-<?php echo $item['id']; ?>" placeholder="<?php echo Arr::get($cmslabel, 'compare_add_title', 'Upišite naziv proizvoda'); ?>" data-id_exclude="<?php echo ($items) ? implode(',', array_keys($items)) : ''; ?>" type="text" data-compare_content="<?php echo $item['compare_widget']['content']; ?>">
				</form>
			</div>
		<?php endif ?>

		<div class="cp-special-image-container">
			<figure class="cp-special-image lloader">
				<a href="<?php echo $item['url']; ?>">
					<img data-lazy="<?php echo Thumb::generate(Arr::get($item, 'main_image'), 240, 240, false, 'thumb', TRUE, '/media/images/no-image-240.jpg'); ?>" <?php echo Thumb::generate(Arr::get($item, 'main_image'), array('width' => 240, 'height' => 240, 'default_image' => '/media/images/no-image-240.jpg', 'placeholder' => '/media/images/no-image-240.jpg', 'srcset' => '480c 2x')); ?> <?php if($mode != 'landing'): ?> title="<?php echo Text::meta($item['main_image_title']); ?>" data-product_main_image="<?php echo $item['shopping_cart_code']; ?>"<?php endif; ?> alt="<?php if($mode == 'landing'): ?><?php echo $item['title']; ?><?php else: ?><?php echo Text::meta($item['main_image_description']); ?><?php endif; ?>" />
				</a>
			</figure>

			<?php if(!isset($badges) OR !empty($badges_special)): ?>
				<div class="cp-special-badges">
					<?php if (!empty($badges_special_1)): ?>
						<?php foreach($badges_special_1 as $badge_special): ?>
							<?php if ($badge_special['category'] == 4): ?>
								<div class="cp-badge cp-special-badge cp-badge-new new">
									<span><?php echo $priority['title']; ?></span>
									<div class="cp-badge-tooltip cp-badge-tooltip-new"><?php echo Arr::get($cmslabel, 'pa_new_badge_tooltip'); ?></div>
								</div>
							<?php endif; ?>

							<?php if ($badge_special['category'] == 0): ?>
								<div class="cp-badge cp-special-badge cp-badge-discount discount">
									<span>-<?php echo $item['discount_percent_custom']; ?> %</span>
									<div class="cp-badge-tooltip cp-badge-tooltip-discount"><?php echo Arr::get($cmslabel, 'pa_discount_badge_tooltip'); ?></div>
								</div>
							<?php endif; ?>

							<?php if ($badge_special['category'] == 3): ?>
								<div class="cp-badge cp-special-badge cp-badge-gift gift">
									<?php if(!empty($badge_special['label_title'])): ?>
										<span>-<?php echo $badge_special['label_title']; ?> %</span>
									<?php endif; ?>
									<div class="cp-badge-tooltip cp-badge-tooltip-gift"><?php echo Arr::get($cmslabel, 'pa_gift_badge_tooltip'); ?></div>
								</div>
							<?php endif; ?>
						<?php endforeach; ?>

						<?php foreach($badges_special_2 as $badge_special): ?>
							<div class="cp-badge cp-special-badge cp-badge-coupon coupon">
								<?php if(!empty($badge_special['label_title'])): ?>
									<span>-<?php echo $badge_special['label_title']; ?> %</span>
								<?php endif; ?>
								<div class="cp-badge-tooltip cp-badge-tooltip-coupon"><?php echo Arr::get($cmslabel, 'pa_coupon_badge_tooltip'); ?></div>
							</div>
							<?php break; ?>
						<?php endforeach; ?>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			
			<?php if (isset($item['compare_widget']) AND !empty($compare_page)): ?>
				<div class="cp-special-container">
					<a class="cp-special-compare-remove compare_set_<?php echo $item['compare_widget']['content']; ?> compare_active" href="javascript:cmscompare.set('<?php echo $item['compare_widget']['content']; ?>', 'remove_reload', '', '_tracking:index');"><span><?php echo Arr::get($cmslabel, 'compare_remove', 'Ukloni'); ?></span></a>
				</div>
			<?php endif; ?>
		</div>

		<div class="clear cp-special-cnt">
			<?php if(!empty($item['category_title'])): ?>
				<a class="cp-special-category" href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a>
			<?php endif; ?>
			<h2 class="cp-special-title">
				<a href="<?php echo $item['url']; ?>" data-product_title="<?php echo $item['shopping_cart_code']; ?>"><?php echo $item['title']; ?>	</a>
			</h2>
			<div class="cp-special-code" data-product_code="<?php echo $item['shopping_cart_code']; ?>"><strong><?php echo Arr::get($cmslabel, 'id'); ?>:</strong> <?php echo $item['code']; ?></div>
		</div>

		<?php if($item['price_custom'] > 0): ?>
			<div class="cp-special-price">
				<?php $installment_price = 0; ?>
				<?php if (!empty($item['installments_min_price'])) {
						$installment_price = $item['installments_min_price'];
						if (!empty($item['installments_loyalty_min_price']) AND $is_loyalty) {
							$installment_price = $item['installments_loyalty_min_price'];
						}
						$installment_price = Utils::currency_format($installment_price * $currency['exchange'], $currency['display']);
					}
					?>
				<?php if ($item['discount_percent_custom'] > 0 OR $item['price_custom'] < $item['basic_price_custom']): ?>
					<div class="cp-special-old-price line-through" data-product_basic_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['basic_price_custom'] * $currency['exchange'], $currency['display']); ?></div>
					<div class="cp-special-current-price cp-special-discount-price red" data-product_price="<?php echo $item['shopping_cart_code']; ?>">(-<?php echo $item['discount_percent_custom']; ?>%) <?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($item['installments_min_price'])) ? '<span class="cp-installments-price">'. str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'pa_installments_price_text')).'</span>' : ""; ?></div>
				<?php else: ?>
					<div class="cp-special-current-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><?php echo Utils::currency_format($item['price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($item['installments_min_price'])) ? '<span class="cp-installments-price">'. str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'pa_installments_price_text')).'</span>' : ""; ?></div>
				<?php endif; ?>
				<?php if (!empty($item['discount_expire'])): ?>
					<div class="cp-special-discount-expire"><?php echo str_replace('%d%', date('d.m.Y.', $item['discount_expire']), Arr::get($cmslabel, 'pa_discount_expire')) ?></div>
				<?php endif; ?>
				<?php if (!empty($item['loyalty_price_custom'])): ?>
					<div class="cp-special-current-price cp-special-loyalty-price cp-loyalty-price" data-product_price="<?php echo $item['shopping_cart_code']; ?>"><span><?php echo Utils::currency_format($item['loyalty_price_custom'] * $currency['exchange'], $currency['display']); ?><?php echo (!empty($item['installments_min_price'])) ? '<span class="cp-installments-price">'. str_replace("%PRICE%", $installment_price, Arr::get($cmslabel, 'pa_installments_price_text')).'</span>' : ""; ?></span></div>
				<?php endif; ?>
			</div>
		<?php endif; ?>

		<?php if (!empty($item['is_available']) AND empty($item['variation_total'])): ?>
			<?php
			$add_to_cart_label = 'add_to_shopping_cart';
			if (in_array($item['type'], ['advanced', 'configurable'])) {
				$add_to_cart_label = 'add_to_shopping_cart_configurable';
			} elseif ($item['status'] == '5') {
				$add_to_cart_label = 'add_to_shopping_cart_preorder';
			}
			?>
			<a class="btn btn-green cp-special-btn-addtocart" title="<?php echo Arr::get($cmslabel, $add_to_cart_label); ?>" href="<?php if (in_array($item['type'], ['advanced', 'configurable'])): ?><?php echo $item['url']; ?><?php else: ?>javascript:cmswebshop.shopping_cart.add('<?php echo $item['shopping_cart_code']; ?>', '_tracking:index', 'simple_loader', 'simple', 8)<?php endif; ?>">
				<span><?php echo Arr::get($cmslabel, $add_to_cart_label); ?></span>
			</a>
		<?php else: ?>
			<a class="btn btn-green cp-special-btn-addtocart cp-btn-details" title="<?php echo Arr::get($cmslabel, 'read_more'); ?>" href="<?php echo $item['url']; ?>">
				<span><?php echo Arr::get($cmslabel, 'read_more'); ?></span>
			</a>
		<?php endif; ?>

		<?php if (!empty($compare_page)): ?>
			<div class="c-compare-m-btns">
				<a href="javascript:void(0);" data-compare_mode="all" class="c-compare-btn c-compare-btn-all active"><?php echo Arr::get($cmslabel, 'compare_all_details'); ?><span></span></a>
				<a href="javascript:void(0);" data-compare_mode="diff" class="c-compare-btn c-compare-btn-diff"><?php echo Arr::get($cmslabel, 'compare_difference'); ?><span></span></a>
			</div>			
			<div class="cp-compare-attributes cp-attributes-compare<?php /*if(count($item) < 2): ?> cp-attributes-compare-single<?php endif; */?>">
				<table class="table-cp-attributes">
					<?php 
					$item_attributes = array_flip(array_keys($attributes));
					foreach ($item['attributes'] AS $attribute) {
						if (isset($item_attributes[$attribute['attribute_code']])) {
							$item_attributes[$attribute['attribute_code']] = [$attribute['attribute_title'], $attribute['title']];
						}
					}
					?>

					<?php $j = 1; ?>
					<?php foreach ($item_attributes AS $attribute_code => $attribute_data): ?>
						<?php if (is_array($attribute_data)): ?>
							<tr class="attr-row active attr-row<?php echo $j; ?>" data-row="<?php echo $j; ?>" data-compare_attribute="<?php echo $attribute_code; ?>">
								<td class="col-title"><span class="col-attribute-title"><?php echo $attribute_data[0]; ?></span><span class="col-attribute-value"><?php echo $attribute_data[1]; ?></span></td>
							</tr>
						<?php else: ?>
							<tr class="attr-row active attr-row<?php echo $j; ?> attr-row-empty" data-row="<?php echo $j; ?>" data-compare_attribute="<?php echo $attribute_code; ?>">
								<td class="col-title">&nbsp;</td>
							</tr>
						<?php endif; ?>
						<?php $j++; ?>
					<?php endforeach; ?>
				</table>
			</div>
		<?php endif ?>
	</article>
	<?php $i++; ?>
<?php endforeach; ?>