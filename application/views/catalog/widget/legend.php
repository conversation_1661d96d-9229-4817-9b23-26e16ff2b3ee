<?php $legend_items = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'pa_legend', 'limit' => 20]); ?>
<?php $legend_items_extra = Widget_Rotator::elements(['lang' => $info['lang'], 'category_code' => 'pa_legend_extra', 'limit' => 20]); ?>
<?php if (!empty($legend_items) OR !empty($legend_items_extra)): ?>
	<div class="c-legend">
		<div class="c-legend-label"><span><?php echo Arr::get($cmslabel, 'pa_legend'); ?></span></div>
		<div class="c-legend-tooltip">
			<div class="c-lt-header">
				<div class="c-lt-header-title"><?php echo Arr::get($cmslabel, 'pa_legend'); ?></div>
				<div class="c-lt-close"></div>
			</div>
			<?php if($legend_items): ?>
				<div class="c-lt-top">
					<?php foreach($legend_items as $legend_item): ?>
						<?php if(!empty($legend_item['element_color_4'])): ?>
							<style>
								.c-lt-icon-<?php echo $legend_item['id']; ?>{background: <?php echo $legend_item['element_color_4']; ?>;}
							</style>
						<?php endif; ?>
						<div class="c-lt-item">
							<?php if(!empty($legend_item['image'])): ?>
								<?php $fileExtension = pathinfo($legend_item['image'], PATHINFO_EXTENSION); ?>
								<div class="c-lt-icon c-lt-img">
									<?php if($fileExtension == 'svg'): ?>
										<img src="<?php echo Utils::file_url($legend_item['image']); ?>" alt="<?php echo $legend_item['title']; ?>">
									<?php else: ?>
										<img <?php echo Thumb::generate($legend_item['image'], array('width' => 13, 'height' => 13, 'html_tag' => TRUE, 'srcset' => '26x26c 2x')); ?> alt="<?php echo Text::meta($legend_item['title']); ?>" />
									<?php endif; ?>
								</div>
							<?php elseif(!empty($legend_item['element_color_4'])): ?>
								<div class="c-lt-icon c-lt-icon-<?php echo $legend_item['id']; ?>"></div>
							<?php endif; ?>
							<?php if(!empty($legend_item['title'])): ?>
								<span><?php echo $legend_item['title']; ?></span>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
			<?php if($legend_items_extra): ?>
				<div class="c-lt-bottom">
					<?php foreach($legend_items_extra as $legend_item_extra): ?>
						<?php if(!empty($legend_item_extra['element_color_4'])): ?>
							<style>
								.c-lte-item-qty-<?php echo($legend_item_extra['id']); ?>{background: <?php echo $legend_item_extra['element_color_4']; ?>;}
								.c-lte-item-col2-<?php echo($legend_item_extra['id']); ?> h4{color: <?php echo $legend_item_extra['element_color_4']; ?>;}
							</style>
						<?php endif; ?>
						<div class="c-lte-item">
							<div class="c-lte-item-col1">
								<div class="c-lte-img"><img src="/media/images/no-image-60.jpg" alt=""></div>
								<?php if($legend_item_extra['element_content_small']): ?>
									<div class="c-lte-item-qty c-lte-item-qty-<?php echo($legend_item_extra['id']); ?>"><?php echo $legend_item_extra['element_content_small']; ?></div>
								<?php endif; ?>
							</div>
							<?php if($legend_item_extra['content']): ?>
								<div class="c-lte-item-col2 c-lte-item-col2-<?php echo($legend_item_extra['id']); ?>">
									<?php echo $legend_item_extra['content']; ?>
								</div>
							<?php endif; ?>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>
	</div>
<?php endif; ?>