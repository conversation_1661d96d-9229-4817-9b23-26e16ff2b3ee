<a class="cd-compare-btn compare compare_set_<?php echo $content; ?><?php if ($active): ?> compare_active<?php endif; ?>" href="javascript:cmscompare.set('<?php echo $content; ?>', '<?php if ($active): ?>remove<?php else: ?>+<?php endif; ?>', '', '_tracking:index');" data-compare_operation="+|remove" <?php if (empty($mode)): ?> data-compare_change_tooltip="1" title="<?php echo ($active) ? Arr::get($cmslabel, 'remove_compare_product') : Arr::get($cmslabel, 'add_compare_product'); ?>" <?php endif; ?>>
	<span class="cd-compare-icon cd-compare-add"><?php echo Arr::get($cmslabel, 'pa_compare_add'); ?></span>
	<span class="cd-compare-icon cd-compare-remove"><?php echo Arr::get($cmslabel, 'pa_compare_remove'); ?></span>
	<span class="cd-compare-info compare_message compare_message_<?php echo $content; ?>" style="display:none;"></span>
</a>
