<?php $categories_by_position = Widget_Catalog::categories(array('lang' => $info['lang'], 'level_range' => '1.3', 'mode' => 'widget', 'hierarhy_by_position' => true)); ?>
<ul class="categories">
	<?php foreach ($categories_by_position as $category): ?>
		<?php $subcategories = (!empty($category['children'])) ? $category['children'] : [] ?>
		<li class="category<?php if($subcategories): ?> has-children<?php endif; ?> <?php echo $category['code']; ?>">
			<a href="<?php echo $category['url']; ?>">
				<?php if($category['main_image']): ?>
					<span class="category-img">
						<?php $fileExtension = pathinfo($category['main_image'], PATHINFO_EXTENSION); ?>
						<?php if($fileExtension == 'svg'): ?>
							<img src="<?php echo Utils::file_url($category['main_image']); ?>" alt="<?php echo $category['title']; ?>">
						<?php else: ?>
							<img <?php echo Thumb::generate($category['main_image'], array('width' => 83, 'height' => 50, 'html_tag' => TRUE, 'srcset' => '170x100r 2x')); ?> alt="<?php echo Text::meta($category['title']); ?>" />
						<?php endif; ?>
					</span>
				<?php endif; ?>
				<span class="category-title"><?php echo $category['title']; ?><span class="toggle-icon"></span></span>
			</a>
			<?php if($subcategories): ?>
				<ul class="subcategories">
					<?php foreach ($subcategories as $subcategory): ?>
						<?php $subsubcategories = (!empty($subcategory['children'])) ? $subcategory['children'] : [] ?>
						<li class="subcategory<?php if($subsubcategories): ?> has-children<?php endif; ?>">
							<a href="<?php echo $subcategory['url']; ?>">
								<?php if($subcategory['main_image']): ?>
									<div class="subcategory-icon"><img src="<?php echo Utils::file_url($subcategory['main_image']); ?>" alt="<?php echo $subcategory['title']; ?>"/></div>
								<?php endif; ?>
								<span class="subcategory-title"><?php echo $subcategory['title']; ?><span class="toggle-icon"></span></span>
							</a>
							<?php if($subsubcategories): ?>
								<ul class="subsubcategories">
									<?php foreach ($subsubcategories as $subsubcategory): ?> 
										<li class="subsubcategory"><a href="<?php echo $subsubcategory['url']; ?>"><?php echo $subsubcategory['title']; ?><span class="counter"><?php echo $subsubcategory['total']; ?></span></a></li>
									<?php endforeach; ?>
									<?php if(!empty($subcategory['total_discount'])): ?>
										<li class="subsubcategory"><a class="red" href="<?php echo $subcategory['url']; ?>?discount:1"><?php echo Arr::get($cmslabel, 'sale'); ?><span class="counter"><?php echo $subsubcategory['total_discount']; ?></span></a></li>
									<?php endif; ?>
									<?php if(!empty($subcategory['total_new'])): ?>
										<li class="subsubcategory"><a class="blue" href="<?php echo $subcategory['url']; ?>?new:1"><?php echo Arr::get($cmslabel, 'new'); ?><span class="counter"><?php echo $subsubcategory['total_new']; ?></span></a></li>
									<?php endif; ?>
									<li class="subsubcategory"><a href="<?php echo $subcategory['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all', 'Pokaži vse'); ?></a></li>
								</ul>
							<?php endif; ?>
						</li>
					<?php endforeach; ?>
					<?php if($info['user_device'] == 't' OR $info['user_device'] == 'm'): ?>
						<li class="categories-all"><a href="<?php echo $category['url']; ?>"><?php echo Arr::get($cmslabel, 'show_all', 'Pokaži vse'); ?></a></li>
					<?php endif; ?>
				</ul>
			<?php endif; ?>
		</li>
	<?php endforeach; ?>
</ul>