<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta($item['seo_title']); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/detail', ['item' => $item]); ?><?php $this->endblock('seo'); ?>

<?php $this->block('breadcrumb'); ?>
	<?php $breadcrumb = Widget_Cms::breadcrumb($info['lang'], $info['cmspage_url'], TRUE, $item['breadcrumbs']); ?>
	<?php echo View::factory('cms/widget/breadcrumb', ['breadcrumb' => $breadcrumb]); ?>
<?php $this->endblock('breadcrumb'); ?>

<?php $this->block('content'); ?>
	<h1 class="pd-title"><?php echo $item['seo_h1'] ?></h1>

	<a class="pd-category" href="<?php echo $item['category_url']; ?>"><?php echo $item['category_title']; ?></a>
		
	<!-- Post content and related documents -->
	<div class="pd-desc cms-content">
		<?php echo $item['short_description']; ?>
		
		<?php echo $item['content']; ?>
		<?php $documents = Utils::get_files('publish', $item['id'], '-image', $info['lang']); ?>
		<?php if ($documents): ?>
			<ul class="pd-documents">
				<?php foreach ($documents as $file): ?>
					<li><a href="<?php echo $file['url']; ?>" title="<?php echo Text::meta($file['description']); ?>"><?php echo Text::meta($file['title']); ?></a></li>
				<?php endforeach; ?>
			</ul>
		<?php endif; ?>
	</div>

<?php $this->endblock('content'); ?>