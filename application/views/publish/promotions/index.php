<?php $this->extend('default'); ?>

<?php $this->block('title'); ?><?php echo Text::meta(((!empty($kind['seo_title_full'])) ? $kind['seo_title_full'] : Arr::get($cms_page, 'seo_title')).((!empty($pagination->current_page) AND $pagination->current_page > 1) ? sprintf(Arr::get($cmslabel, 'current_page', ' - stranica %s od %s'), $pagination->current_page, $pagination->total_pages) : '')); ?><?php $this->endblock('title'); ?>
<?php $this->block('seo'); ?><?php echo View::factory('cms/widgetseo/index', ['kind' => $kind, 'pagination' => $pagination]); ?><?php $this->endblock('seo'); ?>
<?php $this->block('page_class'); ?> page-publish<?php $this->endblock('page_class'); ?>

<?php $this->block('content'); ?>

<?php
$curr_ctg_slug = Arr::get($_GET, 'category');
$categories_filter = explode(',', Arr::get($_GET, 'category_id'));
?>
	<h1 class="p-title"><?php echo $kind['seo_h1']; ?> <?php if(!empty($items)): ?><span class="p-counter">(<?php echo count($items); ?>)</span><?php endif; ?></h1>

    <?php if (!$curr_ctg_slug): ?>
        <?php $child_categories = Widget_Publish::categories(['lang' => $info['lang'], 'start_position' => $kind['position_h'], 'only_available' => true]); ?>
        <?php if (sizeof($child_categories)): ?>
            <ul class="p-categories">
                <?php foreach ($child_categories as $child_category): ?>
                    <?php if($child_category['total'] > 0): ?>
                        <?php
                        $checked = in_array($child_category['id'], $categories_filter) ? 'checked' : '';
                        ?>
                        <div class="p-categories-item">
                            <input type="checkbox" data-js="checkbox-as-link-filter" data-filter_group="category_id" <?php echo $checked; ?> id="p-search-<?php echo $child_category['code']; ?>" name="<?php echo $child_category['code']; ?>" value="<?php echo $child_category['id']; ?>">
                            <label for="p-search-<?php echo $child_category['code']; ?>"><?php echo $child_category['title']; ?><span class="counter"><?php echo $child_category['total']; ?></span></label>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
                <a class="p-categories-item p-categories-item-remove" href="#" data-js="clear-checkbox-group-and-redirect" data-group="category_id"><?php echo Arr::get($cmslabel, 'pa_clear_promotions_filtering'); ?></a>
            </ul>
        <?php endif; ?>
    <?php endif; ?>
	<div id="items_<?php echo $kind['code']; ?>_layout">
		<?php echo View::factory('publish/promotions/index_layout', [
			'kind' => $kind,
			'q' => '',
			'items' => $items,
			'items_per_page' => $items_per_page,
			'items_all' => $items_all,
			'items_total' => $items_total,
			'child_categories' => $child_categories,
			'child_categories_published' => $child_categories_published,
			'pagination' => $pagination,
			]); ?>
	</div>
<?php $this->endblock('content'); ?>