<?php if (sizeof($items)): ?>
	<?php
	$curr_ctg_slug = Arr::get($_GET, 'category');

	$items_this_ctg = $items;
	$items_other_ctg = [];
	if ($curr_ctg_slug){
		unset($items_this_ctg);
		$items_this_ctg = [];
		foreach ($items as $key => $val){
			$item_ctg_slug = Arr::get($val, 'category_slug');

			if ($item_ctg_slug == $curr_ctg_slug){
				$items_this_ctg[$key] = $val;
			} else {
				$items_other_ctg[$key] = $val;
			}
		}
	}
	?>
	<div class="p-items" id="items_<?php echo $kind['code']; ?>" data-infinitescroll="items_<?php echo $kind['code']; ?>" data-infinitescroll_next_page="<?php echo $pagination->next_page; ?>" data-infinitescroll_total_pages="<?php echo $pagination->total_pages; ?>" data-infinitescroll_auto_trigger="1">
		<?php echo View::factory('publish/promotions/index_entry', ['items' => $items_this_ctg]); ?>
		<?php if (sizeof($items_other_ctg)): ?>
			<div class="cd-subtitle">Akcije iz drugih kategorija</div>
			<?php echo View::factory('publish/promotions/index_entry', ['items' => $items_other_ctg]); ?>
		<?php endif; ?>
	</div>

	<?php echo $pagination; ?>
	<?php if ($pagination): ?>
		<div class="load-more-container p-load-more-container">
			<a href="javascript:void(0);" class="btn btn-white btn-medium load-more btn-load-more" style="display: none"><?php echo str_replace(array('%PER_PAGE%', '%TOTAL%', '%NEXT_PAGE_FIRST_ITEM%', '%NEXT_PAGE_LAST_ITEM%'), array($pagination->items_per_page, $pagination->total_items, $pagination->next_page_first_item, $pagination->next_page_last_item), Arr::get($cmslabel, 'load_more_publish')); ?></a>
		</div>
	<?php endif; ?>
<?php else: ?>
	<?php echo Arr::get($cmslabel, 'pa_no_promotions'); ?>
<?php endif; ?>