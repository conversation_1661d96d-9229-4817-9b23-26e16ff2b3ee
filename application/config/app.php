<?php defined('SYSPATH') OR die('No direct access allowed.');

$host = (isset($_SERVER['HTTP_HOST'])) ? $_SERVER['HTTP_HOST'] : str_replace('/var/www/pa.bigbang.si/', 'pa.bigbang.si', dirname(__FILE__));
$path = '/var/www/vhosts/bigbang.si/';
if (in_array($host, ['pa.bigbang.si'])) {
    $path = '/var/www/bigbang.si/';
} else if (in_array($host, ['pa.bigbangmarkerdev.info'])) {
    $path = '/var/www/vhosts/bigbangmarkerdev.info/httpdocs/';
} else if (in_array($host, ['pabigbangprod.marker'])) {
    $path = '/var/www/vhosts/bigbang.si_prod/';
} else if (in_array($host, ['pabigbang.marker', 'pasanctahr.marker'])) {
    $path = '/var/www/vhosts/bigbang.si/';
}

$config = require_once("{$path}application/config/app.php");

$new_config = [
    'sitename' => 'pa.bigbang.si',
    'site_version' => 9911,
    'site_id' => 2,
    'catalog' => [
        'offer_location' => null, // glavna poslovnica
        'category_use_landingpage' => false, // koristi landing stranice kategroija (bez ispisa proizvoda)
        'use_pa_warehouses_ids' => true,
        'use_warehouses_negative_qty' => true,
        'relatedrule_use_manufacturer_check' => false,
        'product_priorities' => [
            1 => [
                'code' => 'recommended_3',
                'title' => 'Preporučeno MP',
            ],
            2 => [
                'code' => 'new',
                'title' => 'Novo',
                'older' => 30 * 86400, // 3 dana
            ],
            3 => [
                'code' => 'action',
                'title' => 'Akcija',
            ],
        ],
        'product_status' => [
            'available_based_on_status' => [1, 2, 4, 5, 7],
        ],
        'per_page' => 30,
        'service_sort' => 'position',
        'list_usage_purpose' => 'stores', // koja namjena se koristi
        'index_products_other_image_count' => 0,
        'badge_usage_purpose' => 'stores', // koji badgevi se koriste
        'specific_shipping_pairs' => [
            'p' => 'osobno_preuzimanje',
            'ps' => 'osobno_preuzimanje_poslovnica',
            'pw' => 'osobno_preuzimanje_skladiste',
            's' => 'dostavna_sluzba',
            'e' => 'dostavna_sluzba_express',
            'bb' => 'bigbang_dostavna',
            'bb_fast' => 'hitra_dostava',
            'bb_xxl' => 'bigbang_dostavna_xl',
        ],
        'use_wishlists' => false,
        'use_categoryrelatedproducts' => false, // izdvajanje proizvoda na vrhu kategorije
        'use_searchformcaches' => false,
        'category_without_base_url' => false,
    ],
    'publish' => [
        'publish_use_datetime_expire' => true,
        'sort' => 'expiry',
        'sort_first_specific_category' => true,
    ],
    'search' => [
        'api_modules' => [
            'catalog.products' => 'api/bigbangsipim/search/?q=%QUERY%',
        ],
        'api_modules_timeouts' => [
            'catalog.products' => 10,
        ],
    ],
    'webshop' => [
        'multiple_cart' => true, // istovremeno aktivno više košarica
        'multiple_cart_default_cart_api_payment' => 'PayInStore', // naziv defaultne cart api metode
        'default_restore_cart_prohibition_seconds' => 30, // ako nema promjene u košarici nakon restore, onemogućeno je završavanje narudžbe prije isteka tih sekundi ili interkacije prodavača
        'default_restore_cart_prohibition_order_types' => ['order'], // vrste narudzbi na kojima vrijedi default_restore_cart_prohibition_seconds
        //'ignore_cart_api_call_upon_adding_new_product' => true, // ignorira poziv cart APIja kod poziva hapi carta nakon dodavanja novog proizvoda
        'cache_cart' => true, // kesira proizvode u kosarici (na temelju tokena korisnika)
        'cache_multiple_cart' => true, // kesira listu kosarica kod prodavaca
        'cache_multiple_cart_lifetime' => 300, // seconds
        'fast_multiple_cart_change' => true, // brza promjena kosarice
        'merge_shopping_cart' => false,
        'cart_use_custom_errors' => true,
        'shoppingcart_price_change_as_error' => false,
        'check_price_api_if_zero_products' => true,
        'forbid_check_price_api_when_order_created' => [
            'active' => false,
        ],
        'set_payment_if_empty' => 24, // namješta payment na određenu vrijednost ako ništa ne postoji u customer_data['payment']
        'check_qty' => false, // provjera kolicine proizvoda
        'forbid_order_if_serial_number_empty' => true, // onemogucuje zavrsetak narudzbe ako je polje serijskog broja prazno
        'forbid_order_if_cart_hash_empty' => true, // oneomgucuje zavrsetak narudzbe ako je polje kartice prazno
        'checkout_address_must_contain_number' => false,
        'order_send_api' => 'api/bigbangsiorder/send_webshoporder/?pa_order_id=%PA_ORDER_ID%&try=%TRY%&response=quick',
        'order_send_api_timeout' => [
            1 => 30, // first call - 10s // default 10 se
            2 => 60, // second call - 60s
        ],
        'order_send_api_timeout_default' => 30, // sync slanje narudzbe - timeout
        'order_timelimit_per_email' => '1', // kreiranje nove narudžbe s iste e-mail adrese ima vremensko ograničenje (vrijeme u sekundama)
        'use_scan_item_qr' => true,
        'preorder_package_mirrors_main_package_delivery' => false,
        'use_shipping_sales' => true, // shipping sales token
        'check_price_api_use_b2b_channel' => true,
        'group_delivery_orders_with_coupons_shipping_code' => 'bigbang_dostavna',
        'immediate_specific_delivery_types' => ['PickupStore', 'PickupWarehouse', 'PickupTransit'],
        'group_delivery_orders_coupons_product_code' => '324539',
        'second_plus_order_delivery_time_optimized' => true,
        'use_reset_delivery_indicator' => true, // uključuje indikator "cart.indicators.reset_delivery_enabled" na hapi cartu
        'set_coupon_on_product' => [],
        'shipping_pickup_codes' => ['osobno_preuzimanje', 'osobno_preuzimanje_poslovnica', 'osobno_preuzimanje_skladiste'],
        'store_pickup_codes' => ['osobno_preuzimanje_poslovnica', 'osobno_preuzimanje_skladiste'],
        'shipping_type_pairs' => [
            'osobno_preuzimanje' => 'PickUpInStore',
            'osobno_preuzimanje_poslovnica' => 'PickUpInStoreOnTheFloor',
            'osobno_preuzimanje_skladiste' => 'PickUpInStoreWarehouse',
            'dostavna_sluzba' => 'StandardDelivery',
            'dostavna_sluzba_express' => 'ExpressDelivery',
            'bigbang_dostavna' => 'BBDelivery',
            'bigbang_dostavna_xl' => 'BBDeliveryXL',
            'hitra_dostava' => 'BBExpressDelivery',
            'digitalna_odprema' => 'DigitalDelivery',
        ],
        'return_sorted_cart' => true,
        'parcels_can_use_different_receivers' => true, // svaka otprema moze imati drugacijeg prijemnika - spremanje u polja apptoken 'shopping_shipping_items'
        'parcel_receiver_fields' => [
            'b_email', 'b_first_name', 'b_last_name', 'b_phone', 'b_country', 'b_address', 'b_location', 'b_zipcode', 'b_city',
            'recipient_id', 'recipient_erp_id', 'specific_user_selected', 'specific_address_selected', 'cart_reservation_for',
            'api_code', 'api_code2', 'loyalty_code',
            'company_address', 'b_company_address', 'b_company_location', 'b_company_zipcode', 'b_company_city',
        ],
        'use_parcel_receiver_fields_on_store_pickups' => true,
        'default_specific_order_type' => 'order',
        'forbid_cart_api_call_if_no_cart_selected' => true, // PA: blokira poziv cart APIja ako  nema selektirane košarice
        'specific_order_type_additional_data_provider' => 'Controller_ApiAdapter_BigbangsiOrder',
        'specific_order_types' => [
            'Order' => 'order',
            'ReservationOrder' => 'reservation_order',
            'ConfirmedOrder' => 'confirmed_order',
            'RetailOffer' => 'retail_offer',
            'B2bOffer' => 'b2b_offer',
        ],
        'set_missing_cart_order_type_on_order_type_change' => ['order'],
        'confirm_avans_item_pop_up_cart_order_types' => ['order'],
        'specific_order_type_valid_to_time' => '23:59:00',
        'specific_order_types_options' => [
            'order' => [
                'use_payment_due_days' => true,
                'custom_shopping_cart_code' => true,
                'b2b_channel_equal_to_null' => true,
                'min_expiration_days' => 2,
                'max_expiration_days' => 10,
                'valid_to' => [
                    'days' => [
                        10 => [2410], // za poslovnicu 2410, 10 radna dana
                        2 => null, // za sve ostale poslovnice, 2 radna dana
                    ],
                ],
                'update_to' => [
                    'hours' => 1,
                    'equals_valid_to' => false,
                ],
            ],
            'reservation_order' => [
                'use_payment_due_days' => true,
                'has_customer_form_id' => true,
                'min_expiration_days' => 2,
                'max_expiration_days' => 14,
                'valid_to' => [
                    'days' => 3,
                ],
                'update_to' => [
                    'hours' => 1,
                    'equals_valid_to' => false,
                ],
            ],
            'confirmed_order' => [
                'min_expiration_days' => 2,
                'max_expiration_days' => 14,
            ],
            'retail_offer' => [
                'use_payment_due_days' => true,
                'min_expiration_days' => 0,
                'max_expiration_days' => 30,
                'valid_to' => [
                    'days' => 14,
                ],
                'update_to' => [
                    'hours' => null,
                    'equals_valid_to' => true,
                ],
            ],
            'b2b_offer' => [
                'use_payment_due_days' => true,
                'has_customer_form_id' => true,
                'min_expiration_days' => 0,
                'max_expiration_days' => 30,
                'valid_to' => [
                    'days' => 14,
                ],
                'update_to' => [
                    'hours' => null,
                    'equals_valid_to' => true,
                ],
            ],
        ],
        'b2b_seller_required_cart_order_types' => ['b2b_offer'],
        'b2b_seller_required_order_types' => ['b2b_offer', 'confirmed_order'],
        'specific_order_types_options_properties' => [
            'additional_recipient_email',
            'customer_order_form_id',
            'display_recipient_on_visualization',
            'customer_order_form_date',
            'specific_payment_due_date_days',
            'reservation_date',
            'b2b_channel',
        ],
        'order_use_another_country' => false,
        'quick_cart_default_shipping_code' => 'osobno_preuzimanje',
        'cookie_expire' => 'today',
        'price_api_send_store_id' => true,
        'pa_default_shipping_code' => 'osobno_preuzimanje',
        'use_selected_payment_method_required_warning' => true,
        'pa_default_delivery_id' => '14',
        'pa_forbid_shipping_delivery_date_in_past' => true,
        'use_warehouse_pickup' => true,
        'shipping_on_product_level_fields' => [
            'shipping_first_name', 'shipping_last_name', 'shipping_address', 'shipping_zipcode', 'shipping_city',
            'shipping_phone', 'shipping_message',
        ],
        'orderitem_extra_fields' => [
            'shipping_id', 'pickup_location_id', 'shipping_date', 'shipping_time',
            'shipping_first_name', 'shipping_last_name', 'shipping_address', 'shipping_zipcode', 'shipping_city',
            'shipping_phone', 'shipping_message', 'date_available', 'shipping_date_default', 'pickup_status',
            'ean_code',
        ],
        'api_customer' => [
            'quick_response_fields' => ['payment'],
            'quick_response_fields_no_reload' => ['payment'],
        ],
        'require_mobile_phone_shipping_codes' => ['bigbang_dostavna', 'dostavna_sluzba'],
        'forbid_finishing_order_if_seller_number_empty' => true,
        'review_order_on_payment_step' => true,
        'shipping_visible_in_assistant_use' => true,
        'check_price_api_allow_methods' => [
            'webshop.shopping_cart',
            'webshop.payment',
            'webshop.review_order',
            'webshop.create_order',
            'api_webshop.index',
            'api_webshop.coupon',
            'api_webshop.customer_payment,cc_discount',
            'api_webshop.customer_shipping',
            'api_webshop.change_shipping_on_product',
            'api_webshop.set_cart_customer_data',
            'api.webshop.prices_cart',
            'api.webshop.remove_prices_cart',
        ],

        'remember_fields' => ['email', 'first_name', 'last_name', 'oib', 'address', 'phone', 'city', 'zipcode', 'country', 'cart_reservation_for'],
        /*
        'orderitem_extra_fields' => [
            'shipping',
            'shipping_pickup_location',
            'shipping_location',
            'shipping_date',
            'shipping_time',
            'shipping_first_name',
            'shipping_last_name',
            'shipping_address',
            'shipping_zipcode',
            'shipping_city',
            'shipping_phone',
            'shipping_message',
        ],*/
        'payments_config' => [
            'default' => 'pouzece_poslovnica',
            //'available' => ['pouzece_poslovnica'],
            'cart_order_type' => [
                'order' => 'pouzece_poslovnica',
                'reservation_order' => 'opca_uplatnica',
                'retail_offer' => 'opca_uplatnica',
                'b2b_offer' => 'opca_uplatnica',
            ],
        ],
        'customer_data_guest' => [
            // kupac ne daje svoje osobne podate, na narudzbi spremamo  NEZNANI KUPEC V MP (customer_id 2518793,  recipient_id : null).
            'first_name' => '000',
            'last_name' => '000',
            'address' => '000',
            'zipcode' => '0000',
            'city' => '000',
            //'country' => 'si',
            'phone' => '000',
            'email' => '<EMAIL>',
            'api_code' => '',
            'api_code2' => '',
            'loyalty_code' => '',
        ],
        'order_use_disable_send_mail_checked' => true,
        'thankyou_redirect_to_cart' => true,
        'cart_title' => [
            'si' => [
                'default' => 'Prenos z bigbang.si (%TIME%)',
                'customer' => '%CUSTOMER%, prenos z bigbang.si (%TIME%)',
            ],
        ],
        'cart_multiple_shipping_calculate' => false,
        'use_tracking_step' => false,
        'check_price_api_alert' => true,
        'popup_use_service' => false,
        //'shipping_delivery_days' => 2, // razlika MP vs PM
        'shipping_delivery_force_max_day' => 365,
        'coupon' => [
            'lenght_skip_validation' => 1, // duzina kupona za koju se ne provjerava baza
        ],
        'can_change_product_title' => ['343603'], // preimenovanje naziva proizvoda u košarici za unesene šifre
        'use_shipping_salesman_price' => true, // proizvoljne cijene prodavaca za dostavu
        'salesman_price_use_price_definition' => true,
        'salesman_price_allow_individual_discounts' => true, // svaki razlog unutar "salesman_price_reason_list" može imati svoj maksimalni popust
        'salesman_discount_max_categories' => ['service', 'shipping'],
        'salesman_price_required_reason' => ['default'],
        'salesman_price_universal_product_reason_id' => '099',
    ],
    'location' => [
        'locations_filterby_visible_pa' => true,
    ],
    'auth' => [
        //'login_api' => '', // only for local
        //'logout_api' => '', // only for local
        'login_tracking_log' => true,
        'user_change_store' => true, // biranje trgovine i odjela
        'user_change_store_only_if_empty' => true, // odabir trgovine nakon logiranja ide samo ako prodavač još nema odabranu trgovinu
        'user_warehouse_1_field_name' => 'store_location_id', // column name in auth_users table
        'user_warehouse_2_field_ignore' => true, // if true $user_warehouse_2 remains null
        'ignore_user_use_warehouse_priority' => false,
        'user_warehouse_qty_mode' => 2, // 1 - prikazuje samo kolicine na odabranom skladistu, 2 - gledaju se ukupne kolicine
        'reset_token_on_login' => true,
        'reset_token_on_change_store' => true,
        'use_token_hash' => true, // customer_data sadrzi token hash (oznacava da token pripada prodajnom asistentu)
    ],
    'api' => [
        'hapi_block_index_cart_url' => '/hapi/v1/webshop/order/',
    ],
    'utils' => [
        'webp_support' => true,
    ],
    'seo' => [
        'html_pagination_links' => false,
    ],
    'min' => [
        'css_gdefault' => 'fancybox,standard',
        'js_gdefault' => 'jquery,fancybox,lazyload,cmsgdpr,jqueryuidatepicker,cmscompare,cmswebshop,cmsautocomplete,jqueryuislider,cmscoupon,cmsutilsrangeslider,/media/jquery.ui.touch-punch.min.js,ssm,cmssiteforms,cmscatalog,elevatezoom,qrscanner,showpassword,cmsfeedback,cmsfilter,floatinglabels,slick,cmsstandard,cmsinfinitescroll,cmsgtm,standard',
        'js_gmodernizr' => 'modernizr',
        'js_gallery' => 'jquery,touchnswipe,slick',
        'js_gjquery' => 'jquery',
    ],
];

$config = Arr::merge($config, $new_config);
$config['context'] = ['auth.request_login', 'location.set_department', 'catalog.redirect',
    'cms.redirect', 'cms.labels', 'webshop.check_multicart', 'catalog.compare', 'loyalty.user', 'location.locationpoints_store',
    'webshop.shoppingcart_info', 'catalog.currency'];
$config['catalog']['product_status']['modes_limit'] = [];

return $config;
