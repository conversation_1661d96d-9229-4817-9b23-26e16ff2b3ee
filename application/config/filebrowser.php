<?php defined('SYSPATH') OR die('No direct access allowed.');

$host = (isset($_SERVER['HTTP_HOST'])) ? $_SERVER['HTTP_HOST'] : str_replace('/var/www/pa.bigbang.si/', 'pa.bigbang.si', dirname(__FILE__));
$path = '/var/www/vhosts/bigbang.si/';
if (in_array($host, ['pa.bigbang.si'])) {
    $path = '/var/www/bigbang.si/';
} else if (in_array($host, ['pa.bigbangmarkerdev.info'])) {
    $path = '/var/www/vhosts/bigbangmarkerdev.info/httpdocs/';
} else if (in_array($host, ['pabigbang.marker', 'pasanctahr.marker'])) {
    $path = '/var/www/vhosts/bigbang.si/';
}

$config = require_once("{$path}application/config/filebrowser.php");

return $config;
