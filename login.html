<html>
	<head></head>
	<body>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.27.2/axios.min.js" integrity="sha512-odNmoc1XJy5x1TMVMdC7EMs3IVdItLPlCeL5vSUPN2llYKMJ2eByTTAIiiuqLg+GdNr9hF6z81p27DArRFKT7A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
		<script src="https://pa.bigbangmarkerdev.info/shared/jquery/1.8.3.min.js?version=56f6e018091c113cb9fa6c67a3dc7ca6"></script>
		<script>
			fetch(`https://pa.bigbangmarkerdev.info/hapi/v1/auth/login/`, {
				method: 'POST',
				headers: {
					'Authorization':
						'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************.gFUzm-grK58MNbT4_PQdvGKH2jrdMHxtAUSnwMBCyto',
				},
				body: JSON.stringify({'email': '<EMAIL>', 'password': 'zL7B1ef6'}),
			})
				.then(response => {
					return response.json();
				})
				.then(res => {
					console.log('fetch');
					console.log(res);
				});

			axios.post('https://pa.bigbangmarkerdev.info/hapi/v1/auth/login/`', {'email': '<EMAIL>', 'password': 'zL7B1ef6'}).then(function (response) {
				console.log('axios');
				console.log(response);
				// do whatever you want if console is [object object] then stringify the response
			});

			$(function () {
				$.ajax({
					type: 'POST',
					url: 'https://pa.bigbangmarkerdev.info/hapi/v1/auth/login/',
					dataType: 'json',
					headers: {
						'Authorization':
							'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************.d6zBKuSPpsx3wmd3AfFfhRQZ1-51rHUjT2fqA_FaEf8',
					},
					data: '{"email": "<EMAIL>", "password": "zL7B1ef6"}',
					success: function (res) {
						console.log('jquery');
						console.log(res);
					},
				});
			});
		</script>
	</body>
</html>
