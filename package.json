{"name": "checkout", "version": "0.0.0", "scripts": {"dev": "vite --port 3000", "builddev": "vite build --base=/media/ --mode=development", "build": "vite build --base=/media/", "preview": "vite preview --port 5050"}, "dependencies": {"@sentry/tracing": "^7.114.0", "@sentry/vue": "^8.39.0", "@splidejs/vue-splide": "^0.6.12", "@vuepic/vue-datepicker": "^9.0.3", "@vueuse/core": "^11.1.0", "pinia": "^2.2.6", "swiper": "^11.1.15", "vee-validate": "^4.14.7", "vue": "^3.5.13", "vue-barcode-reader": "^1.0.3", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.0", "less": "^4.2.0", "less-loader": "^12.2.0", "vite": "^5.4.11"}}