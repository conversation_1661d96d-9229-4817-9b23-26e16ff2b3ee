<?php
$servername = "185.103.219.120";
$username = "bigbang-app";
$password = "HNTYj87%*nm91@hnYN8sajMANDDkN";
$dbname = "bigbangsi_web";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);
// Check connection
if ($conn->connect_error) {
    header('HTTP/1.1 500 Internal Server Error');
} else {
	$sql = "SELECT id FROM app_sites";
	$result = $conn->query($sql);
	if ($result->num_rows > 0) {
	    // output data of each row
	    while($row = $result->fetch_assoc()) {
	        echo "test ok - id " . $row["id"];
	    }
	} else {
	    header('HTTP/1.1 500 Internal Server Error');
	}
	$conn->close();
}
?>
