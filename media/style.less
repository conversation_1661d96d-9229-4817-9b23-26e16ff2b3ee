/*------- normalize -------*/
*{margin: 0; padding: 0; border: 0; outline: none; -webkit-tap-highlight-color: transparent; box-sizing: border-box; -webkit-font-smoothing: antialiased;}
img{max-width: 100%; height: auto;}
html{font-family: sans-serif; /* 1 */ -ms-text-size-adjust: 100%; /* 2 */ -webkit-text-size-adjust: 100%; /* 2 */}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary{display: block;}
audio, canvas, progress, video{display: inline-block; /* 1 */}
audio:not([controls]){display: none; height: 0;}
progress{vertical-align: baseline;}
[hidden], template{display: none;}
a{background-color: transparent; -webkit-text-decoration-skip: objects; /* 2 */}
a:active, a:hover{outline: 0; -webkit-tap-highlight-color: transparent;}
abbr[title]{border-bottom: 1px dotted;}
b, strong{font-weight: bold;}
dfn{font-style: italic;}
mark{background: #ff0; color: #000;}
small{font-size: 80%;}
sub, sup{font-size: 75%; line-height: 0; position: relative; vertical-align: baseline;}
sup{top: -0.5em;}
sub{bottom: -0.25em;}
svg:not(:root){overflow: hidden;}
hr{box-sizing: border-box; height: 0; border-bottom: 1px solid #ccc; margin-bottom: 10px; border-top-style: none; border-right-style: none; border-left-style: none;}
pre{overflow: auto;}
pre.debug{font-size: 14px !important;}
code, kbd, pre, samp{font-family: monospace, monospace; font-size: 1em;}
button, input, optgroup, select, textarea{color: inherit; /* 1 */ font: inherit; /* 2 */ margin: 0; /* 3 */}
button{overflow: visible;}
button, select{text-transform: none;}
button, html input[type="button"], input[type="reset"], input[type="submit"]{-webkit-appearance: button; /* 2 */ cursor: pointer; /* 3 */}
button[disabled], html input[disabled]{cursor: default;}
button::-moz-focus-inner, [type="button"]::-moz-focus-inner, [type="reset"]::-moz-focus-inner, [type="submit"]::-moz-focus-inner{border-style: none; padding: 0;}
input{line-height: normal; border-radius: 0; box-shadow: none;}
input[type="checkbox"], input[type="radio"]{box-sizing: border-box; /* 1 */ padding: 0; /* 2 */}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button{height: auto;}
[type="search"]::-webkit-search-cancel-button, [type="search"]::-webkit-search-decoration{-webkit-appearance: none;}
input[type=text], input[type=email], input[type=password], input[type=tel], input[type=search]{-webkit-appearance: none;}
input[type=number] {-moz-appearance:textfield; -webkit-appearance: textfield; -ms-appearance: textfield;}
input::-webkit-outer-spin-button, input::-webkit-inner-spin-button {-webkit-appearance: none;}
::-webkit-input-placeholder{color: inherit;}
fieldset{border: none; margin: 0; padding: 0;}
textarea{overflow: auto; resize: vertical;}
optgroup{font-weight: bold;}
table{border-collapse: collapse; border-spacing: 0;}
input:-webkit-autofill, textarea:-webkit-autofill, select:-webkit-autofill{-webkit-box-shadow: 0 0 0 30px #fff inset; box-shadow: 0 0 0 30px #fff inset;} /* set browser autocomplete bg yellow color to white */
/*------- /normalize -------*/

@import "defaults.less";

/*------- helpers -------*/
.display-n{display: none;}
.display-ib{display: inline-block;}
.display-b{display: block;}
.display-t{display: table;}
.display-tc{display: table-cell;}
.align-vt{vertical-align: top;}
.align-vm{vertical-align: middle;}
.align-l{text-align: left;}
.align-r{text-align: right;}
.align-c{text-align: center;}
.fz0{font-size: 0;}
.fs-i{font-style: italic;}
.fw-b{font-weight: bold;}
.fw-n{font-weight: normal;}
.float-l, .float-left{float: left;}
.float-r, .float-right{float: right;}
.pos-r{position: relative;}
.pos-a{position: absolute;}
.pos-s{position: static;}
.strong{font-weight: bold;}
.italic{font-style: italic;}
.uppercase{text-transform: uppercase;}
.first{margin-left: 0 !important;}
.last{margin-right: 0 !important;}
.image-left, .alignleft{float: left; margin: 5px 20px 10px 0px;}
.image-right, .alignright{float: right; margin: 5px 0px 10px 20px;}
.align-left{text-align: left;}
.align-right{text-align: right;}
.center{text-align: center;}
.underline{text-decoration: underline;}
.nounderline{text-decoration: none;}
.line-through{text-decoration: line-through;}
.rounded{border-radius: @borderRadius;}

.body-overflow{overflow: hidden;}

.red{color: @red;}
.blue{color: @blue;}
.green{color: @green;}
.orange{color: @orange;}

.toggle-icon{
	width: 16px; height: 16px; flex-shrink: 0;
	&:before, &:after{.pseudo(16px,2px); right: 0px; top: 7px; background: @blue; opacity: 1; .transition(opacity);}
	&:after{width: 2px; height: 16px; right: 7px; top: 0;}

	@media (max-width: @m){
		width: 14px; height: 14px;
		&:before{width: 14px; height: 2px; top: 6px;}
		&:after{width: 2px; height: 14px; right: 6px;}
	}
}

.list{
	list-style: none; padding: 0; margin: 0 0 15px 20px;
	li{
		position: relative; padding: 2px 0 4px 25px;
		&:before{.pseudo(12px, 1px); background: @blue; top: 14px; left: 0; z-index: 1;}
	}

	@media (max-width: @m){
		margin: 0 0 15px 0; font-size: 14px;
		li{
			padding: 2px 0 2px 20px;
			&:before{width: 10px; top: 14px; left: 0;}
		}
	}
}

.no-touch{
	.tel, .tel a, a[href^=tel]{color: @textColor; cursor: default;}
}
.page-wrapper{max-width: 1920px; margin: 0 auto; overflow: hidden;}

:root {
  --contentWidth: 603px;
}
/*------- /helpers -------*/

/*------- buttons -------*/
.btn, input[type=submit], button{
	position: relative; display: inline-flex; align-items: center; justify-content: center; vertical-align: middle; min-height: 65px; border-radius: @borderRadius; padding: 0 15px; font-size: 16px; line-height: 22px; color: @white; font-weight: bold; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); text-decoration: none; .transition(box-shadow); cursor: pointer;
	&:hover{color: @white; box-shadow: 0 100px 10px rgba(0,0,0,0.08) inset; text-decoration: none;}

	@media (max-width: @t){
		min-height: 54px; font-size: 15px;
		&:hover{box-shadow: none;}
	}
	@media (max-width: @m){min-height: 47px; font-size: 14px; line-height: 19px;}
}

.btn-white{
	background: @white; color: @textColor; border: 1px solid @borderColor;
	&:hover{color: @textColor;}
}
.btn-medium{min-width: 180px;}
.btn-rounded{border-radius: 27px; padding: 0 35px;}
.btn-green{background: linear-gradient(225deg, #01AABA 0%, #0092A0 100%);}
.btn-red{background: linear-gradient(226.06deg, #E6473A 0%, #C9221B 100%);}
.cd-compare-btn{.btn; .btn-white;}
.cd-back-btn{.btn;}

.btn-float {
	width: auto;padding: 0 30px; display: flex; align-items: center; justify-content: center; border-radius: 30px; font-size: 14px;line-height: 1;font-weight: bold;color: #fff; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); height: 60px; box-shadow: 5px 8px 20px 0 rgba(3,32,62,0.25); .transition(box-shadow); z-index: 111; cursor: pointer;
	@media (max-width: @m){padding: 0; color: @textColor; background: none; box-shadow: none; height: 100%; flex-direction: column; font-size: 11px; flex-grow: 1;}
	&:hover{
		color: @white;
		@media (max-width: @m){color: @textColor;}
	}
	&:before {
		.icon-service;font: 24px/1 @fonti; margin-right: 7px; position: relative; display: flex; align-items: center; justify-content: center;
		@media (max-width: @m){margin: 0 0 5px; height: 20px; width: 100%; color: @blue;}
	}
	@media (min-width: @t){
		&:hover{box-shadow: none;}
	}
}
.btn-float-round{
	width: 60px; font-size: 0; padding: 0;
	@media (max-width: @m){width: auto; font-size: 11px;}
}
.btn-float-new-cart{
	.btn-green;
	@media (max-width: @m){background: none;}
	span{
		width: 14px; height: 14px; display: flex; position: relative; margin-right: 7px;
		@media (max-width: @m){background: @green; width: 21px; height: 21px; border-radius: 100px; margin-bottom: 5px;}
		&:before, &:after{
			.pseudo(14px,2px); background: @white; position: absolute;
			@media (max-width: @m){width: 10px;}
		}
		&:before{top: 6px;
			@media (max-width: @m){top: 9px; left: 5px;}
		}
		&:after{
			width: 2px; height: 14px; top: 0; left: 6px;
			@media (max-width: @m){height: 10px; top: 5px; left: 9px;}
		}
	}
	&:before{display: none;}
}
.btn-float-scan-card:before{
	.icon-face-scan;
	@media (max-width: @m){margin-bottom: 5px; font-size: 21px;}
}
.btn-float-scan-item:before{
	.icon-scan; font-size: 20px;
}
.btn-float-reset-shipping{
	color: @blue;background: #fff; border: 1px solid @blue;
	@media (max-width: @m){border: 0; color: @textColor;}
	&:hover{
		color: @blue;
		@media (max-width: @m){color: @textColor;}
	}
	&:before {.icon-reset; }
}
/*------- /buttons -------*/

/*------- selectors -------*/
/*
::-webkit-scrollbar { -webkit-appearance: none; width: 5px; }
::-webkit-scrollbar-thumb {
	background-color: @lightGray; border-radius: 5px;
	box-shadow: 0 0 1px rgba(255,255,255,.5);
}
*/
body{
	background: #fff; color: @textColor; .font(@fontSize, @lineHeight, @font);

	@media (max-width: @m){font-size: 14px;}
}
a{
	color: @linkColor; text-decoration: none; .transition();
	&:hover{text-decoration: none; color: @linkHoverColor;}
}
ul, ol{margin: 0; padding: 0;}
h1, h2, h3, h4{
	line-height: 1.2; font-weight: bold; padding-bottom: 15px;
	a, a:hover{text-decoration: none;}
}
h1{font-size: 36px;}
h2{font-size: 28px;}
h3{font-size: 22px;}
h4{font-size: 18px;}
p{padding-bottom: 10px;}

@media (max-width: @t){
	h1{font-size: 30px;}
	h2{font-size: 24px;}
	h3{font-size: 20px;}
	h4{font-size: 16px;}
}
@media (max-width: @m){
	h1, h2, h3, h4{padding-bottom: 10px;}
	h1{font-size: 22px;}
	h2{font-size: 20px;}
	h3{font-size: 18px;}
	h4{font-size: 15px;}
}
/*------- /selectors -------*/

/*------- forms -------*/
label{padding: 0 0 4px 0; display: inline-block;}
select{-moz-appearance: none; -o-appearance:none; -webkit-appearance: none; -ms-appearance: none;}
select::-ms-expand {display: none;}
input, textarea, select{
	-webkit-appearance: none; background-color: transparent; border-radius: 0; width: 100%; height: 50px; background: @white; border-radius: @borderRadius; padding: 0 20px; border: 1px solid @borderColor; font-size: 14px; letter-spacing: -0.3px; font-family: @font; line-height: 1.3; .transition(border-color);

	@media (max-width: @m){height: 47px; padding: 0 15px;}
}
select{
	background: @white url(images/arrow-down.svg) no-repeat; background-size: 10px; background-position: right 20px top 20px; padding-right: 40px;

	@media (max-width: @m){background-position: right 16px top 19px;}
}
input:disabled, textarea:disabled, input:disabled+label, .disabled{cursor: not-allowed !important; color: #ccc;}
input:hover, textarea:hover, select:hover, input:focus, textarea:focus, select:focus{border-color: @borderColor/1.3; outline: 0;}
input[type=submit], button{border: none; display: inline-block;}
input[type=checkbox], input[type=radio]{padding: 0; height: auto; border: none;}
textarea{height: 130px; padding-top: 10px; padding-bottom: 10px; line-height: 19px;}
legend{
	font-size: 16px; line-height: 18px; font-weight: bold;
	a{text-decoration: none;}
}
input[type=checkbox], input[type=radio]{position: absolute; left: -9999px; display: inline;}
input[type=checkbox] + label, input[type=radio] + label{
	cursor: pointer; position: relative; padding: 2px 0 0 32px; min-height: 20px; font-size: 14px; line-height: 1.4; text-align: left;

	@media (max-width: @m){padding-left: 30px;}
}

input[type=checkbox] + label:before{
	.pseudo(20px, 20px); text-indent: 0; color: #fff; border: 1px solid @borderColor; border-radius: @borderRadius; left: 0; text-align: center; top: 0; .icon-check; font: 10px/22px @fonti; .transition(all);

	@media (max-width: @m){width: 18px; height: 18px; font-size: 8px; line-height: 20px; text-indent: 1px;}
}
input[type=radio] + label:before{.pseudo(19px, 19px); border-radius: 200px; color: #fff; border: 1px solid @borderColor; left: 0; text-align: center; top: 0; .transition(all);}

input[type=checkbox]:checked + label:before{color: @white; border-color: @blue; background: @blue;}
input[type=radio]:checked + label:before{background: @blue; border-color: @blue; box-shadow: inset 0px 0px 0px 4px #fff;}

.form-animated-label{
	p{padding-bottom: 10px;}
	p, .field{position: relative;}
	label{position: absolute; top: 17px; left: 20px; padding: 0; cursor: text; z-index: 10; .transition(all); font-size: 14px; line-height: 19px; letter-spacing: -0.3px;}
	.global-error{width: 100%;}
	.focus,.ffl-floated{
		label{top: 7px; font-size: 11px; line-height: 15px; letter-spacing: -0.2px; color: @blue; font-weight: normal;}
		input,select{padding-top: 15px; font-size: 14px;}
		textarea{padding-top: 25px;}
	}

	input[type=checkbox] + label, input[type=radio] + label{cursor: pointer; position: relative; padding: 2px 0 0 32px; min-height: 20px; font-size: 14px; line-height: 1.4; text-align: left; top: unset; left: unset;}

	@media (max-width: @m){
		label{top: 15px; left: 15px;}
		.focus,.ffl-floated{
			label{top: 7px; font-size: 10px; line-height: 14px; letter-spacing: -0.1px;}
		}
	}
}
/*------- /forms -------*/

/*------- tables -------*/
.table{
	width: 100%; border-spacing: 0; margin: 10px 0px 20px;
	th{font-weight: bold; font-size: 14px; text-align: left; padding: 6px 0; border-bottom: 1px solid @gray;}
	td{border-bottom: 1px solid @gray; padding: 6px 0;}
	&.stripe tbody tr:nth-child(even){background: #E9E9E9;}
}
.table-row{display: table; width: 100%;}
.table-col{display: table-cell;}
.table-wrapper{overflow-x: scroll; -webkit-overflow-scrolling: touch;}

::-webkit-scrollbar {-webkit-appearance: none; width: 4px; background: @gray;}
::-webkit-scrollbar-thumb {background-color: @blue;}

.ts-header{
	font-size: 14px; line-height: 1.4; font-weight: bold; letter-spacing: -0.3px; color: @white; display: flex; align-items: center; background: #000; padding: 13px 15px 13px 20px; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border-radius: @borderRadius;

	@media (max-width: @iframeT){display: none;}
}
.ts-row{
	display: flex; align-items: center; padding: 18px 15px 18px 20px; font-size: 14px; line-height: 1.4; letter-spacing: -0.3px; color: @textColor; border-radius: @borderRadius; flex-grow: 1;

	@media (max-width: @iframeT){display: block; padding: 15px 100px 15px 15px; font-size: 12px; letter-spacing: -0.2px; position: relative;}
	@media (max-width: @iframeM){padding: 10px 100px 10px 15px;}
}
.ts-row:nth-child(even){
	background: rgba(240, 242, 244, 0.65);

	@media (max-width: @iframeT){background: @white;}
}
.ts-row:nth-child(odd){
	@media (max-width: @iframeT){background: rgba(240, 242, 244, 0.65);}
}
.ts-col{
	flex-shrink: 0; padding-right: 15px;

	@media (max-width: @iframeT){flex-shrink: unset; padding: 0 0 5px;}
	@media (max-width: @iframeM){padding: 0 0 3px;}
}
/*------- /tables -------*/

/*------- slick -------*/
.slick-slider{position: relative; display: block; box-sizing: border-box; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; -ms-touch-action: pan-y; touch-action: pan-y; -webkit-tap-highlight-color: transparent; }
.slick-list{
	position: relative; overflow: hidden; display: block; margin: 0; padding: 0;
	&:focus{outline: none; }
	&.dragging{cursor: pointer; cursor: pointer; }
}
.slick-slider .slick-track, .slick-slider .slick-list{-webkit-transform: translate3d(0, 0, 0); -moz-transform: translate3d(0, 0, 0); -ms-transform: translate3d(0, 0, 0); -o-transform: translate3d(0, 0, 0); transform: translate3d(0, 0, 0);}
.slick-track{
	position: relative; left: 0; top: 0; display: block; margin-left: auto; margin-right: auto;
	&:before, &:after {content: ""; display: table; }
	&:after {clear: both; }
	.slick-loading & {visibility: hidden; }
}
.slick-slide {
	float: left; height: 100%; min-height: 1px; display: none;
	img {display: block; }
	&.slick-loading img {display: none; }
	&.dragging img {pointer-events: none; }
	.slick-initialized & {display: block; }
	.slick-loading & {visibility: hidden; }
	.slick-vertical & {display: block; height: auto; }
}
.slick-arrow.slick-hidden {display: none;}
/*------- /slick -------*/

/*------- info messages -------*/
.error{
	color: @errorColor; display: block; padding: 7px 0 0 20px; font-size: 12px; line-height: 16px;

	@media (max-width: @m){font-size: 11px; padding-left: 15px;}
}
.global-error, .global-success, .global-warning{
	font-size: 14px; margin: 0 0 15px 0; line-height: 21px; padding: 15px 15px 15px 55px; min-height: 50px; background: @errorColor url(images/icons/danger-white.svg) no-repeat 20px 15px; background-size: 25px auto; color: #fff; position: relative;

	@media (max-width: @m){font-size: 12px; min-height: 44px; background: @errorColor url(images/icons/danger-white.svg) no-repeat 15px 12px; background-size: 20px auto; padding: 12px 10px 12px 50px;}
}
.global-error a{color: #fff; text-decoration: underline;}
.global-success{background-color: @successColor; background-image: url(images/icons/check.svg); background-size: 20px auto; background-position: 20px center;}
.field_error_input, .field_error_input_radio{border-color: @errorColor;}
/*------- /info messages -------*/

/*------- loading -------*/
.lloader{background-size: 50px;}
.load-more-loader{
	display: block; font-size: 14px; text-align: center; margin: 30px 0; width: 100%; color: @textColor!important;
	span{display: block; position: relative; background: url(images/loader.svg) no-repeat center top; background-size: 50px auto; padding: 50px 0 0;}
}
/*------- /loading -------*/

/*------- sidebar -------*/
.sidebar{
	width: 21%; flex-shrink: 0; color: @white; background: linear-gradient(270deg, #0078B4 0%, #0050A0 100%); display: flex; flex-direction: column; position: fixed; left: 0; top: 0; bottom: 0; z-index: 1111;

	@media (max-width: @t){width: 30%;}
	@media (max-width: @m){width: 100%; background: #0050a0; top: unset; right: 0; flex-direction: row; z-index: 11111;}
}

.sidebar-header{
	display: flex; align-items: center; justify-content: space-between; padding: 21px 12% 20px;

	@media (max-width: @t){padding: 21px 7% 20px;}
	@media (max-width: @m){display: none;}
}
.logo{
	width: 114px; height: 23px; background: url(images/logo.svg) no-repeat; background-size: contain; background-position: center; display: block; margin-right: 20px; flex-shrink: 0;

	@media (max-width: @t){width: 100px;}
}
.sidebar-label{
	font-size: 12px; line-height: 1.2; font-weight: bold; text-transform: uppercase;

	@media (max-width: @l){font-size: 11px; line-height: 1;}
}

.sidebar-nav{
	list-style: none; margin: 0; padding: 0; flex-grow: 1;
	&>li{
		display: block; font-size: 0; line-height: 0; position: relative; .transition(background);
		&>a{
			display: flex; align-items: center; font-size: 16px; line-height: 1.5; color: @white; margin-bottom: -1px; padding: 4.3% 12%; background: transparent; border-top: 1px solid rgba(255,255,255,0.3); border-bottom: 1px solid rgba(255,255,255,0.3); position: relative;
			&:hover{text-decoration: underline;}
			&.active{
				color: @textColor; background: @white; z-index: 1;
				.sidebar-nav-icon img{filter: unset;}
			}
		}
		&.has-children:before{.pseudo(12px,12px); background: @white; right: -6px; top: 50%; transform: translateY(-50%) rotate(45deg); opacity: 0; .transition(opacity); z-index: 1;}
		&.active-submenu{
			background-color: rgba(0,0,0,0.15);
			&:before{opacity: 1;}
			.sidebar-submenu{opacity: 1; visibility: visible; max-height: unset;}
		}
	}
	.sidebar-settings-m, .ww{display: none;}

	@media (max-width: @t){
		&>li>a{
			font-size: 14px; padding: 4.3% 7%;
			&:hover{text-decoration: none;}
		}
	}
	@media (max-width: @m){
		display: flex;
		&>li{
			display: block; font-size: 0; line-height: 0; flex-grow: 1;
			&>a{
				justify-content: center; text-align: center; height: 50px; font-size: 0; line-height: 0; margin: 0 0 0 -1px; padding: 0; border: none; border-right: 1px solid rgba(255,255,255,0.3);
				&:hover{text-decoration: none;}
				&.active{border-top: 1px solid @borderColor;}
			}
		}
		&>li:first-child a{margin-left: 0;}
		.m-hidden, .m-move{display: none;}
		.ww{display: block;}

		.sidebar-settings-m{
			display: block;
			&>a{
				border: none; border-top: 1px solid transparent; .transition(background);
				&>span:before{.icon-settings; font: 22px/22px @fonti; color: @white; position: absolute; .transition(color);}
			}
			ul{
				display: none; position: fixed; bottom: 50px; left: 0; right: 0; text-align: center; background: @white; padding: 20px 15px 10px;
				&>li{
					display: block; font-size: 14px; line-height: 1.4; color: @textColor; margin-bottom: 10px;
					&>a{
						display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.3px; text-align: center; color: @textColor; padding: 0; margin: 0; text-decoration: none; border: none;
						&:before{content: none;}
						.m{display: block;}
						.d{display: none;}
					}
				}
				.m-move{display: block;}
				.sidebar-nav-icon{display: none;}
			}
			&.active{
				&>a{
					background: @white; border-color: @borderColor!important; z-index: 1;
					&>span:before{color: @blue;}
				}
				ul{display: block; z-index: 111;}
			}
			
			.cw-compare{width: auto; height: auto; min-height: unset; padding: 0; margin: 0 0 10px; border: none; display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.3px; font-weight: normal; text-align: center; color: @textColor;}
			.cw-compare-counter{font-weight: normal; position: static; margin-left: 3px; color: @blue;}
		}
	}
}
.sidebar-nav-icon{
	width: 22px; height: 22px; flex-shrink: 0; margin-right: 15px; display: flex; align-items: center; justify-content: center;
	img{width: auto; height: auto; max-width: 22px; max-height: 22px; display: block; filter: brightness(0) invert(1);}

	@media (max-width: @m){margin: 0;}
}
.sidebar-submenu{
	display: block; width: 230px; background: @white; padding: 22px 30px; position: absolute; left: 100%; top: 0; box-shadow: 10px 5px 40px 0 rgba(0,34,67,0.3); max-height: 0; overflow: hidden; opacity: 0; visibility: hidden; .transition(opacity);
	&>li{
		display: block; font-size: 0; line-height: 0; padding-bottom: 9px;
		&:last-child{padding-bottom: 0;}
		&>a{
			display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.2px; color: @textColor;
			&:hover{color: @blue; text-decoration: underline;}
		}
	}

	@media (max-width: @m){display: none;}
}

.setting-nav-active{
	@media (max-width: @m){
		overflow: hidden; position: relative;
		&:before{.pseudo(auto,auto); position: fixed; top: 0; right: 0; bottom: 0; left: 0; background-color: rgba(0,24,47,0.6); z-index: 11111;}
	}
}

.sidebar-alert-message{
	display: flex; align-items: center; width: 380px; padding: 20px; background: @orange; font-size: 14px; color: @white; position: fixed; bottom: 30px; left: 30px; z-index: 1111;

	@media (max-width: @ms){width: calc(~"100vw - 30px"); margin: 0 15px; padding: 15px; font-size: 12px; bottom: 55px; left: 0; right: 0;}
}

.sidebar-footer{
	padding: 12.5% 12% 7%; background-color: rgba(0,0,0,0.15); border-top: 1px solid rgba(255,255,255,0.3);

	@media (max-width: @t){padding: 10% 7% 6%;}
	@media (max-width: @m){display: none;}
}
.sidebar-user{
	font-size: 16px; line-height: 1.5; letter-spacing: -0.2px; font-weight: bold; padding-bottom: 10px;

	@media (max-width: @t){font-size: 14px;}
}

.sidebar-footer-nav{
	list-style: none; margin: 0; padding: 0;
	&>li{
		display: block; font-size: 0; line-height: 0; margin-bottom: 15px;
		a{
			display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.2px; color: @white; padding-left: 30px; position: relative;
			&:before{font: 19px/19px @fonti; color: @white; position: absolute; left: 0;}
			&:hover{text-decoration: underline;}
			
			.m{display: none;}
			.d{display: block;}
		}
	}
}
.sidebar-location a:before{.icon-pin;}
.sidebar-department a:before{.icon-settings;}
.sidebar-logout a:before{.icon-logout;}
.sidebar-login a:before{.icon-user;}
/*------- /sidebar -------*/

/*------- department change -------*/
.departments-change-box{
	padding: 40px 50px;

	@media (max-width: @m){padding: 15px 20px;}
}
.department-change-title{
	font-size: 16px; letter-spacing: -0.32px; font-weight: bold; padding-bottom: 15px;

	@media (max-width: @m){font-size: 14px; padding-bottom: 12px;}
}
/*------- /department change -------*/

/*------- main -------*/
.main{
	display: flex;
	
	@media (max-width: @m){display: block;}
}
.main-content{
	width: 79%; margin-left: 21%; flex-grow: 1; position: relative;

	@media (max-width: @t){width: 70%; margin-left: 30%;}
	@media (max-width: @m){width: 100%; margin: 0 0 50px;}
}
.main-header{
	width: 79%; height: 65px; display: flex; align-items: center; position: fixed; left: 21%; right: 0; top: 0; background: @white; z-index: 500;

	@media (max-width: @t){width: 70%; left: 30%;}
	@media (max-width: @m){width: 100%; height: 47px; left: 0;}
}

.content-section{
	padding: 100px 130px 7.3% 7.3%;

	@media (max-width: @t){padding: 100px 6% 6% 6%;}
	@media (max-width: @m){padding: 47px 0 0;}
}
.cms-content{
	flex-grow: 1;
	h2,h3,h4{padding-top: 20px;}
	ul{.list;}
	ol{
		margin: 0 0 15px 38px;
		li{margin: 0 0 5px;}
	}
	img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}
	iframe{width: 100%;}
	figure{margin-bottom: 10px;}
	figcaption{padding: 10px 0 0;}
	.btn, input[type=submit], button{min-width: 180px; margin-bottom: 5px;}
	table{.table; display: block; width: 100%!important;}

	@media (max-width: @m){
		padding: 15px;
		table{font-size: 12px;}
	}
}

.header-btn{width: 65px; height: 65px; border-radius: 0; font-size: 0; line-height: 0; padding: 0; position: relative; flex-shrink: 0; border: none;}
.header-btn-count{font-size: 12px; line-height: 17px; font-weight: 600; color: @textColor; position: absolute; top: 11px; right: 12px;}
/*------- /main -------*/

/*------- cw -------*/
.cw-compare{
	border-bottom: 1px solid @borderColor;
	&:before{.icon-compare; font: 20px/20px @fonti; color: @blue; position: absolute;}
	.m{display: none;}

	@media (max-width: @m) {
		display: none; border: none;
		&:before{content: none;}
		.m{display: inline-block;}
		.d{display: none;}
	}
}
/*------- /cw -------*/

/*------- cart widget -------*/
.ww{
	z-index: 111;
	&.active{
		.ww-items:after{.icon-cart;}
		.ww-items:hover .ww-counter{color: @white;}
		.ww-counter{border-color: @blue; color: @white;}
		&:hover .ww-preview{opacity: 1; max-height: unset; visibility: visible;}	
	}

	@media (max-width: @m){display: none; font-size: 0; line-height: 0; flex-grow: 1; z-index: 0;}
}
.ww-items{
	&:after{.icon-cart-empty; font: 21px/21px @fonti; color: @white; position: absolute;}

	@media (max-width: @m){
		display: flex; width: auto; height: 50px; margin: 0 0 0 -1px; padding: 0; border: none; border-right: 1px solid rgba(255,255,255,0.3); background: transparent; font-size: 0; line-height: 0;
		&:after{position: relative; width: 22px; height: 22px; padding: 5px 0 0;}
	}
}
.ww-counter{
	color: @white;

	@media (max-width: @m){position: absolute; top: 3px; left: 50%; font-size: 10px; line-height: 14px;}
}
/*------- /cart widget -------*/

/*------- categories -------*/
.categories{width: 100%; padding: 0; margin: 0; list-style: none; position: relative;}
.category{
	display: block; margin-bottom: -1px; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; font-size: 0; line-height: 0; 
	&:first-child{border-top: none;}
	&:last-child{border-bottom: none;}
	&.has-children{
		.category-title .toggle-icon{display: flex;}
	}
	&.active{
		&>a{color: @blue;}
		.category-title .toggle-icon:after{opacity: 0;}
		.subcategories{max-height: 10000px;}
	}
	&>a{
		display: flex; align-items: center; padding: 15px 0; font-size: 18px; line-height: 1.4; letter-spacing: -0.5px; position: relative; .transition(color);
		@media (min-width: @t){
			&:hover{color: @blue;}
		}
	}
	.category-img{
		display: flex; align-items: center; width: 60px; max-height: 60px; flex-shrink: 0; margin-right: 25px;
		img{width: auto; height: auto; max-width: 100%; max-height: 100%;}
	}
	.category-title{
		display: block; width: 100%; padding-right: 35px; position: relative;
		.toggle-icon{display: none; position: absolute; top: 50%; right: 0; transform: translateY(-50%);}
	}

	@media (max-width: @t){
		&>a{padding: 10px 0; font-size: 16px;}
		.category-img{width: 50px; height: 50px; margin-right: 15px;}
	}
	@media (max-width: @m){
		&.active{
			&>a{
				color: @white;
				&:before{opacity: 1; max-height: unset; transition: opacity 0.7s;}
				&:after{opacity: 1;}
			}
			.category-title{
				color: @white;
				.toggle-icon{
					&:before, &:after{background: @white;}
				}
			}
			.subcategories{padding: 15px 15px 5px;}
		}
		&>a{
			padding: 15px 15px 14px; font-size: 14px; letter-spacing: -0.3px;
			&:before{.pseudo(10px,10px); background: #0152a1; position: absolute; top: calc(~"100% - 7px"); left: 15px; .rotate(45deg); opacity: 0; max-height: 0;}
			&:after{.pseudo(auto,auto); top: 0; right: 0; bottom: 0; left: 0; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); opacity: 0; .transition(opacity);}
			&:hover{color: @textColor;}
		}
		.category-img{display: none;}
		.category-title{
			z-index: 1;
			.toggle-icon{
				&:before, &:after{.transition(all);}
			}
		}
	}
}

.subcategories{
	width: 100%; display: flex; flex-wrap: wrap; padding: 0; margin: 0; list-style: none; padding: 0 0 0 115px; max-height: 0; overflow: hidden; .transition(max-height);

	@media (max-width: @t){padding: 0 0 0 65px;}
	@media (max-width: @m){padding: 0 15px; transition: max-height 0.3s, padding 0.3s;}
}
.subcategory{
	display: block; width: calc(~"100% / 4"); min-width: 200px; font-size: 0; line-height: 0; padding: 0 40px 50px 0;
	&>a{display: block; padding: 0 0 5px; font-size: 18px; line-height: 1.4; letter-spacing: -0.5px; color: @blue; position: relative;}
	.subcategory-icon{
		display: flex; align-items: center; justify-content: center; position: absolute; left: -30px; max-width: 24px; max-height: 24px; opacity: 0.7;
		img{width: auto; height: auto; width: 22px; max-height: 22px; display: block;}
	}
	.subcategory-title{
		display: block; position: relative;
		&:hover{text-decoration: underline;}
		.toggle-icon{display: none; position: absolute; right: 0; top: 0;}
	}

	@media (max-width: @t){
		width: calc(~"100% / 3"); min-width: 170px; padding: 15px 20px 30px 0;
		&>a{font-size: 16px;}
		.subcategory-icon{display: none;}
	}
	@media (max-width: @m){
		width: 100%; min-width: unset; padding: 0 0 15px 0;
		&.has-children>a .toggle-icon{display: flex;}
		&>a{font-size: 14px; letter-spacing: -0.3px; color: @textColor; padding: 0;}
		.subcategory-title:hover{text-decoration: none;}

		&.active{
			&>a{color: @blue;}
			.subcategory-title .toggle-icon:after{opacity: 0;}
			.subsubcategories{max-height: 5000px; padding: 10px 0 0;}
		}
	}
}

.subsubcategories{
	padding: 0; margin: 0; list-style: none;

	@media (max-width: @m){max-height: 0; overflow: hidden; transition: max-height 0.3s, padding 0.3s;}
}
.subsubcategory{
	display: block; font-size: 0; line-height: 0; padding: 0 0 5px;
	&>a{
		display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.5px; position: relative; .transition(color);
		.counter{display: none; font-size: 12px; line-height: 1.4; letter-spacing: -0.5px; color: rgba(51,51,51,0.5); position: absolute; right: 0; top: 1px;}
		&:hover{text-decoration: underline; color: @blue;}
	}

	@media (max-width: @t){
		&>a{
			font-size: 13px; letter-spacing: -0.3px;
			&:hover{text-decoration: none; color: @textColor;}
		}
	}
	@media (max-width: @m){
		padding-bottom: 7px;
		&>a{
			padding: 0 30px 0 20px;
			&:before{.pseudo(10px,2px); background: #ADBAC4; left: 0; top: 7px;}
			.counter{display: block;}
		}
	}
}
/*------- /categories -------*/

.cp{
	display: flex; flex-grow: 1; border: 1px solid @borderColor; margin-bottom: -1px; position: relative; .transition(all);
	@media (min-width: @t){
		&:hover{border-color: @blue; z-index: 1;}
	}

	@media (max-width: @m){background: @white; margin-bottom: 15px; border-radius: @borderRadius;}
}
.cp-col1{
	display: flex; flex-direction: column; flex-grow: 1; padding: 20px 20px 10px 25px; position: relative;

	@media (max-width: @m){padding: 0;}
}
.cp-badges{
	display: flex; align-items: center; position: absolute; top: 22px; right: 20px;

	@media (max-width: @m){top: unset; bottom: 15px; right: 15px;}
}
.cp-badge{
	width: 14px; height: 14px; flex-shrink: 0; border-radius: @borderRadius; margin-left: 5px; font-size: 0; line-height: 0; position: relative; z-index: 1;
	&:after{.pseudo(auto,25px); top: 100%; left: -20px; right: -20px; display: none; visibility: hidden;}
	&.discount{background: @red;}
	&.new{background: #0050A0;}
	&.gift{background: @yellow;}
	&.coupon{background: url(images/star.svg) no-repeat; background-size: contain; background-position: center;}
	&.uau{background: #f57855;}
	&:hover{
		&:after{display: block; visibility: visible;}
		.cp-badge-tooltip{max-height: unset; opacity: 1; visibility: visible; z-index: 1;}
	}

	@media (max-width: @m){
		width: 12px; height: 12px;
		&:after{content: none;}
	}
}
.cp-badge-tooltip{
	width: auto; min-height: 28px; white-space: nowrap; padding: 5px 10px; border-radius: @borderRadius; background: @white; font-size: 12px; line-height: 18px; letter-spacing: -0.15px; text-align: center; position: absolute; top: 25px; left: 50%; transform: translateX(-50%); flex-shrink: 0; box-shadow: 0 0 20px 0 rgba(0,34,67,0.25); max-height: 0; opacity: 0; visibility: hidden; .transition(opacity);
	&:before{.pseudo(7px,7px); background: @white; top: -3px; left: 50%; transform: translateX(-50%) rotate(45deg);}

	@media (max-width: @t){display: none;}
}
.cp-col1-top{flex-grow: 1;}
.cp-info-top{
	@media (max-width: @m){display: flex; min-height: 65px; position: relative;}
}
.cp-info-top-left{
	@media (max-width: @m){flex-grow: 1; padding: 13px 15px; position: relative;}
}
.cp-title{
	font-size: 14px; line-height: 1.4; letter-spacing: -0.2px; margin-right: 25%; padding: 0;
	a{
		.transition(color);
		&:hover{color: @blue;}
	}

	@media (max-width: @m){
		font-size: 12px; letter-spacing: normal; margin-right: 0;
		a:hover{color: @textColor;}
	}
}
.cp-info{
	display: flex; align-items: center; margin-top: 5px; font-size: 12px; line-height: 1.4;

	@media (max-width: @m){flex-wrap: wrap; padding-right: 35px;}
}
.cp-code{
	padding-right: 7px; margin-right: 6px; position: relative;
	&:before{.pseudo(1px,11px); right: 0; top: 2px; background: #CCD8E2;}

	@media (max-width: @m){
		padding-right: 0; margin-right: 0;
		&:before{content: none;}
	}
}
.cp-category{
	//padding-right: 7px; margin-right: 6px; position: relative;
	//&:before{.pseudo(1px,11px); right: 0; top: 2px; background: #CCD8E2;}
	a{
		color: @blue;
		&:hover{text-decoration: underline;}
	}
	
	@media (max-width: @m){display: none;}
}
.cp-logistic{
	padding-left: 7px; margin-left: 6px; text-transform: uppercase; font-weight: bold; position: relative;
	&:before{.pseudo(1px,11px); left: 0; top: 2px; background: #CCD8E2;}
}
.cp-seller{
	padding-right: 7px; margin-right: 6px; position: relative;
	&:before{.pseudo(1px,11px); right: 0; top: 2px; background: #CCD8E2;}
	a{
		color: @blue;
		&:hover{text-decoration: underline;}
	}
	span{
		padding-left: 18px; font-weight: bold; position: relative;
		&:before{.icon-seller; font: 15px/1 @fonti; color: @blue; position: absolute; left: 0;}
	}

	@media (max-width: @m){
		span{
			padding: 0;
			&:before{content: none!important;}
		}
	}
}
.cp-condition-wrapper{
	@media (max-width: @ms){width: 100%;}
}
.cp-condition{
	display: inline-flex; align-items: center; justify-content: center; min-height: 22px; margin-left: 8px; padding: 2px 10px; background: #CDD700; border-radius: 11px; font-size: 11px; line-height: 1.3; color: #0050A0; font-weight: 600;
	
	@media (max-width: @ms){min-height: 20px; margin: 6px 0 0; padding: 2px 8px;}
}


.cp-info-top-right{
	display: none;

	@media (max-width: @m){display: flex; flex-direction: column; width: 60px; flex-shrink: 0; padding: 0; border: none;}
}

.cp-attributes{
	font-size: 12px; line-height: 1.4; margin: 10px 25% 0 0;

	@media (max-width: @m){padding: 13px 15px; margin: 0; border-top: 1px solid @borderColor;}
}
.cp-attribute:last-child .comma{display: none;}

.cp-bottom{
	display: flex; align-items: flex-end; margin-top: 10px;

	.cd-compare-btn{
		width: 40px; height: 40px; min-height: unset; margin: 0; padding: 0; font-size: 0; line-height: 0; box-shadow: none;
		&:before{.icon-compare; font: 16px/16px @fonti; color: @blue; position: absolute;}

		&.compare_active{
			background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border-color: transparent; .transition(box-shadow);
			&:before{color: @white;}
			&:hover{box-shadow: 0 100px 10px rgba(0,0,0,0.08) inset;}
		}

		@media (max-width: @t){min-height: unset; font-size: 0;}
		@media (max-width: @m){width: 38px; height: 38px; min-height: unset;}
	}
	.cd-compare-icon{display: none!important;}
	.cd-compare-info{
		line-height: 1.2; color: @blue; left: unset; right: -20px;
		&:before{left: unset; right: 33px;}

		@media (max-width: @m){
			padding: 5px 15px; font-size: 11px; right: -15px;
			&:before{right: 27px;}
		}
	}

	@media (max-width: @m){margin-top: 0; padding: 10px 10px 10px 15px; border-top: 1px solid @borderColor;}
}
.cp-bottom-left{
	display: flex; align-items: center; flex-wrap: wrap;

	@media (max-width: @m){padding-right: 15px;}
}
.cp-price{
	display: flex; align-items: baseline; margin-right: 15px; font-size: 12px; line-height: 17px; font-weight: bold;
	&.uau-badge{
		.cp-current-price:not(.blue), .cp-installments-price>strong {
			color: #f57855;
		}
	}

	@media (max-width: @m){flex-grow: 1; flex-wrap: wrap; width: 100%; margin: 0; padding: 0;}
}
.cp-old-price{font-weight: normal; margin-right: 5px;}
.cp-current-price{font-size: 14px;}
.cp-installments-price{
	color: @textColor; font-weight: normal; padding-left: 7px; margin-left: 5px; position: relative;
	&:before{content: "/"; position: absolute; left: 0;}
}
.cp-discount-expire{
	font-weight: normal; padding-left: 9px; margin-left: 8px; position: relative;
	&:before{.pseudo(1px,11px); left: 0; top: 2px; background: #CCD8E2;}

	@media (max-width: @m){
		width: 100%; display: block; padding: 0; margin: 5px 0 0;
		&:before{content: none;}
	}
}
.cp-loyalty-price{
	padding-left: 15px; font-size: 12px; text-align: right; color: @green;
	span{
		padding-left: 24px; position: relative;
		&:before{.icon-loyalty; font: 13px/13px @fonti; color: @green; position: absolute; left: 0; top: 0;}
	}

	@media (max-width: @m){
		width: 100%; display: flex; text-align-last: left; padding: 0; margin-top: 7px;
		span:before{font-size: 11px; line-height: 11px; top: 2px;}
	}
}
.cp-badge-coupon{
	display: flex; align-items: center; margin: 1px 10px 1px 0; border-radius: @borderRadius; font-size: 12px; line-height: 1.5; cursor: pointer; z-index: 1;
	@media (min-width: @t){
		&:hover{
			.cp-badge-coupon-info span:before{color: @blue;}
		}
	}

	@media (max-width: @m){margin: 5px 5px 0 0; font-size: 11px;}
}
.cp-badge-coupon-title{
	display: flex; align-items: center; white-space: nowrap; min-height: 28px; padding: 2px 8px; background: @red; border-radius: 2px 0 0 2px; font-weight: bold; color: @white; position: relative;
	&:after{.pseudo(5px,5px); background: @red; right: -3px; .rotate(45deg); z-index: 1;}
	span{
		padding-left: 27px; position: relative;
		&:before{.icon-coupon; font: 13px/1 @fonti; color: @white; position: absolute; top: 2px; left: 0;}
	}

	@media (max-width: @m){
		min-height: 24px;
		&:after{width: 4px; height: 4px; right: -2px;}
		span{
			padding-left: 24px; position: relative;
			&:before{font-size: 11px; top: 3px;}
		}
	}
}
.cp-badge-coupon-info{
	display: flex; align-items: center; white-space: nowrap; min-height: 28px; padding: 2px 8px 2px 10px; background: @white; border: 1px dashed #ADBAC4; border-left: none; border-radius: 0 2px 2px 0; position: relative;
	.icon{
		display: flex; justify-content: center; width: 13px; height: 13px; margin-left: 5px;
		&:before{.icon-sheet; font: 13px/1 @fonti; color: @textColor; position: relative;}
	}

	@media (max-width: @m){
		min-height: 24px;
		.icon{
			width: 12px; height: 12px;
			&:before{font-size: 12px; top: 0;}
		}
	}
}
.cp-badge-coupon-tooltip{
	display: flex; align-items: center; justify-content: center; white-space: nowrap; min-height: 26px; padding: 2px 10px; background: @white; border: 1px solid @blue; border-radius: @borderRadius; font-size: 11px; line-height: 1.4; color: @blue; font-weight: bold; text-align: center; position: absolute; top: calc(~"100% - -2px"); box-shadow: 0 0 10px 0 rgba(4,45,86,0.2);
	&:before{.pseudo(5px,5px); background: @white; border-left: 1px solid @blue; border-top: 1px solid @blue; top: -4px; .rotate(45deg); z-index: 1;}
}
.cp-payments-options{width: 100%; margin-top: 3px; font-size: 11px; line-height: 1.4;}
.cp-payments-option{
	display: block; width: 100%; margin-bottom: 2px;
	&:last-child{margin-bottom: 0;}
}
.cp-btns{display: flex; align-items: center; justify-content: flex-end; flex-grow: 1;}
.cp-btn-addtocart{
	width: 40px; height: 40px; min-height: unset; flex-shrink: 0; margin-left: 5px; padding: 0; font-size: 0; line-height: 0;
	&:before{.icon-cart-empty; font: 19px/19px @fonti; color: @white; position: absolute;}

	@media (max-width: @m){
		width: 38px; height: 38px; margin-left: 0;
		&:before{font-size: 17px; line-height: 17px;}
	}
}
.cp-btn-details:before{.icon-info; font: 17px/17px @fonti;}

.cp-col2{
	width: 100px; display: flex; flex-direction: column; flex-shrink: 0; padding: 20px 10px 10px; border-left: 1px solid @borderColor;

	@media (max-width: @m){display: none;}
}
.cp-image{
	width: 100%; display: flex; justify-content: center; position: relative; flex-grow: 1;
	&>a{display: flex; align-items: center; justify-content: center; height: 60px;}
	img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}

	@media (max-width: @m){display: none;}
}
.cp-no-image{
	width: 100%; height: 45px; display: none; align-items: center; justify-content: center; position: relative; flex-grow: 1; border-left: 1px solid @borderColor;
	img{width: auto; height: auto; max-width: 40px; max-height: 40px; display: block;}

	@media (max-width: @m){display: flex;}
}
.cp-available-qty{
	display: flex; align-items: center; justify-content: center; width: 100%; background: @green2; margin-top: 15px; padding: 6px 5px; border-radius: @borderRadius; font-size: 11px; line-height: 1.4; text-align: center; font-weight: bold; color: @white;
	&.supplier{background: #F08747;}
	&.not-available-in-store{background: #F5B800;}
	&.not-available{background: #ADBAC4;}
	&.preorder{background: @blue;}

	@media (max-width: @m){display: none;}
}
.cp-available-qty-m{
	display: none; margin: 0 0 -1px!important; padding: 3px 5px!important; border-radius: 0!important;

	@media (max-width: @m){display: flex;}
}

.cp-special{
	width: calc(~"100% / 3"); height: auto; margin: 0 0 0 -1px; padding: 0; border: 1px solid @borderColor; position: relative; display: flex; flex-direction: column;

	@media (max-width: @t){min-width: 60%; width: 60%; white-space: normal; margin: 0 -1px 0 0;}
	@media (max-width: @m){min-width: 85%; width: 85%;}
}
.cp-special-image-container{
	position: relative; padding-top: 30px;

	@media (max-width: @m){padding-top: 20px;}
}
.cp-special-image{
	width: 100%; display: flex; align-items: center; justify-content: center; position: relative;
	&>a{display: flex; align-items: center; justify-content: center; height: 240px;}
	img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}

	@media (max-width: @t){
		a{height: 180px;}
	}
	@media (max-width: @m){
		a{height: 140px;}
	}
}
.cp-special-badges{
	display: flex; align-items: center; position: absolute; top: 14px; left: 20px; z-index: 1;

	@media (max-width: @t){left: 15px; top: 12px;}
}
.cp-special-badge{
	margin: 0 5px 0 0;
	&:after{.pseudo(auto,25px); top: 100%; left: -20px; right: -20px; display: none; visibility: hidden;}
	&.discount{background: @red;}
	&.new{background: #0050A0;}
	&.gift{background: @yellow;}
	&.coupon{background: url(images/star.svg) no-repeat; background-size: contain; background-position: center;}
	&:hover{
		&:after{display: block; visibility: visible;}
		.cp-badge-tooltip{max-height: unset; opacity: 1; visibility: visible; z-index: 1;}
	}

	@media (max-width: @m){
		width: 12px; height: 12px;
		&:after{content: none;}
	}
}
.cp-special-container{
	display: inline-block; position: absolute; top: 0; right: 0; height: 33px; z-index: 11;

	@media (max-width: @m){height: 26px;}
}
.cp-special-compare-remove{
	width: 33px; height: 33px; display: flex; align-items: center; justify-content: center; flex-shrink: 0; background: linear-gradient(226.06deg, #E6473A 0%, #C9221B 100%); position: relative; .transition(box-shadow);
	&:before{.icon-x; font: 14px/14px @fonti; color: @white; position: absolute;}
	&:hover{box-shadow: 0 100px 10px rgba(0,0,0,0.08) inset;}

	@media (max-width: @m){
		width: 26px; height: 26px;
		&:before{font-size: 12px; line-height: 12px;}
	}
}

.cp-special-cnt{
	display: block; padding: 20px 30px; font-size: 12px; line-height: 1.4; flex-grow: 1;

	@media (max-width: @t){padding: 10px 15px;}
	@media (max-width: @m){font-size: 11px;}
}
.cp-special-category{
	display: block; color: @blue; padding-bottom: 2px;
	&:hover{text-decoration: underline; color: @blue;}
}
.cp-special-title{
	display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.17px; font-weight: normal; padding: 0;
	a{
		display: block;
		&:hover{color: @blue;}
	}

	@media (max-width: @t){font-size: 13px;}
	@media (max-width: @m){font-size: 12px;}
}
.cp-special-code{display: block; padding-top: 5px;}

.cp-special-price{
	display: block; font-size: 12px; line-height: 1.4; padding: 0 30px 20px;

	@media (max-width: @t){padding: 0 15px 10px;}
}
.cp-special-old-price{display: block; padding-bottom: 2px;}
.cp-special-current-price{font-weight: bold;}
.cp-special-discount-expire{padding-top: 2px;}
.cp-special-loyalty-price{display: block; flex-grow: unset; padding: 10px 0 0; text-align: left;}
.cp-special-btn-addtocart{
	width: auto; min-height: 44px; font-size: 14px; font-weight: normal; margin: 0 30px 15px;

	@media (max-width: @t){font-size: 12px; margin: 0 15px 15px; padding: 0 5px;}
}

.cp-special-empty{
	display: none;

	@media (max-width: @t){display: block; padding-right: 50px;}
}

/*------- catalog -------*/
.page-catalog{
	@media (max-width: @m){
		.main-header{display: block; height: 94px;}
		.sw{width: 100%;}
		.main-content{margin-bottom: 0;}
		.content-section{padding-top: 94px;}
	}
}
.c-header{
	display: flex; align-items: center; margin-bottom: 20px;

	@media (max-width: @t){display: block;}
	@media (max-width: @m){display: flex; margin: 0;}
}
.c-row{
	display: flex;

	@media (max-width: @t){display: block;}
}
.c-items-section{
	flex-grow: 1;

	@media (max-width: @m){background: rgba(204,216,226,.2); padding: 15px 15px 25px;}
}
.c-title{
	font-size: 32px; line-height: 1.4; letter-spacing: -0.5px; font-weight: normal; margin-right: 30px; padding: 0; flex-grow: 1;

	@media (max-width: @t){padding-bottom: 15px;}
	@media (max-width: @m){display: none;}
}
.c-load-more-container{display: flex; justify-content: flex-end; margin-top: 20px;}
/*------- /catalog -------*/

/*------- filters -------**/
.c-filters-section{
	width: 270px; margin-right: 60px; flex-shrink: 0;

	@media (max-width: @t){width: 100%; margin: 0 0 30px; flex-shrink: unset;}
	@media (max-width: @m){margin: 0;}
}
.cf-btn-container, .confirm-filters{display: none;}

.cf{position: relative;}
.cf-item{
	border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; margin-bottom: -1px;
	::-webkit-scrollbar {-webkit-appearance: none; width: 2px; background: @gray;}
	::-webkit-scrollbar-thumb {background-color: @blue;}
	&.active{
		.cf-item-wrapper{display: block;}
		.toggle-icon:after{opacity: 0;}
	}

	@media (max-width: @m){padding: 0 15px;}
}
.cf-title{
	font-size: 14px; line-height: 1.6; font-weight: 600; color: @blue; padding: 10px 20px 10px 0; cursor: pointer; position: relative; display: flex; align-items: center; text-decoration: none; .transition(all);
	.toggle-icon{
		width: 14px; height: 14px; position: absolute; right: 0; top: 15px;
		&:before,&:after{.pseudo(14px,2px); background: @blue; top: 6px; right: 0; opacity: 1; .transition(opacity);}
		&:after{width: 2px; height: 14px; top: 0; right: 6px;}
	}
}
.cf-item-wrapper{width: calc(~"100% - -7px"); padding-right: 6px; list-style: none; display: none;}
.cf-row{
	margin-bottom: 8px; position: relative;
	input[type=checkbox]+label{width: 100%; padding-right: 20px;}
	input[type=checkbox]:checked+label{color: @blue;}

	&.level-1,&.level-2{display: none;}
}
.cf-row-not-available{
	color: #ccc;
	input[type=checkbox]+label{
		pointer-events: none;
		&:hover{color: #ccc;}
	}
}
.cf-row:last-child{margin-bottom: 13px;}
.cf-row-expand{
	display: none;
	&.active{display: block;}
}
.cf-counter{font-size: 12px; line-height: 1.75; color: @textColor; opacity: 0.6; position: absolute; right: 0;}
.btn-cf-expand{
	display: none; font-size: 14px; color: @blue; text-decoration: underline; margin: 5px 0 15px;
	&.active{display: block;}
}

.cf-label-color{
	padding: 4px 0 4px 32px; cursor: pointer; min-height: 24px; line-height: 20px; font-size: 14px; text-align: left;
	&:before{content: none;}
}
.cf-color-img{position: absolute; left: -3px; top: -2px; display: flex; align-items: center; justify-content: center; width: 26px; height: 26px; background: @white;}

.cf-item-a_barva-48, .cf-item-a_barva-pim-6698538{
	.cf-row{
		input[type=checkbox]:checked+label{
			.cf-color-img{border: 1px solid #013D70;}
		}
	}
	&.active{
		.cf-item-wrapper{display: block; overflow: inherit;}
	}
}

.cp-rate{
	display: inline-block; height: 14px; margin-right: 2px; padding: 0; font-size: 0!important; position: relative; top: 2px;
	span{
		display: inline-block; position: relative; width: 15px; height: 15px; margin-right: 3px; opacity: 1!important;
		&:after{.pseudo(15px,15px); background: url(images/icons/star.svg) no-repeat; background-size: contain; top: 0; left: 0;}
		&.icon-star:after{background: url(images/icons/star-yellow.svg) no-repeat; background-size: contain; z-index: 1;}
	}

	@media (max-width: @m){
		height: 15px; display: inline-flex; align-items: center; position: relative; z-index: 1;
		span{
			width: 14px; height: 14px; margin-right: 3px;
			&:after{width: 14px; height: 14px;}
		}
	}
}

.cf-range{
	display: flex; flex-flow: column; position: relative; width: 100%; height: auto; overflow: hidden;
	.slider{position: relative; display: block; margin: 10px auto 20px; overflow: initial;}
}
.searchfield-slider_input{
	display: flex; align-items: center; justify-content: space-between;
	input{height: 44px; flex-grow: 1; width: 50%; font-size: 14px; font-weight: bold; text-align: center;}
}
.searchfield-slider_input_separator{width: 11px; height: 2px; background: @borderColor; margin: 0 11px; font-size: 0; line-height: 44px;}

.ci-categories{
	border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; margin-bottom: -1px; position: relative;
	&.active{
		.ci-title>.toggle-icon:after{opacity: 0;}
		.cf-item-wrapper{display: block;}
	}

	@media (max-width: @m){
		border: none; background: #000; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); margin: 0;
		&.active{
			.ci-title .toggle-icon{
				background: transparent;
				&:before{.rotate(-45deg); top: 0;}
				&:after{.rotate(45deg); top: 0; opacity: 1;}
			}
			.cf-item-wrapper{opacity: 1; padding-bottom: 80px;}
		}
	}
}
.ci-title{
	.toggle-icon{position: absolute; right: 0; top: 50%; transform: translateY(-50%);}

	@media (max-width: @m){
		display: none; height: 47px; color: @white; padding: 13px 15px 12px 45px; font-weight: normal;
		.toggle-icon{
			width: 18px; height: 2px; background: @white; left: 15px; right: unset; .transition(all);
			&:after, &:before{content: ""; display: block; position: absolute; width: 18px; height: 2px; background: @white; right: 0; top: 0; .transition(all);}
			&:before{top: -6px;}
			&:after{top: 6px;}
		}
	}
}
.ci-title-m{
	display: none;

	@media (max-width: @m){display: block; white-space: nowrap;}
}
.ci-item-wrapper{
	&>li{
		width: 100%; padding: 5px 0 10px;
		&>a{
			display: block; cursor: pointer; padding-right: 30px; font-size: 14px; letter-spacing: -0.3px; position: relative; .transition(color);
			&:hover{color: @blue;}
			.toggle-icon{display: none; position: absolute; right: 0; top: 0;}
		}
		&.has-children>a .toggle-icon{display: flex;}
		&.active{
			&>a{
				color: @blue;
				.toggle-icon:after{opacity: 0;}
			}
			&>ul{max-height: 5000px; padding: 10px 0 0 10px;}
		}

		&>ul{
			padding-left: 10px; max-height: 0; overflow: hidden; transition: max-height 0.3s, padding 0.3s;
			&>li{
				display: block; padding-bottom: 7px;
				a{
					display: block; font-size: 14px; letter-spacing: -0.3px; padding-right: 30px; position: relative; .transition(color);
					&:hover{text-decoration: underline; color: @blue;}
					&.active{color: @blue;}
					.ci-counter{font-size: 12px; line-height: 1.4; letter-spacing: -0.3px; color: rgba(51,51,51,0.5); position: absolute; right: 0; top: 2px;}
				}
			}
		}
	}

	@media (max-width: @m){
		width: 100%; padding: 0; background: @white; position: fixed; top: 47px; bottom: 0; left: 0; right: 0; z-index: 1111; opacity: 0; .transition(opacity);
		&>li{
			display: block; border-bottom: 1px solid @borderColor; padding: 0; margin: 0 0 -1px;
			&>a{
				padding: 14px 15px;
				&:hover{color: @textColor;}
				.toggle-icon{right: 15px; top: 50%; transform: translateY(-50%);}
			}
			&.active{
				&>a:hover{color: @blue;}
				&>ul{padding: 0 15px 10px;}
			}
			&>ul{
				padding: 0 15px;
				&>li>a{font-size: 13px;}
			}
		}
	}
}
/*------- /filters -------*/

/*------- range slider -------*/
#searchfield-price-slider_numbers { 
	font-size: 14px; text-transform: uppercase; color:#fff; height: 20px; line-height: 22px; position:relative; 
	span{display: block; position:absolute; top:0; left:0; margin-left:-22px; width: 45px; text-align: center;}
	.r0{left:-5px; margin-left: 0; text-align: left; display: block; color: @blue;}
	@iterations: 99;
	.r-loop (@i) when (@i > 0) {
	    .r@{i} {
	        left: ~"@{i}%";
	    }
	    .r-loop(@i - 1);
	}
	.r-loop (@iterations);
	.r100{left:auto; right:-30px; width:auto; color: @blue;}
	abbr{text-transform: none; padding-left: 2px;}
}

.range-slider-quadrature{padding: 0 6px 10px; width: 100%;}
.ui-slider{width: 100%; position: relative; height: 5px; background: @borderColor!important; margin: 0; min-height: inherit!important;}
.ui-slider-range{background: @blue; height: 5px; position: absolute; z-index: 1; display: block;}
.ui-slider-handle{
	background: @blue; width: 5px; height: 17px; position: absolute; z-index: 2; top: -6px; margin-left: 0;
	&:last-of-type{margin-left: -5px;}
}
/*------- /range slider -------*/

/*------- catalog detail -------*/
.cd-subtitle{
	display: block; font-size: 18px; line-height: 1.4; font-weight: 600; letter-spacing: -0.3px; padding-bottom: 15px;

	@media (max-width: @l){font-size: 16px; padding-bottom: 0;}
	@media (max-width: @m){font-size: 15px;}
}

.cd-col2-box{
	padding: 20px 40px; background: @white; border: 1px solid @borderColor; border-radius: @borderRadius;

	@media (max-width: @l){padding: 20px;}
	@media (max-width: @t){padding: 25px;}
	@media (max-width: @m){padding: 15px 20px;}
}

.cd-extra-benefits{
	display: block; margin-top: 25px; padding-bottom: 10px;

	@media (max-width: @t){margin: 0 0 20px; padding-bottom: 15px;}
	@media (max-width: @m){margin-bottom: 15px; padding-bottom: 10px;}
}
.cd-extra-benefit-item{
	width: 100%; border-top: 1px solid @borderColor;
	&.no-border{border-top: none;}
	&.active{
		.cd-extra-benefit-desc{max-height: 2500px; padding-bottom: 15px;}
		.cd-extra-benefit-icon:after{opacity: 0;}
	}
}
.cd-extra-benefits-header{
	margin-top: 25px;
	&:first-child{margin-top: 0;}

	@media (max-width: @m){margin-top: 20px;}
}
.cd-extra-benefits-title{padding-bottom: 5px;}
.cd-extra-benefit-row{
	display: flex; align-items: center; width: 100%; min-height: 45px; padding: 10px 0;

	@media (max-width: @m){min-height: 35px;}
}
.cd-extra-benefit{
	display: flex; align-items: center; padding: 0 0 0 32px!important; position: relative;
	&:before{width: 20px!important; height: 20px!important; top: unset!important; left: 0!important; right: unset!important; background: @white; font: 11px/22px @fonti!important;}
}
.cd-extra-benefit-title{
	font-size: 14px; line-height: 1.2; letter-spacing: -0.2px; position: relative; display: block;

	@media (max-width: @m){font-size: 12px;}
}
.cd-extra-benefit-icon{
	display: flex; align-items: center; justify-content: center; width: 12px; height: 12px; margin-left: 17px; position: relative; cursor: pointer;
	&:before,&:after{.pseudo(12px,2px); background: @blue; top: 5px; right: 0; .transition(opacity);}
	&:after{width: 2px; height: 12px; right: 5px; top: 0;}
}
.cd-extra-benefit-price{
	flex-grow: 1; flex-shrink: 0; text-align: right; font-weight: bold; font-size: 14px; line-height: 1.6; padding-left: 15px;

	@media (max-width: @t){font-size: 14px;}
	@media (max-width: @m){font-size: 12px;}
}
.cd-extra-benefit-desc{
	padding: 0 29px 0 32px; font-size: 12px; line-height: 1.5; font-weight: normal; color: @textColor; overflow: hidden; max-height: 0; transition: max-height 0.3s, padding 0.3s;
	a{color: @blue; text-decoration: underline;}
	p{padding-bottom: 0;}

	@media (max-width: @m){padding-right: 0;}
}
/*------- /catalog detail -------*/

/*------- catalog zoom gallery -------*/
.fancybox-active{position: fixed; width: 100%; overflow: hidden;}
.gallery{overflow: hidden;}
.ratioHolder{width:100%; padding-bottom:66.666667%; position:relative;}
.sliderHolder{position: absolute; top: 0px; left: 0px;}
.superCaption{padding:5px;}
.sliderHolder{width:100%; min-height:100%; height:100%; position:relative; overflow:hidden; -ms-touch-action:none; touch-action:none;}
.slider{overflow:hidden; position:absolute; top:0px; left:0px; width:100%; min-height:100%; background: #fff; }
.sliderBg{width:100%; min-height:100%; height:100%; position:absolute; left:0px; top:0px;}
.slides, .thumbs{overflow:hidden; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;}
.slides{width: 100%; min-height: 90%; position: absolute; margin-right: 80px; top: 50%; transform: translateY(-50%);}

.fancybox-gallery{
	.fancybox-iframe{padding: 0!important;}
	.fancybox-skin{border-radius: 0!important; background: @white!important;}
	.fancybox-close{top: 40px; right: 40px;}
}
.gsControl{ 
	position: absolute; right: 10%; display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; top: 50%; transform: translateY(-50%); .transition(all);
	&:before{.icon-arrow-down; font: 40px/40px @fonti; color: @black; .rotate(-90deg); .transition(color);}
	@media (min-width: @t){
		&:hover:before{color: @blue;}
	}

	@media (max-width: @t){right: 5%;}
}
.gsPrev{
	right: auto; left: 10%;
	&:before{.rotate(90deg);}

	@media (max-width: @t){left: 5%;}
}
.zopim.fancybox-margin{display: none!important;}
/*------- /catalog zoom gallery -------*/

/*------- catalog modal -------*/
.fancybox-cart-modal{z-index: 1111;}
.fancybox-modal{
	@media (max-width: @fancyboxM){
		.fancybox-close{
			top: 6px; right: 8px; border: none; background: transparent; box-shadow: none;
			&:before{font-size: 18px; line-height: 18px;}
		}
	}
}

.cm{
	display: flex; flex-grow: 1; position: relative;
	
	@media (max-width: @fancyboxM){flex-direction: column-reverse; padding: 63px 15px 135px;}
}
.cm-col1{
	width: 54%; flex-shrink: 0; border-right: 1px solid @borderColor; padding: 40px 20px 0 50px;

	@media (max-width: @fancyboxT){padding: 30px 15px 30px 40px;}
	@media (max-width: @fancyboxM){width: 100%; flex-shrink: unset; border: none; padding: 30px 0 0;}
	
}
.cm-bought-related-title{
	font-size: 22px; line-height: 1.4; letter-spacing: -0.4px; font-weight: 600; color: @blue; padding: 0 0 15px;

	@media (max-width: @fancyboxT){font-size: 18px; letter-spacing: -0.3px;}
	@media (max-width: @fancyboxM){font-stretch: 16px; padding-bottom: 12px;}
}
.cm-items-list{
	max-height: calc(~"100vh - 85px"); overflow-y: auto; overflow-x: hidden; padding-right: 20px;
	.cd-bought-together-item:last-child{margin-bottom: 40px;}
	&::-webkit-scrollbar{width: 5px;}
	&::-webkit-scrollbar-thumb, &::-webkit-scrollbar-track {border-radius: 3px;}
	&::-webkit-scrollbar-track-piece{margin-bottom: 40px;}
	&::-webkit-scrollbar-track{border-bottom: 40px solid @white;}

	@media (max-width: @fancyboxT){
		max-height: unset;
		.cd-bought-together-item:last-child{margin-bottom: 0;}
		&::-webkit-scrollbar-track-piece{margin-bottom: 30px;}
		&::-webkit-scrollbar-track{border-bottom: 30px solid @white;}
	}
	@media (max-width: @fancyboxM){padding-right: 0;}
}

.cm-col2{
	flex-grow: 1; padding: 40px 70px;

	@media (max-width: @fancyboxT){padding: 30px 50px;}
	@media (max-width: @fancyboxM){padding: 0;}
}
.cm-message{
	display: block; font-size: 22px; line-height: 1.4; letter-spacing: -0.4px; font-weight: 600; color: @green; position: relative; margin-bottom: 22px;
	&:before{.icon-check; font: 17px/17px @fonti; font-weight: bold; color: @green; position: absolute; left: -30px; top: 5px;}

	@media (max-width: @fancyboxT){
		font-size: 20px; margin-bottom: 15px;
		&:before{font-size: 16px; line-height: 16px;}
	}
	@media (max-width: @fancyboxM){
		position: fixed; top: 0; left: 0; right: 0; font-size: 14px; color: @white; margin: 0; padding: 14px 40px; background: linear-gradient(225deg, #01AABA 0%, #0092A0 100%); z-index: 111;
		&:before{font-size: 15px; line-height: 15px; font-weight: normal; color: @white; left: 15px; top: 16px;}
	}
}
.cm-title{
	font-size: 16px; line-height: 1.3; font-weight: normal; padding: 0;

	@media (max-width: @fancyboxT){font-size: 14px;}
}
.cm-code{display: block; font-size: 12px; line-height: 1.4; padding-top: 5px;}
.cm-price{margin-top: 10px;}
.cm-price-cnt{
	display: flex; align-items: center;
	.cd-old-price{margin: 0 5px 0 0;}
	.cd-current-price{font-size: 16px;}

	@media (max-width: @fancyboxM){
		.cd-current-price{font-size: 12px;}
	}
}

.cm-subtitle{display: block; font-size: 16px; line-height: 1.4; letter-spacing: -0.3px; color: @blue; font-weight: 600; padding-bottom: 10px;}
.cm-extra-benefits{
	margin: 30px 0 0;

	@media (max-width: @fancyboxT){margin-top: 25px;}
	@media (max-width: @fancyboxM){margin-top: 20px;}
}
.cm-extra-benefit-item{
	padding: 10px 0; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; margin-bottom: -1px; font-size: 14px;

	@media (max-width: @fancyboxM){padding: 0 0 10px; border: none; margin: 0; font-size: 12px;}
}

.cm-bt-category{
	display: block; margin: 30px 0 0; position: relative;

	@media (max-width: @fancyboxT){margin-top: 25px;}
	@media (max-width: @fancyboxM){margin-top: 20px;}
}

.cm-btn-container{
	display: flex; justify-content: flex-end; margin-top: 35px;

	@media (max-width: @fancyboxT){margin-top: 30px;}
	@media (max-width: @fancyboxM){display: inline-block; position: fixed; bottom: 70px; right: 15px; margin: 0; z-index: 11;}
}
.cm-btn-add{
	min-width: 180px; min-height: 54px; padding: 0 35px; border-radius: 27px; font-size: 14px; line-height: 1.4; font-weight: bold;
	span{
		padding-right: 20px; position: relative;
		&:before{.icon-arrow; font: 9px/9px @fonti; color: @white; position: absolute; right: 0; top: 4px; .rotate(180deg); .transition(right);}
	}
	&:hover span:before{right: -5px;}

	@media (max-width: @fancyboxM){
		min-width: 120px; min-height: 44px; padding: 0 20px; border-radius: 22px; font-size: 12px;
		span{
			padding-right: 30px;
			&:before{top: 3px;}
		}
		&:hover span:before{right: 0;}
	}
}
/*------- /catalog modal -------*/

/*------- manufacturers -------*/
.m-header{
	@media (max-width: @m){padding: 15px 15px 0;}
}
.m-items{
	position: relative;

	@media (max-width: @m){padding: 20px 0;}
}
.m-column{
	display: block; width: 100%; margin-bottom: 90px;

	@media (max-width: @t){margin-bottom: 80px;}
	@media (max-width: @m){
		margin: 0 0 -1px; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor;
		&.active{
			.m-letter:after{opacity: 0;}
			.m-list-section{max-height: 10000px; overflow: hidden;}
			.m-list{padding-bottom: 15px;}
		}
	}
}
.m-letter{
	font-size: 36px; line-height: 1.4; font-weight: 300; color: #232323; position: relative; display: flex; align-items: center;
	&:before{.pseudo(auto;1px); background: #D9E4EB; left: 66px; right: 0;}
	span{display: block;}

	@media (max-width: @m){
		font-size: 18px; line-height: 24px; padding: 10px 15px; position: relative;
		&:before, &:after{.pseudo(14px,2px); background: @blue; top: 21px; right: 15px; left: unset; opacity: 1; .transition(opacity);}
		&:after{width: 2px; height: 14px; top: 15px; right: 21px;}
	}
}
.m-list-section{
	display: flex; margin-top: 17px;

	@media (max-width: @m){margin: 0; padding: 0 15px; max-height: 0; overflow: hidden; .transition(max-height);}
}
.m-list{
	width: 100%; display: block; column-count: 5; list-style: none;
	&>li{
		padding: 0 15px 3px 0;
		&>a{
			font-size: 14px; line-height: 1.4; color: #232323; text-decoration: none;
			&:hover{text-decoration: underline; color: @blue;}
		}
	}

	@media (max-width: @t){
		column-count: 3;
		&>li{
			padding: 0 15px 2px 0;
			&>a{
				font-size: 12px;
				&:hover{text-decoration: none; color: @textColor;}
			}
		}
	}
	@media (max-width: @m){
		display: block; column-count: 2;
		&>li{
			padding: 0 10px 3px 0; width: auto;
			&>a{font-size: 12px; line-height: 1.4;}
		}
	}
}
/*------- /manufacturers -------*/

/*------- compare -------*/
.page-compare .content-section{
	@media (max-width: @t){padding-right: 0;}
}
.t-compare-attributes, .compare-pager, .c-compare-m-btns{display: none;}
.compare-cnt{
	position: relative; padding: 10px 0 0;

	@media (max-width: @t){
		background: @white;
		&>.wrapper{
			margin: 0; position: relative;
			&:after{.pseudo(40px,auto); left: calc(~"47% - 40px"); top: 2%; bottom: 2%; box-shadow: 0 15px 35px 0 rgba(24,6,3,0.2); opacity: 0; z-index: 1; .transition(opacity);}
			&.active{
				&:after{opacity: 1;}
			}
		}
	}
	@media (max-width: @m){padding: 20px 0 15px 15px;}
}
.wrapper-compare{position: relative;}
.compare-sidebar-header{
	width: 30%; position: absolute; top: 150px; z-index: 10;

	@media (max-width: @t){width: 47%; background: @white; top: 0; bottom: 0; z-index: 11;}
}
.page-compare-title-container{
	width: 100%; display: flex; align-items: center; justify-content: center; text-align: center; padding-bottom: 55px;

	@media (max-width: @t){padding: 0; position: absolute; top: 90px; writing-mode: vertical-lr; .rotate(-180deg);}
}
.page-compare-title{
	display: block; font-size: 30px; line-height: 1.3; letter-spacing: -0.5px; font-weight: normal; color: @textColor; padding: 0;
	&:before{.icon-compare; font: 40px/40px @fonti; color: @blue; position: absolute; bottom: 0; left: 50%; transform: translateX(-50%);}

	@media (max-width: @t){
		font-size: 28px; flex-shrink: 0;
		&:before{content: none;}
	}
	@media (max-width: @m){font-size: 22px;}
}
.c-compare-items{
	margin-left: 30%; position: relative; display: flex; z-index: 1; min-height: 480px;
	.cp-special{
		border-top: none;
		&:after, &:before{content: none!important;}
	}

	@media (max-width: @t){
		margin-left: 47%; min-height: 450px; overflow-y: hidden; overflow-x: auto; -webkit-overflow-scrolling: touch; white-space: normal;
		&::-webkit-scrollbar {display: none;}
	}
}

.cp-compare-header{
	font-size: 14px; line-height: 1.4; border-bottom: 1px solid @borderColor; border-top: 1px solid @borderColor; min-height: 50px;
	.autocomplete-showall{display: none!important;}
	.autocomplete-container{width: 100%; z-index: 200; top: 50px; position: absolute; left: 0; right: 0; background: @white; box-shadow: 0 0 20px 0 rgba(0,0,0,0.1);}
	.ui-autocomplete { 
		font-size: 12px; text-align: left; list-style: none; height: auto;
		.search-title {font-size: 13px; line-height: 16px; padding-top: 4px;}
		img{opacity: 1;}
		&>li{
			padding: 10px; cursor: pointer; .transition(background);
			&>a{display: flex; color: @textColor; text-decoration: none;}
			.image{display: block; flex-shrink: 0; width: 60px; height: 60px; margin-right: 20px;}
			img{width: auto; height: auto; max-width: 100%; max-height: 100%;}
			.search-col2{display: flex; flex-direction: column;}
			&:hover{background: @gray;}
		}
	}
	input{border: 0; .placeholder(@textColor, @borderColor);}

	@media (max-width: @t){
		min-height: 47px;
		.ui-autocomplete { 
			.search-title{font-size: 11px; line-height: 14px; padding-top: 2px;}
			.search-price{font-size: 11px; padding-top: 3px;}
			&>li .image{width: 50px; height: 50px; margin-right: 10px;}
		}
	}
}
.cp-new-compare{
	border: none!important;
	.cp-compare-header{border: 1px solid @borderColor;}
	&:hover{border: unset!important}
}
.cp-compare-form{
	position: relative; width: 100%; min-height: 50px;

	@media (max-width: @t){min-height: 47px;}
}
.cp-compare-input{
	width: 100%; min-height: 50px; padding-right: 25px; padding-left: 46px; font-size: 14px; background: @white url(images/search.svg) no-repeat; background-size: 16px; background-position: center left 20px;

	@media (max-width: @t){height: 47px; min-height: 47px; padding-left: 35px; padding-right: 15px; font-size: 12px; background-size: 14px; background-position: center left 11px;}
}
.cp-compare-btn{
	width: 46px; height: 50px; min-height: 50px; position: absolute; top: 0; left: 0; padding: 0; background: transparent; display: flex; align-items: center; justify-content: center; margin: 0;
	&:before{.icon-search; font: 16px/16px @fonti; color: @blue; position: absolute; left: 20px; top: 17px;}

	@media (max-width: @t){
		width: 34px; height: 47px; min-height: 47px;
		&:before{font-size: 14px; line-height: 14px; left: 11px;}
	}
}

.table-cp-attributes{
	font-size: 14px; line-height: 1.3; letter-spacing: -0.3px; width: calc(~"100% - -1px"); border-top: 1px solid @borderColor; background: #fff; margin-bottom: -1px;
	.col-attribute-title{display: none;}
	&.active{
		.attr-row{
			td{background: @white;}
			&.active{
				display: table-row; 
				td{background: #ebf5fa; font-weight: 600;}
			}
		}
	}
	tbody{
		tr:nth-child(2n){
			td{background: #f5f7f8;}
		}
		tr.activeRow2{
			td{background: #f5f7f8;}
		}
	}

	td{padding: 12px 30px; border-bottom: 1px solid @borderColor; border-right: 1px solid @borderColor;}

	@media (max-width: @t){
		width: calc(~"100% - -2px"); font-size: 12px;
		td{padding: 8px 15px;}
	}
}
.cp-attributes-compare{
	&.active{
		.attr-row{
			td{background: @white;}
			&.active{
				display: table-row; 
				td{background: #ebf5fa;}
			}
		}
	}
}
.all-attributes-border{margin-top: -1px; border-top: 1px solid @borderColor;}
.c-compare-sidebar-attributes{
	width: 100%; padding-top: 1px; position: absolute; bottom: 0;

	@media (max-width: @t){
		width: 47%; margin: 0 0 0 1px; z-index: 11;
		&:before{content: none;}
	}
}
.table-c-all-attributes{
	margin: 0; width: calc(~"30% - -1px"); text-align: left; border-left: 1px solid @borderColor; margin-top: -1px; font-size: 14px; font-weight: 600; border-collapse: initial;
	td{padding-left: 30px; font-size: 14px;}

	@media (max-width: @t){
		width: 100%; margin: 0 0 -0.5px; font-size: 12px; border-collapse: collapse;
		td{padding-left: 15px; font-size: 12px;}
	}
}
.c-compare-btns-cnt{
	font-size: 0; position: absolute; top: -59px; left: 0; display: flex; width: 30%; z-index: 9;
	
	@media (max-width: @t){width: 100%; top: -46px;}
}
.c-compare-btn{
	text-decoration: none; width: 50%; display: flex; justify-content: center; font-size: 14px; color: @white; font-weight: 600; align-items: center; height: 60px; border: none; border-bottom: none; background: linear-gradient(270deg, #0078B4 0%, #0050A0 100%); padding: 0; .transition(opacity);
	&.active{
		color: @textColor; background: @white; border: 1px solid @borderColor; border-bottom: none;
		&:hover{color: @textColor; text-decoration: none;}
	}
	@media (min-width: @t){
		&:hover{color: @white; opacity: 0.9; text-decoration: none;}
	}

	@media (max-width: @t){
		font-size: 11px; height: 47px;
		&:hover{color: @white;}
		&.active:hover{color: @textColor;}
	}
}
/*------- /compare -------*/

/*------- cart -------*/
.flyout-active{
	overflow: hidden; position: relative;
	&:before{.pseudo(auto,auto); position: fixed; top: 0; bottom: 0; left: 0; right: 0; background: rgba(0,24,47,0.6); z-index: 11111;}
}

.page-cart{
	.content-section{padding-bottom: 15%;}
	.sidebar-alert-message:before{.pseudo(auto,auto); position: fixed; top: 65px; right: 0; bottom: 0; left: 21%; z-index: 1;}

	@media (max-width: @m){
		min-height: 100vh; background:#f2f5f7;
		.content-section{padding-bottom: 65px;}
		.sidebar-nav>.ww>a{
			background: @white; border-top: 1px solid @borderColor; color: @textColor; z-index: 1;
			&:after{color: @blue;}
			.ww-counter{color: @blue;}
		}
	}
}
.cart-active-item .btn-float{display: none;}
.w-cart-content{
	display: none;

	@media (max-width: @m){border-bottom: 1px solid @borderColor;}
}
.w-cart-content-title{
	display: block; padding: 25px 25px 15px; font-size: 18px; line-height: 1.4; letter-spacing: -0.03px; font-weight: 600;

	@media (max-width: @m){padding: 15px 15px 10px; font-size: 14px;}
}
.w-cart-group{
	display: flex; align-items: center; width: 100%; padding: 15px 20px 15px 25px; background: #F6F7F8; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor; line-height: 1.4; position: relative;
	&:before{.pseudo(8px,8px); background: @white; border-top: 1px solid @borderColor; border-right: 1px solid @borderColor; position: absolute; bottom: -5px; left: 31px; .rotate(-45deg);}

	@media (max-width: @t){padding: 15px 25px;}
	@media (max-width: @m){
		padding: 13px 15px;
		&:before{left: 18px;}
	}
	@media (max-width: @ms){align-items: flex-start; flex-wrap: wrap;}
}
.w-cart-group-pickup{
	align-items: center; width: auto; height: 40px; margin: -1px 25px 0; padding: 5px 15px; background: #e5f4f5; border: none; border-radius: @borderRadius; color: @green; z-index: 1;
	&:before{content: none;}
	.w-cart-group-title{
		padding-left: 20px; font-size: 12px; letter-spacing: -0.2px; font-weight: bold; color: @green; cursor: default; text-decoration: none; pointer-events: none; position: relative;
		&:before{.icon-check; font: 10px/10px @fonti; color: @green; position: absolute; left: 0; top: 3px;}
	}

	@media (max-width: @m){height: 30px; margin: -1px 15px 0; padding: 5px 12px;}
}
.w-cart-group-warehouse-pickup{
	background: #e8f3f9; color: @blue;
	.w-cart-group-title{
		color: @blue;
		&:before{.icon-trolley-cart; font: 18px/18px @fonti; color: @blue; position: absolute; left: 0; top: -2px;}
	}

	@media (max-width: @m){
		.w-cart-group-title:before{font-size: 16px; line-height: 16px; top: -1px;}
	}
}
.w-cart-group-title{
	font-size: 18px; font-weight: 600; letter-spacing: -0.3px; cursor: pointer; text-decoration: underline; text-underline-offset: 2px;
	@media (min-width: @t){
		&:hover{text-decoration: none;}
	}

	@media (max-width: @m){font-size: 14px;}
	@media (max-width: @ms){flex-grow: 1;}
}
.w-cart-group-badge{
	display: flex; align-items: center; justify-content: center; min-height: 30px; margin-left: 12px; padding: 6px 15px 5px; background: linear-gradient(270deg, #00B0DE 0%, #0085CF 100%); border-radius: 15px; font-size: 12px; line-height: 1.3; letter-spacing: -0.3px; font-weight: bold; color: @white;
	&.badge2{background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%);}
	&.badge3{background: linear-gradient(270deg, #F4BB0A 1.13%, #E7AF07 100%);}

	@media (max-width: @ms){display: none;}
}
.w-cart-group-col2{
	flex-grow: 1;

	@media (max-width: @m){text-align: right;}
}
.w-cart-group-date{
	font-size: 16px; font-weight: 600; letter-spacing: -0.3px; text-align: right;

	@media (max-width: @m){font-size: 14px;}
}
.w-cart-group-address{
	display: block; margin-top: 3px; font-size: 12px; line-height: 1.4; letter-spacing: -0.3px; text-align: right; position: relative;
	span{opacity: 0.8;}

	@media (max-width: @ms){
		display: inline-block; margin-top: 4px; padding-left: 16px;
		&:before{.pseudo(10px,10px); background: linear-gradient(270deg, #00B0DE 0%, #0085CF 100%); border-radius: @borderRadius; left: 0; top: 2px;}
		&.badge2:before{background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%);}
		&.badge3:before{background: linear-gradient(270deg, #F4BB0A 1.13%, #E7AF07 100%);}
	}
}
.w-cart-total-shipping{
	margin-left: 80px; padding: 15px 25px 30px; font-size: 14px; line-height: 1.3; letter-spacing: -0.28px; text-align: right;
	.w-cart-totals-label{
		padding-left: 34px; position: relative;
		&:before{.icon-delivery; font: 20px/1 @fonti; color: @textColor; position: absolute; top: -2px; left: 0;}
	}

	@media (max-width: @m){
		padding: 15px 13px 20px; font-size: 12px;
		.w-cart-totals-label{
			padding-left: 30px;
			&:before{font-size: 17px;}
		}
	}
}
.w-cart-totals-value{
	cursor: pointer;
	.price{text-decoration: underline;}
	.green{text-decoration: underline; text-decoration-color: @green; -webkit-text-decoration-color: @green;}
}
.w-shipping-price-edit{
	display: inline-flex; align-items: center; justify-content: center; flex-shrink: 0; width: 10px; height: 10px; margin-right: 5px; margin-left: 10px; background: @white; font-size: 0; line-height: 0; cursor: pointer;
	&:before{.icon-edit; font: 12px/1 @fonti; color: @textColor; font-weight: bold; position: absolute; z-index: 1;}
	&:hover:before{color: @blue;}
}
.w-cart-content-bottom{
	display: flex; padding: 25px 25px 30px 25px; border-top: 1px solid @borderColor;

	@media (max-width: @m){display: none; width: 100%; padding: 15px; order: 3;}
}
.w-cart-checkbox{
	padding-bottom: 15px;
	&:last-child{padding-bottom: 0;}

	@media (max-width: @t){padding-bottom: 15px;}
	@media (max-width: @m){
		padding-bottom: 12px;
		input[type=checkbox]+label{font-size: 12px; min-height: 16px; padding: 1px 0 0 28px;}
		input[type=checkbox]+label:before{width: 14px; height: 14px; font-size: 7px; line-height: 16px; text-indent: 0;}
	}
}

.wp{
	padding: 15px 25px; position: relative;
	&:before{.pseudo(auto,1px); background: @borderColor; left: 80px; right: 25px; bottom: -1px; z-index: 1;}
	&:last-child:before{content: none;}

	@media (max-width: @t){padding: 15px 25px;}
	@media (max-width: @m){
		border: none; padding: 15px;
		&:before{left: 55px; right: 15px;}
		&.active-item-details .wp-row2{display: block; padding-top: 15px;}
	}
}
.wp-row1{display: flex; align-items: flex-start; flex-grow: 1;}
.wp-order-numb{display: none;}
.wp-check-button{
	width: 35px; flex-shrink: 0;
	input[type=checkbox]:checked+label:before{background: rgba(0,120,186,0.1); color: @blue;}

	@media (max-width: @m){
		width: 24px;
		input[type=checkbox]+label{padding-left: 18px; min-height: 16px;}
		input[type=checkbox]+label:before{width: 14px; height: 14px; line-height: 16px;}
		input[type=checkbox]:checked+label:before{line-height: 16px; text-indent: 0;}
	}
}
.wp-warehouse-qty{
	width: 20px; flex-shrink: 0;

	@media (max-width: @m){width: 16px;}
}
.wp-warehouse-qty-box{
	display: block; width: 12px; height: 12px; border-radius: @borderRadius; background: @green2; position: relative; top: 5px;
	&.supplier{background: #F08747;}
	&.not-available-in-store{background: #F5B800;}
	&.not-available{background: #ADBAC4;}
	&.preorder{background: @blue;}

	@media (max-width: @m){width: 10px; height: 10px; top: 4px;}
}
.wp-cnt{
	font-size: 14px; line-height: 1.4; letter-spacing: -0.3px; flex-grow: 1; padding: 3px 65px 0 0;

	@media (max-width: @m){font-size: 12px; letter-spacing: -0.2px; padding: 2px 15px 0 0;}
}
.w-cart-package-select-all{
	display: flex; align-items: center; justify-content: center; width: 22px; height: 22px; margin-right: 15px; background: @white; border: 1px solid @gray; border-radius: @borderRadius; cursor: pointer; .transition(all);
	&:before{.icon-check; font: 10px/22px @fonti; color: @white; position: absolute; .transition(all);}
	&.active{
		background: rgba(0,120,186,0.1); border-color: @blue;
		&:before{color: @blue;}
	}

	@media (max-width: @m){
		width: 16px; height: 16px; margin-right: 10px;
		&:before{font-size: 8px; line-height: 14px;}
	}
}
.wp-title{
	display: inline-flex; align-items: center; font-size: 14px; line-height: 1.4; font-weight: normal; letter-spacing: -0.3px; padding: 0; position: relative;
	@media (min-width: @t){
		a:hover{text-decoration: underline; color: @blue;}
	}

	.barcode{
		width: 16px; margin-left: 10px; top: 2px; display: inline-flex; align-items: center; justify-content: center; flex-shrink: 0; position: relative;
		&:before{.icon-barcode; font: 16px/16px @fonti; color: @blue; position: absolute; top: -15px;}
	}
	.preorder-date{color: @blue; font-weight: bold;}

	@media (max-width: @m){
		font-size: 12px; letter-spacing: -0.2px;
		.barcode{
			empty-cells: 12px; margin-left: 5px;
			&:before{font-size: 12px; line-height: 12px; top: -12px;}
		}
	}
}
.wp-title-edit-container{display: flex; align-items: center; position: absolute; left: calc(~"100% - -10px");}
.wp-title-edit-btn{display: flex; align-items: center; justify-content: center; flex-shrink: 0; width: 30px; height: 30px; background: @white; border: 1px solid @blue; border-radius: 100%; font-size: 0; line-height: 0; cursor: pointer;}
.wp-title-edit{
	&:before{.icon-edit; font: 12px/1 @fonti; color: @blue; font-weight: bold; position: absolute; z-index: 1;}
	&:hover{
		background: @blue;
		&:before{color: white;}
	}
}
.wp-title-remove{
	margin-left: 5px; border: none; background: #eaf1f7;
	&:before{.icon-x; font: 10px/1 @fonti; color: @black; font-weight: bold; position: absolute; z-index: 1;}
	&:hover{
		background: @red;
		&:before{color: white;}
	}
}
.wp-codes{display: flex; align-items: center; flex-wrap: wrap; margin-top: 10px;}
.wp-code{
	display: block; font-size: 12px; line-height: 1.3; letter-spacing: -0.2px; margin-right: 8px;

	@media (max-width: @m){font-size: 11px;}
}
.wp-seller{
	margin: 0 7px 0 0; padding-left: 8px; font-size: 12px; line-height: 1.3; font-weight: bold; letter-spacing: -0.2px; color: @blue; position: relative;
	&:before{.pseudo(1px,10px); background: #DBE3EA; top: 2px; left: 0;}

	@media (max-width: @m){font-size: 11px;}
}
.wp-condition{
	display: inline-flex; align-items: center; justify-content: center; min-height: 22px; margin: 0 8px 0 0; padding: 2px 10px; background: #CDD700; border-radius: 11px; font-size: 11px; line-height: 1.3; color: #0050A0; font-weight: 600;
		
	@media (max-width: @ms){min-height: 20px; margin: 0 5px 0 0; padding: 2px 8px;}
}
.wp-ean{
	display: flex; align-items: center; min-height: 28px; flex-shrink: 0; padding: 5px 10px; background: @white; border: 1px solid @borderColor; border-radius: @borderRadius; font-size: 12px; line-height: 1.3; color: @textColor; position: relative; .transition(all); z-index: 11;
	span{
		padding-left: 20px; position: relative;
		&:before{.icon-eancode; font: 10px/10px @fonti; color: @black; position: absolute; top: 2px; left: 0;}
	}
	&.longer{
		padding-right: 22px; cursor: pointer;
		&:before{.icon-arrow-down; font: 7px/7px @fonti; color: @blue; position: absolute; top: 9px; right: 8px; .transition(all);}
		&.active{
			background: #f8f9fa; border-color: @blue;
			&:before{.rotate(180deg);}
			.wp-ean-codes{max-height: 1000px; overflow: unset;}
		}
		@media (min-width: @t){
			&:hover{border-color: @blue;}
		}
	}

	@media (max-width: @m){
		display: inline-flex; min-height: 24px; margin-top: 5px; margin-right: 8px; padding: 2px 7px; font-size: 11px;
		&.longer:before{font-size: 7px; line-height: 7px; top: 9px; right: 8px;}
	}
}
.wp-ean-codes{
	position: absolute; top: calc(~"100% - -1px"); left: -1px; right: -1px; max-height: 0; overflow: hidden; .transition(all);
	&:before{.pseudo(auto,auto); left: 0; right: 0; bottom: 0; top: -32px; box-shadow: 0 15px 25px 0 rgba(3,32,62,0.1); z-index: -1;}
	.wp-ean{width: 100%; margin: 0; border-top: none; border-radius: 0;}
}
.wp-badge-coupon{
	margin: 0 0 0 8px;

	@media (max-width: @m){margin: 5px 0 0;}
}

.wp-shipping-info{
	font-size: 12px; line-height: 1.4; letter-spacing: -0.2px; color: rgba(51,51,51,0.6); padding-top: 3px;
	&.date{
		padding-left: 18px; position: relative;
		&:before{.icon-calendar; font: 11px/11px @fonti; color: @blue; position: absolute; left: 0; top: 4px;}
	}

	@media (max-width: @m){font-size: 11px;}
}
.wp-shipping-date{
	margin-top: 5px; padding-left: 18px; font-size: 12px; line-height: 1.4; font-weight: 600; position: relative;
	&:before{.icon-calendar; font: 12px/14px @fonti; color: @blue; position: absolute; left: 0;}
}
.w-cart-checkbox-section{
	display: flex; align-items: center; flex-wrap: wrap; margin-top: 10px;
}

.w-cart-checkbox-item{
	display: flex; margin: 0 8px 8px 0; position: relative;
	input[type=checkbox]+label{padding: 8px 16px 8px 41px; background: @white; border: 1px solid @borderColor; border-radius: @borderRadius; font-size: 12px; letter-spacing: -0.24px; font-weight: bold; .transition(all);}
	input[type=checkbox]+label:before{left: 8px; top: 5px;}
	input[type=checkbox]:checked+label{background: rgba(0,120,186,0.1); border-color: @blue; color: @blue;}
	input[type=checkbox]:checked+label:before{background: @white; border-color: @blue; color: @blue;}
	input.field_error_input+label{border-color: @red!important;}

	@media (max-width: @m){
		input[type=checkbox]+label{padding: 3px 0 0 26px; border: none; border-radius: 0; font-size: 11px; letter-spacing: -0.22px;}
		input[type=checkbox]+label:before{width: 16px; height: 16px; font-size: 8px; line-height: 18px; left: 0; top: 0;}
		input[type=checkbox]:checked+label{background: transparent; color: @textColor;}
		input[type=checkbox]:checked+label:before{background: @blue; border-color: @blue; color: @white;}
	}
}
.wp-item-details{
	display: none;

	@media (max-width: @m){display: block; font-size: 11px; letter-spacing: -0.2px; font-weight: bold; text-decoration: underline; padding: 6px 0 0 40px;}
}
.wp-col4{
	display: flex; align-items: center; flex-shrink: 0;

	@media (max-width: @t){flex-direction: row-reverse; flex-shrink: unset; flex-grow: 1;}
	@media (max-width: @m){flex-direction: unset; justify-content: flex-end; margin-top: 15px; padding-left: 40px;}
}
.wp-barcode-scan{
	display: none;
	
	@media (max-width: @m){
		display: flex; align-items: center; justify-content: center; width: 36px; height: 36px; margin-left: 5px; flex-shrink: 0; border-radius: @borderRadius; background: @blue; position: relative; order: 2;
		&:before{.icon-barcode; font: 17px/17px @fonti; color: @white; position: absolute; left: 9px;}
	}
}
.wp-qty{
	width: 110px; height: 44px; flex-shrink: 0; margin-left: 10px; border: 1px solid @borderColor; background: @white; display: flex; align-items: center; justify-content: center; position: relative;
	&.single{display: none;}

	@media (max-width: @t){margin: 0 10px 0 0;}
	@media (max-width: @m){width: 75px; height: 36px; margin: 0 0 0 5px; order: 4;}
}
.wp-input-qty{
	width: 20%; height: 42px; padding: 0; flex-grow: 1; font: 14px/19px @font; border: none; color: @textColor; text-align: center; transition: none;

	@media (max-width: @m){height: 34px; font-size: 12px; line-height: 17px;}
}
.wp-btn-qty{
	width: 38%; height: 100%; flex-shrink: 0; display: flex; align-items: center; justify-content: center; cursor: pointer;
	&:before{.pseudo(12px,2px); display: block; position: absolute; background: @green;}
}
.wp-btn-inc:after{.pseudo(2px,12px); display: block; position: absolute; background: @green;}
.wp-message{
	position: absolute; top: 48px; left: 0; right: 0; background: @white; font-size: 11px; line-height: 15px; color: @green; text-align: center; z-index: 1;
	&.product_message_response_error{color: @red;}

	@media (max-width: @m){top: 40px; left: -10px; right: -10px;}
}
.wp-qty-split{
	display: flex; align-items: center; justify-content: center; width: 34px; height: 44px; background: #F6F7F8; border: 1px solid @borderColor; position: absolute; top: -1px; right: -34px;
	&:before{.icon-split; font: 16px/16px @fonti; color: @blue; position: absolute; .rotate(90deg);}

	@media (max-width: @m){
		height: 36px; background: @white; left: -34px; right: unset;
		&:before{font-size: 14px; line-height: 14px;}
	}
}
.wp-total{
	width: 150px; min-height: 44px; flex-shrink: 0; padding-left: 10px; text-align: right; display: flex; flex-direction: column; justify-content: center;

	@media (max-width: @m){width: 90px; min-height: unset; display: block; padding: 2px 0 0;}
}
.wp-price-current{
	font-size: 14px; line-height: 1.3; letter-spacing: -0.3px; font-weight: bold; color: @textColor;
	&.underline{cursor: pointer;}
	&.red{color: @red;}

	@media (max-width: @m){font-size: 12px; letter-spacing: -0.4px;}
}
.wp-price-salesman{
	margin-top: 3px; font-size: 11px; line-height: 1.4; letter-spacing: -0.22px; font-weight: bold; cursor: pointer;
	span{
		padding-left: 14px; position: relative;
		&:before{.icon-edit; font: 9px/1 @fonti; color: @textColor; font-weight: bold; position: absolute; top: 2px; left: 0; .transition(color);}
	}
	&:hover span:before{color: @blue;}
}
.wp-price-old{
	font-size: 12px; line-height: 1.3; padding-bottom: 2px;

	@media (max-width: @m){font-size: 11px; padding-bottom: 1px;}
}
.wp-qty-count{
	font-size: 12px; line-height: 1.3; padding-top: 4px;

	@media (max-width: @m){font-size: 11px; padding-top: 2px;}
}
.wp-qty-count-tooltip{display: none;}
.wp-price-container{
	position: relative;
	&.active .wp-installments-tooltip{visibility: visible; opacity: 1; max-height: unset;}
}
.wp-installments-tooltip{
	width: 350px; padding: 17px 25px 17px; background: @white; font-size: 13px; line-height: 1.5; text-align: left; font-weight: normal; color: @textColor; position: absolute; left: unset; right: -20px; top: calc(~"100% - -7px"); box-shadow: 0 5px 25px 0 rgba(1,61,112,0.4); border-radius: @borderRadius; z-index: 111; visibility: hidden; opacity: 0; max-height: 0; .transition(opacity);
	a{color: @blue; text-decoration: underline;}
	img{width: auto; height: auto; max-width: 100%; max-height: 100%;}
	&:before{.pseudo(10px,10px); background: @white; right: 18px; top: -3px; .rotate(45deg);}
	&:after{.pseudo(auto,10px); left: 0; right: 0; top: -10px;}

	@media (max-width: @m){
		width: 260px; padding: 15px; font-size: 12px; right: 0;
		&:before{right: 10px;}
	}
}
.wp-installments-tooltip-logo{
	width: 75px; height: 22px; display: block; margin-bottom: 12px;
	img{width: auto; height: auto; max-width: 75px; max-height: 22px; display: block;}
}
.wp-installments-tooltip-title{display: block; font-weight: bold; padding-bottom: 4px;}
.wp-installments-tooltip-table{display: block; margin-bottom: 10px;}

.wp-row2{
	margin-top: 10px;

	@media (max-width: @t){padding-left: 55px;}
	@media (max-width: @m){padding-left: 40px;}
}

.w-cart-bottom{
	display: flex; align-items: center; border-top: 1px solid @borderColor;
	button, .btn{
		min-width: 180px; min-height: 54px; background: @white; border: none; border-left: 1px solid @borderColor; border-radius: 0; font-size: 14px; color: @blue; .transition(all);
		&.disabled{
			color: #b0b0b0; pointer-events: none;
			&:hover{background: @white; color: #b0b0b0; box-shadow: none;}
		}
	}
	.w-cart-reservation:hover{background: @blue; color: @white;}
	.w-btn-finish{
		color: @green;
		&:hover{background: @green; color: @white;}
		&.special{color: #b0b0b0; pointer-events: none;}
	}
	.w-cart-qr-share{
		font-size: 0; line-height: 0; min-width: 54px; position: relative;
		&:before{.icon-trash; font: 20px/20px @fonti; color: @red; .transition(color);}
		&:hover{
			background: @red;
			&:before{color: @white;}
		}
	}
	.w-cart-qr-share{
		&:before{.icon-share; font: 20px/20px @fonti; color: @blue;}
		&:hover{background: @blue;}
	}

	@media (max-width: @t){
		.w-btn-finish:hover{background: @white; color: @green;}
		.w-cart-qr-share:hover{
			background: @white;
			&:before{color: @blue;}
		}
	}
	@media (max-width: @m){
		flex-wrap: wrap; background: #f6f7f8; border-top: none;
		&.inactive{
			padding: 0!important; border: none;
			.w-cart-qr-share{border-top: none;}
		}
		button, .btn{min-width: calc(~"50% - 46px"); min-height: 46px; padding: 0 10px; font-size: 14px; letter-spacing: -0.2px;}
		.w-cart-qr-share{
			min-width: 46px; min-height: 46px; padding: 0; font-size: 0; border-top: 1px solid @borderColor;
			&:before{font-size: 16px; line-height: 16px;}
		}
		.w-cart-qr-share{order: 5;}
		.w-cart-reservation{border-left: 1px solid @borderColor; order: 6;}
		.w-btn-finish{flex-grow: 1; border-top: 1px solid @borderColor; order: 7;}
	}
}
.w-cart-select{
	width: 50%; flex-grow: 1; height: 54px; padding: 0 25px; border: none; font-size: 14px; letter-spacing: -0.3px; position: relative; display: flex; align-items: center;
	&>span{font-weight: bold; .transition(color);}
	&:before{.icon-arrow-down; font: 10px/10px @fonti; color: @blue; position: absolute; right: 25px; top: 23px;}

	@media (max-width: @t){display: none; width: 100%; border-bottom: 1px solid @borderColor;}
	@media (max-width: @m){
		height: 40px; padding: 0 15px; font-size: 12px; letter-spacing: -0.2px;
		&:before{right: 15px; top: 16px;}
	}
}

.w-cart-shipping-bar{
	position: relative;
	&.active-item .w-cart-shipping-box{display: flex;}
}
.w-cart-shipping-box{
	display: none; align-items: center; width: 79%; padding: 15px 130px 15px 5.7%; background: @white; position: fixed; bottom: 0; right: 0; z-index: 111; box-shadow: 0 0 40px 0 rgba(3,32,62,0.2);

	@media (max-width: @t){width: 70%; padding: 15px 4.2%;}
	@media (max-width: @m){width: 100%; padding: 0; left: 0; z-index: 11111; box-shadow: 0 0 20px 0 rgba(3,32,62,0.2)}
}
.w-cart-shipping-choose{
	min-height: 50px; padding: 0 30px; font-size: 14px; line-height: 19px; letter-spacing: -0.17px; font-weight: 600; cursor: pointer;
	span{
		padding-right: 20px; color: rgba(255,255,255,0.5); position: relative;
		&:before{.icon-arrow; font: 7px/1 @fonti; color: @white; position: absolute; right: 0; top: 6px; .rotate(180deg);}
	}

	@media (max-width: @m){flex-grow: 1; border-radius: 0;}
}
.w-cart-parcel-merge, .w-cart-parcel-avans{
	min-height: 50px; margin-left: 15px; padding: 0 25px; border-color: @blue; font-size: 14px; line-height: 19px; letter-spacing: -0.17px; font-weight: 600; color: @blue; cursor: pointer;
	span{
		padding-left: 28px; position: relative;
		&:before{.icon-merge; font: 18px/1 @fonti; color: @blue; position: absolute; left: 0;}
	}

	@media (max-width: @m){
		width: 50px; margin: 0; padding: 0; border: none; border-right: 1px solid @borderColor; border-radius: 0; font-size: 0; line-height: 0; position: relative;
		&:before{.icon-merge; font: 18px/1 @fonti; color: @blue;}
		span{display: none;}
	}
}
.w-cart-parcel-avans{
	span{
		padding: 0;
		&:before{content: none;}
	}

	@media (max-width: @m){
		&:before{content: "A"; font: 15px/1 @font;}
	}
}
.w-cart-shipping-box-right{
	display: flex; align-items: center; justify-content: flex-end; flex-grow: 1;

	@media (max-width: @m){flex-grow: unset;}
}
.w-cart-shipping-remove{
	min-height: 50px; padding: 0 25px; font-size: 14px; line-height: 19px; letter-spacing: -0.17px; cursor: pointer;
	span{
		padding-left: 26px; position: relative;
		&:before{.icon-trash; font: 18px/18px @fonti; color: @red; position: absolute; left: 0;}
	}

	@media (max-width: @m){
		width: 50px; padding: 0; font-size: 0; line-height: 0; border: none; border-radius: 0; position: relative;
		&:before{.icon-trash; font: 16px/16px @fonti; color: @red;}
		span{display: none;}
	}
}
.w-cart-shipping-deselect{
	width: 50px; min-height: 50px; margin-left: 15px; font-size: 0; line-height: 0; cursor: pointer; position: relative;
	&:before{.icon-x; font: 16px/16px @fonti; color: @black;}

	@media (max-width: @m){margin-left: 0; border: none; border-left: 1px solid @borderColor; border-radius: 0;}
}

.field-shipping-row{
	padding-bottom: 10px;
	//select{width: calc(~"100% - 32px"); margin: 7px 0 0 32px;}
	&.special{
		display: flex; align-items: center; justify-content: space-between;
		&>p{width: calc(~"50% - 5px"); padding-bottom: 0;}
		.field-datepicker{background: url(images/calendar.svg) no-repeat; background-size: 16px; background-position: right 20px top 16px; padding-right: 45px;}
	}
	button, .btn{
		width: 100%; min-height: 44px; margin-bottom: 5px; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border: none; color: @white;
		@media (min-width: @t){
			&:hover{color: @white; box-shadow: 0 100px 10px rgba(0,0,0,0.08) inset; text-decoration: none;}
		}
	}
	.autocomplete-container{
		position: absolute; top: 50px; right: 0; left: 0; z-index: 111; box-shadow: 0 5px 20px 0 rgba(0,0,0,0.15);
		::-webkit-scrollbar {-webkit-appearance: none;width: 4px; background: #E0E8EE;}
		::-webkit-scrollbar-thumb {border-radius: 0;background-color: #99AAB8;-webkit-box-shadow: 0 0 1px rgba(255,255,255,.5);}	
		::-webkit-scrollbar-track {border: 1px solid #E0E8EE;}
	}
	.ui-autocomplete{
		overflow: auto; height: auto; max-height: 300px; background: @white; position: relative; font-size: 14px; z-index: 111;
		.ui-menu-item{
			padding: 6px 20px; color: @textColor; border-bottom: 1px solid @borderColor; cursor: pointer; .transition(background);
			&:hover{background: rgba(59,62,77,.05);}
			a{color: @textColor; text-decoration: none;}
		}
		.ui-menu-item:last-child{border-bottom: none;}
	}

	@media (max-width: @m){
		&.special{
			display: block; padding: 0;
			&>p{width: 100%; padding-bottom: 10px;}
			.field-datepicker{background: url(images/calendar.svg) no-repeat; background-size: 16px; background-position: right 15px top 14px; padding-right: 40px;}
		}
	}
}
.field-shipping-subitem{margin: 10px 0 10px 30px;}

.fancybox-remove-cart{
	.fancybox-close{display: none;}

	@media (max-width: @m){top: unset!important; bottom: 65px!important;}
}
.wqr-scan-camera{
	padding: 35px 60px 25px; background: #F6F7F8; border-bottom: 1px solid @borderColor;

	@media (max-width: @iframeMs){padding: 20px;}
}
.wqr-scan-info{
	padding: 0 0 20px 32px; font-size: 16px; font-weight: bold; position: relative;
	&.scan-card:before{.icon-face-scan; font: 22px/22px @fonti; color: @blue; position: absolute; left: 0; top: -2px;}
	&.scan-barcode:before{.icon-barcode; font: 20px/20px @fonti; color: @blue; position: absolute; left: 0;}
	&.scan-qrcode:before{.icon-scan; font: 20px/20px @fonti; color: @blue; position: absolute; left: 0;}

	@media (max-width: @iframeMs){
		padding: 0 0 10px 26px; font-size: 12px;
		&.scan-card:before{font-size: 18px; line-height: 18px; top: -1px;}
		&.scan-barcode:before{font-size: 16px; line-height: 16px;}
		&.scan-qrcode:before{font-size: 16px; line-height: 16px;}
	}
}
.wqr-scan-buttons{display: flex; align-items: center; justify-content: space-between; width: 100%; margin-bottom: 10px;}
.wqr-scan-button{
	width: calc(~"100% / 3 - -3px"); min-height: 45px; margin-left: -1px; font-size: 12px;
	&.active{
		background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border-color: transparent; color: @white;
		&:before{.pseudo(7px,7px); background:#0053a2; bottom: -3px; .rotate(45deg);}
	}

	@media (max-width: @m){font-size: 12px;}
}
.wqr-scan-camera-select{
	display: flex; align-items: center; margin-bottom: 10px;
	label{padding: 0 10px 0 0; flex-shrink: 0;}
	select{width: 100%;}

	@media (max-width: @iframeMs){
		display: block!important;
		label{padding: 0 0 10px;}
	}
}
.wqr-scan-camera-box{display: flex; align-items: center; justify-content: center; background: #000; border-radius: @borderRadius;}
.wqr-scan-content{
	padding: 25px 60px 25px; position: relative;

	@media (max-width: @iframeMs){padding: 10px 20px 20px;}
}
.wqr-scan-form{
	display: flex; flex-wrap: wrap; position: relative;
	input{.placeholder(@textColor, @borderColor);}
}
.wqr-scan-code{
	width: 100%; font-size: 16px; font-weight: bold; padding: 0 0 10px;

	@media (max-width: @iframeMs){font-size: 12px;}
}
.wqr-scan{
	width: 47px; height: 47px; margin: 10px 10px 0 0; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border-radius: @borderRadius; display: flex; align-items: center; justify-content: center; cursor: pointer; .transition(all);
	&:before{.icon-barcode; font: 18px/18px @fonti; color: @white; text-indent: 2px; z-index: 1;}
}
.wqr-scan-send{width: auto; margin-top: 10px; padding: 0 15px; flex-grow: 1;}

.qr-share-row{
	margin-bottom: 20px; font-size: 16px;
	a{
		color: @blue; text-decoration: underline;
		&:hover{text-decoration: none;}
	}
	img{width: auto; height: auto; max-width: 140px; max-height: 100%; display: block;}

	@media (max-width: @iframeMs){
		margin-bottom: 15px; font-size: 12px;
		img{max-width: 100px;}
	}
}
.qr-share-info{
	font-size: 16px; font-weight: bold; padding-bottom: 7px;

	@media (max-width: @iframeMs){font-size: 13px;}
}
.qr-share-input{.placeholder(@textColor, @borderColor);}
.qr-share-btn-send{margin-top: 10px;}

.error_cart_product_avans{
	padding-left: 10px; position: absolute; top: 100%; white-space: nowrap;

	@media (max-width: @m){width: 100%; padding: 5px 0 0 26px; position: relative; top: unset; white-space: unset;}
}
.error_cart_product_sn{
	padding: 7px 0 0 20px; position: absolute;
	
	@media (max-width: @m){padding: 5px 0 0 10px; position: relative;}
}
.error_cart_hash_entered{
	padding: 7px 0 0 20px; position: absolute;

	@media (max-width: @t){padding: 7px 0 5px 25px;}
	@media (max-width: @m){padding: 5px 0 7px 15px; position: relative;}
}
.error_cart_product_shipping{
	padding-left: 32px;

	@media (max-width: @m){padding-left: 30px;}
}
.error_cart_customer_entered{
	padding: 15px 25px 0;

	@media (max-width: @m){padding: 10px 15px 0;}
}
.error_cart_customer_entered_sw{
	padding: 0; position: absolute; right: 25px; top: 20px;

	@media (max-width: @m){right: 15px; top: 12px;}
}

.w-cart-remove{
	display: flex; align-items: center; justify-content: center; width: 54px; height: 72px; font-size: 0; line-height: 0; position: absolute; right: 0; top: 0; z-index: 1; cursor: pointer;
	&:before{.icon-trash; font: 20px/1 @fonti; color: @red; .transition(opcaity);}
	&.active:before{color: @white;}
	&:hover:before{opacity: 0.8;}

	@media (max-width: @t){
		&:hover:before {opacity: 1;}
	}
	@media (max-width: @m){
		width: 46px; height: 46px;
		&:before{font-size: 16px;}
	}
}

.w-cart-remove-tooltip{
	padding: 50px 50px 45px; text-align: center;

	@media (max-width: @m){padding: 17px 15px 15px; font-size: 12px;}
}
.w-cart-remove-tooltip-btns{
	display: flex; align-items: center; justify-content: center; margin-top: 30px;
	
	.btn, button{min-width: 160px; min-height: 50px; margin: 0 10px;}
	.btn-cancel{
		background: #f2f5f8; border: 1px solid #ccd8e2; color: @textColor;
		&:hover{color: @textColor;}
	}
	.btn-remove{background: @red;}

	@media (max-width: @m){
		margin-top: 15px; flex-direction: column; gap: 10px;
		.btn, button{min-width: 115px; width: 100%; min-height: 36px; margin: 0 5px; font-size: 12px !important;}
	}
}
.btn-modal{font-size: 16px; border-radius: 2px;}
.btn-remove{color: #fff; border: 0;}
/*------- /cart -------*/

/*------- publish -------*/
.page-publish{
	@media (max-width: @m){
		.main-header{display: none;}
		.content-section{padding: 0;}
	}
}

.p-title{
	font-size: 32px; font-weight: normal; padding: 0;

	@media (max-width: @m){font-size: 14px; font-weight: 600; color: @white; padding: 15px; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%);}
}
.p-counter{
	font-size: 16px; color: @blue;

	@media (max-width: @m){font-size: 12px; color: @white; font-weight: normal; opacity: 0.6;}
}

.p-categories{
	display: flex; flex-wrap: wrap; margin-top: 15px;

	@media (max-width: @m){margin: 15px 15px 0;}
}
.p-categories-item{
	input[type=checkbox]+label{
		min-height: unset; display: flex; align-items: center; justify-content: center; margin: 0 8px 8px 0; padding: 6px 10px; font-size: 12px; line-height: 1.4; color: @textColor; border: 1px solid @borderColor; border-radius: @borderRadius; .transition(all);
		.counter{color: @red; font-weight: bold; margin-left: 5px; .transition(color);}
		@media (min-width: @t){
			&:hover{border-color: @borderColor/1.3}
		}
	}
	input[type=checkbox]:checked+label{
		background: @red; color: @white; border-color: @red;
		.counter{color: @white;}
	}
	input[type=checkbox]+label:before{content: none;}

	@media (max-width: @m){
		input[type=checkbox]+label{margin: 0 5px 5px 0;}
	}
}
.p-categories-item-remove{
	display: none; align-items: center; justify-content: center; margin: 0 0 8px 0; padding: 6px 10px; font-size: 12px; line-height: 1.4; color: @blue; border: 1px solid @blue; border-radius: @borderRadius; .transition(all);
	&.active{display: flex;}
	@media (min-width: @t){
		&:hover{color: @white; background: @blue;}
	}

	@media (max-width: @m){
		margin: 0 0 5px 0;
		&:hover{color: @blue;}
	}
}


.p-items{
	margin-top: 20px;
	.pp:last-child{border: none;}

	@media (max-width: @m){margin: 20px 15px 0;}
}
.p-load-more-container .btn{
	margin-top: 20px;

	@media (max-width: @m){margin: 10px 15px 0;}
}
/*------- /publish -------*/

/*------- publish post -------*/
.pp{
	display: block; width: 100%; flex-grow: 1; padding: 15px 0; border-bottom: 1px solid @borderColor;

	@media (max-width: @m){padding: 0 0 20px; border: none;}
}
.pp-title{
	font-size: 16px; line-height: 1.4; letter-spacing: -0.3px; font-weight: normal; padding: 0;
	a:hover{color: @blue;}

	@media (max-width: @m){
		font-size: 12px; letter-spacing: normal; font-weight: 600;
		a:hover{color: @textColor;}
	}
}
.pp-cnt{display: flex; align-items: center; margin-top: 5px; font-size: 12px; line-height: 1.4;}
.pp-date{
	margin-right: 6px; padding: 0 7px 0 20px; position: relative;
	&:before{.icon-calendar; font: 13px/13px @fonti; color: @red; position: absolute; left: 0; top: 1px;}
	&:after{.pseudo(1px,10px); background: #ccd8e2; right: 0; top: 2px;}
}
.pp-category a{
	color: @blue;
	@media (min-width: @t){
		&:hover{text-decoration: underline;}
	}
}
/*------- /publish post -------*/

/*------- publish post -------*/
.pd-title{
	@media (max-width: @m){padding: 15px 15px 10px;}
}
.pd-category{
	display: block; font-size: 14px; line-height: 1.4; color: @blue; padding: 0 0 20px;
	&:hover{text-decoration: underline;}

	@media (max-width: @m){
		font-size: 12px; padding: 0 15px 15px;
		&:hover{text-decoration: unset;}
	}
}
/*------- /publish post -------*/

/*------- auth -------*/
.auth-login{
	min-height: 100vh; display: flex; align-items: center; background: linear-gradient(63.43deg,#0050a0 0,#0078b4 100%);
	.error{color: @white;}

	@media (max-width: @m){padding: 0 25px;}
}
.auth-login-wrapper{
	width: 500px; margin: 0 auto;

	@media (max-width: @m){width: 100%; max-width: 500px; margin: 25px auto;}
}
.auth-logo-container{
	display: flex; flex-direction: column; align-items: center; margin: 0 auto 50px; font-size: 14px; line-height: 1.2; text-transform: uppercase; color: @white; font-weight: bold; letter-spacing: -0.2px;

	@media (max-width: @m){font-size: 11px; margin-bottom: 30px;}
}
.auth-logo{
	width: 215px; height: 43px; background: url(images/logo.svg) no-repeat; background-size: contain; display: block; flex-shrink: 0; margin-bottom: 10px;

	@media (max-width: @m){width: 114px; height: 24px; margin-bottom: 8px;}
}
.auth-login-form{
	input{border: none; box-shadow: 5px 5px 25px 0 rgba(3,32,62,0.3);}
}
.submit-container{
	display: flex; align-items: center; margin-top: 15px;
	p{padding-bottom: 0;}
	button, .btn{box-shadow: 5px 5px 25px 0 rgba(3,32,62,0.3);}

	@media (max-width: @m){
		display: block;
		button, .btn{width: 100%;}
	}
}
.auth-links{
	width: 100%; text-align: center; font-size: 14px; line-height: 1.4; padding: 0 20px; display: flex; flex-direction: column;
	a{
		color: @white; text-decoration: none;
		&:hover{color: @white; text-decoration: none;}
	}

	@media (max-width: @m){
		font-size: 13px; padding: 20px;
		a{text-decoration: underline;}
	}
}
.auth-department-form{
	select{margin-bottom: 15px; border: none; box-shadow: 5px 5px 25px 0 rgba(3,32,62,0.3);}
	button, .btn{box-shadow: 5px 5px 25px 0 rgba(3,32,62,0.3);}

	@media (max-width: @m){
		select{margin-bottom: 10px;}
		button, .btn{width: 100%;}
	}
}
.auth-department-btns{
	display: flex; justify-content: center; margin-top: 10px;

	@media (max-width: @m){margin-top: 15px;}
}
/*------- /auth -------*/

/*------- users -------*/
.page-users .content-section{
	display: flex; padding-top: 100px; padding-bottom: 100px;

	@media (max-width: @m){padding-top: 47px; padding-bottom: 0;}
}

.users-header{
	display: flex; align-items: center; margin-bottom: 20px;

	@media (max-width: @iframeM){height: 47px; margin: 0; padding: 0 15px; position: fixed; top: 0; left: 0; right: 0; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); z-index: 111;}
}
.users-back-btn{
	display: none;

	@media (max-width: @iframeM){
		width: 25px; height: 25px; display: flex; align-items: center; flex-shrink: 0; font-size: 0; line-height: 0; position: relative;
		&:before{.icon-arrow; font: 11px/11px @fonti; color: @white; position: absolute; left: 0;}
	}
}
.users-title{
	font-size: 24px; line-height: 1.4; letter-spacing: -0.4px; font-weight: normal; padding: 0 50px 0 0; flex-grow: 1;

	@media (max-width: @iframeT){padding-right: 30px;}
	@media (max-width: @iframeM){font-size: 14px; color: @white; font-weight: 600; padding: 0;}
}
.new-user-btn{
	display: block; font-size: 14px; line-height: 1.4; letter-spacing: -0.4px; font-weight: 600; .transition(color);
	span{
		padding-left: 22px; position: relative;
		&:before, &:after{.pseudo(14px,2px); background: @blue; left: 0; top: 7px;}
		&:after{width: 2px; height: 14px; left: 6px; top: 1px;}
	}
	&:hover{color: @blue;}

	@media (max-width: @iframeM){display: none;}
}

.users-content{
	position: relative;

	@media (max-width: @iframeM){padding: 60px 0 75px;}
}
.users-content-small{
	width: 500px;

	@media (max-width: @iframeT){width: 100%;}
}

.users-search{
	display: flex; align-items: center; flex-grow: 1; margin-bottom: 20px; position: relative;

	@media (max-width: @iframeM){padding: 0 15px; margin-bottom: 12px;}
}
.users-search-form{position: relative; flex-grow: 1;}
.users-search-input{
	height: 60px; padding: 0 75px 0 25px; font-size: 16px; .placeholder(@textColor, @borderColor);

	@media (max-width: @iframeM){height: 44px; padding: 0 55px 0 15px; font-size: 14px;}
}
.users-search-btn{
	background: white; width: 60px; min-height: 58px; position: absolute; top: 1px; right: 1px; font-size: 0; line-height: 0;
	&:before{.icon-search; font: 18px/18px @fonti; color: @blue; position: absolute; .transition(color);}
	&:hover:before{color: @blue/1.3;}

	@media (max-width: @iframeT){
		&:hover:before{color: @blue;}
	}
	@media (max-width: @iframeM){
		width: 44px; min-height: 42px;
		&:before{font-size: 16px; line-height: 16px;}
	}
}
.users-barcode{
	display: none;

	@media (max-width: @iframeT){
		display: flex; align-items: center; justify-content: center; width: 60px; height: 60px; flex-shrink: 0; margin-left: 10px; border-radius: @borderRadius; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); position: relative;
		&:before{.icon-barcode; font: 22px/22px @fonti; color: @white; position: absolute; left: 18px;}
	}
	@media (max-width: @iframeT){
		width: 44px; height: 44px;
		&:before{font-size: 17px; line-height: 17px; left: 13px;}
	}
}

.users-list{position: relative; flex-grow: 1;}
.users-list-name{
	width: 16%; font-weight: 600;
	.name{display: block; color: @blue;}

	@media (max-width: @iframeT){
		width: 100%;
		.name{display: inline-block;}
	}
}
.users-list-address{
	width: 18.5%;

	@media (max-width: @iframeT){width: 100%;}
}
.users-list-email{width: auto; flex-grow: 1;}
.users-list-tel{
	width: 13.5%;

	@media (max-width: @iframeT){width: 100%;}
}
.users-list-bill{
	width: 18.5%;
	.m-label{display: none;}

	@media (max-width: @iframeT){
		width: 100%; padding: 0;
		.m-label{display: inline-block;}
	}
}
.users-list-btns{
	width: 74px; padding: 0; display: flex; align-items: center;

	@media (max-width: @iframeT){position: absolute; right: 15px; bottom: 15px;}
	@media (max-width: @iframeM){width: 70px; bottom: 10px;}
}

.btn-user{
	width: 36px; height: 36px; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border-radius: @borderRadius; display: flex; align-items: center; justify-content: center; flex-shrink: 0; margin-left: 1px; font-size: 0; line-height: 0; position: relative; .transition(box-shadow);
	&:hover{box-shadow: 0 100px 10px rgba(0,0,0,0.08) inset;}

	@media (max-width: @iframeT){
		&:hover{box-shadow: none;}
	}
	@media (max-width: @iframeM){width: 30px; height: 30px; margin-left: 5px;}
}
.btn-user-loyalty{
	background: linear-gradient(225deg, #01AABA 0%, #0092A0 100%);
	&:before, &:after{.pseudo(12px,2px); background: @white;}
	&:after{width: 2px; height: 12px;}

	@media (max-width: @iframeM){
		&:before, &:after{width: 10px;}
		&:after{width: 2px; height: 10px;}
	}
}
.btn-user-share:before{
	.icon-share; font: 15px/15px @fonti; color: @white; position: absolute;

	@media (max-width: @iframeM){font-size: 12px; line-height: 12px;}
}
.btn-user-edit:before{
	.icon-edit; font: 15px/15px @fonti; color: @white; position: absolute;

	@media (max-width: @iframeM){font-size: 13px; line-height: 13px;}
}

.users-list-footer{
	display: flex; flex-direction: column; align-items: center; margin-top: 40px;

	@media (max-width: @iframeM){
		margin-top: 20px; padding: 0 15px;
		.btn, button{width: 100%;}
	}
}
.users-pagination{
	display: block; margin-top: 15px; font-size: 12px; line-height: 1.3; letter-spacing: -0.3px; opacity: 0.6; text-align: center;

	@media (max-width: @iframeM){margin-top: 10px;}
}

.new-user-btn-float{
	display: none;

	@media (max-width: @iframeM){display: flex; bottom: 10px; box-shadow: 3px 5px 6px 0 rgba(3,32,62,0.15);}
}
/*------- /users -------*/

/*------- users form -------*/
.checkbox-section{
	padding-bottom: 15px;

	@media (max-width: @iframeM){padding: 0 0 10px;}
}
.checkbox-section-special{
	display: flex; align-items: center; padding: 10px 0 20px;
	&>span{margin-right: 15px; font-size: 14px; font-weight: 600;}
	p{padding-bottom: 0; margin-right: 20px;}

	@media (max-width: @iframeM){padding: 5px 0 15px;}
}
.form-tooltip{
	display: block; font-size: 12px; line-height: 1.4; letter-spacing: -0.2px; padding: 7px 0 7px 20px; color: rgba(51,51,51,0.6);

	@media (max-width: @iframeM){font-size: 11px; padding: 5px 0 5px 15px;}
}

.users-form{
	position: relative;

	@media (max-width: @iframeM){padding: 0 15px;}
}
.users-form-btn{
	display: flex; justify-content: flex-end; margin-top: 20px;

	@media (max-width: @iframeM){margin-top: 10px;}
}

.users-content-details{
	@media (max-width: @iframeM){padding-left: 15px; padding-right: 15px;}
}
.user-details-header{
	@media (max-width: @iframeM){padding-right: 0;}
}
.user-details-btn-header{
	font-size: 14px; line-height: 1.4; font-weight: 600; letter-spacing: -0.4px; margin-left: 40px;
	span{position: relative; padding-left: 28px;}
	&:hover{color: @blue;}

	@media (max-width: @iframeT){
		font-size: 12px; margin-left: 30px;
		span{padding-left: 25px;}
		&:hover{color: @textColor;}
	}
	@media (max-width: @iframeM){
		font-size: 0; line-height: 0; width: 47px; height: 47px; margin-left: 0; display: flex; align-items: center; justify-content: center; flex-shrink: 0; border-left: 1px solid rgba(204, 216, 226, 0.2);
		span{padding: 0; width: 47px; height: 47px; display: flex; align-items: center; justify-content: center;}
	}
}
.user-details-share span:before{
	.icon-share; font: 17px/17px @fonti; color: @blue; position: absolute; left: 0;

	@media (max-width: @iframeT){font-size: 15px; line-height: 15px;}
	@media (max-width: @iframeM){font-size: 17px; line-height: 17px; color: @white; left: unset;}
}
.user-details-edit span:before{
	.icon-edit; font: 16px/16px @fonti; color: @blue; position: absolute; left: 0;

	@media (max-width: @iframeT){font-size: 14px; line-height: 14px;}
	@media (max-width: @iframeM){font-size: 16px; line-height: 16px; color: @white; left: unset;}
}

.user-data{
	display: block; margin-bottom: 15px; font-size: 16px;
	span{display: block; font-size: 14px; font-weight: bold; color: @blue; padding-bottom: 3px;}

	@media (max-width: @iframeM){
		margin-bottom: 10px; font-size: 12px;
		span{font-size: 12px; padding-bottom: 2px;}
	}
}
.btn-user-details-loyalty{
	min-width: 220px; margin-top: 15px;

	@media (max-width: @iframeM){min-width: 180px;}
}

.user-edit-btn-submit{
	@media (max-width: @iframeM){min-width: 120px;}
}
/*------- /users form -------*/

/*------- price tag -------*/
.pt-header{display: flex; align-items: center; justify-content: space-between; margin-bottom: 20px;}
.pt-title{font-size: 32px; line-height: 1.4; font-weight: normal; letter-spacing: -0.5px; padding: 0 30px 0 0; flex-grow: 1;}
.pt-header-btn{min-height: 50px;}
.pt-content{display: flex;}

.pt-form{width: 400px; flex-shrink: 0;}
.pt-button-container{
	display: flex; justify-content: flex-end;
	button, .btn{min-width: 160px;}
}

.pt-list-counter{font-size: 12px; line-height: 1.4; letter-spacing: -0.2px; opacity: 0.6; position: absolute; top: -46px; left: 20px;}
.pt-list{flex-grow: 1; margin-left: 100px; position: relative;}
.pt-list-row{padding: 14px 20px;}
.pt-list-col1{
	width: 195px;
	span{font-weight: 600; color: @green;}
}
.pt-list-col2{width: 150px;}
.pt-list-col3{width: 120px;}
.pt-list-col4{width: 100px;}
.pt-list-col5{
	flex-grow: 1; text-align: right; padding: 0;
	a{
		font-weight: bold; text-decoration: underline; color: @blue;
		&:hover{text-decoration: none;}
	}
}

.pt-sort{
	width: 100%; margin-bottom: 35px; display: flex; align-items: center;
	select{flex-grow: 1; margin-right: 10px;}
	select:last-child{margin-right: 0;}
}
/*------- /price tag -------*/

/*------- locations -------*/
.locations-header{
	display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;

	@media (max-width: @iframeM){padding: 15px 15px 0;}
}
.locations-title{font-size: 32px; line-height: 1.3; font-weight: normal; flex-grow: 1; padding: 0 30px 0 0;}
.add-location-btn{min-height: 50px;}

.locations-content{
	@media (max-width: @iframeM){padding: 0 15px;}
}
.ls{width: 100%; position: relative;}
.ls-buttons{
	display: flex; align-items: center; justify-content: flex-end; margin-top: 10px;
	button, .btn{min-width: 160px; min-height: 50px; margin-left: 10px;}
}

.lsr{margin-top: 25px;}
.lsr-counter{font-size: 14px; line-height: 1.4; letter-spacing: -0.3px; font-weight: 600; padding: 0 0 10px 20px;}
.lsr-table{width: 100%; position: relative;}
.lsr-row{display: flex; align-items: center; flex-grow: 1; padding: 13px 20px; font-size: 14px; line-height: 1.4; letter-spacing: -0.3px;}
.lsr-row:nth-child(odd){background: #F0F2F4;}
/*------- /locations -------*/

/*------- banners -------*/
.banners-header{
	display: flex; align-items: center; justify-content: space-between; margin-bottom: 15px;

	@media (max-width: @iframeM){padding: 15px 15px 0; margin-bottom: 10px;}
}
.banners-title{
	font-size: 32px; line-height: 1.4; font-weight: normal; letter-spacing: -0.2px; padding: 0 30px 0 0; flex-grow: 1;
	.counter{font-size: 16px; letter-spacing: -0.5px; color: @blue;}

	@media (max-width: @iframeM){
		font-size: 22px; padding-right: 15px;
		.counter{font-size: 12px;}
	}
}
.add-banner-btn{min-width: 100px;}

.banners-content{flex-grow: 1; position: relative;}
.banners-list{width: 100%;}
.banners-list-header{
	padding: 13px 20px;
	@media (max-width: @iframeT){
		display: flex;
		.bi-col{padding: 0;}
	}
	@media (max-width: @iframeM){display: none;}
}
.banners-list-search{
	display: flex; align-items: center; padding: 10px 20px;
	input, select{height: 44px; .placeholder(#ccd8e2, @borderColor);}
	select{background-position: right 20px top 17px;}

	@media (max-width: @iframeT){display: none;}
}
.banners-list-row{
	padding: 18px 20px;
	&:nth-child(odd){background: #F0F2F4;}
	&:nth-child(even){background: @white;}

	@media (max-width: @iframeT){display: flex;}
	@media (max-width: @iframeM){padding: 15px;}
}
.bi-col{
	width: calc(~"100% / 4"); flex-grow: 1; flex-shrink: unset;

	@media (max-width: @iframeT){padding: 0 10px 0 0;}
}
.bi-col-special span{
	position: relative; padding-right: 20px;
	&:before{.icon-sort; font: 18px/18px @fonti; color: @white; position: absolute; right: 0;}
}
.bi-col1{
	width: 180px; flex-grow: unset; flex-shrink: 0; padding-right: 60px;
	img{width: auto; height: auto; max-width: 100%; max-height: 100%; display: block;}

	@media (max-width: @iframeT){width: 100px; padding-right: 15px;}
}
.bi-col6{width: 36px; flex-shrink: 0; padding: 0;}
.btn-banner-edit{
	display: flex; align-items: center; justify-content: center; width: 36px; height: 36px; flex-shrink: 0; background: linear-gradient(63.43deg, #0050A0 0%, #0078B4 100%); border-radius: @borderRadius; position: relative; font-size: 0; line-height: 0;
	&:before{.icon-edit; font: 14px/14px @fonti; color: @white; position: absolute;}
}

.add-banner-header{
	position: relative; margin-bottom: 25px;

	@media (max-width: @iframeM){padding: 15px 15px 0;}
}
.add-banner-title{font-size: 24px; line-height: 1.4; letter-spacing: -0.4px; font-weight: normal; padding: 0;}
.add-banner-content{
	width: 500px; flex-shrink: 0;

	@media (max-width: @iframeM){width: 100%; padding: 0 15px;}
}
.add-banner-btns{
	display: flex; align-items: center; justify-content: space-between; margin-top: 30px;
	button, .btn{min-width: 160px;}

	@media (max-width: @iframeM){
		button, .btn{min-width: 140px;}
	}
}
/*------- /banners -------*/

/*------- datepicker -------*/
.ui-datepicker { 
	display:none; width:255px; background:#fff; font-size: 12px; color: @textColor; box-shadow: 0px 0px 12px rgba(0,0,0,.15); z-index: 99999!important; 
}
.ui-datepicker-calendar {
	width: 93%; margin: 5px auto 7px auto; font-size: 12px; line-height: 1.6; font-weight: normal; text-align: center;
	td {padding: 0 1px 1px 0; color:#fff; text-align:center; width: 14.2%; vertical-align: middle;}
	a, span {display: block; width: 28px; margin: auto; padding: 4px 0; border: none; line-height: 1.4; text-decoration:none; color: @textColor; .transition(all);}
	
	th span {font-size: 12px; border: none; color: @textColor; text-transform: uppercase;}
	.ui-datepicker-unselectable, .ui-state-disabled span {color:#d0d0d0;}
	.ui-state-active, .ui-state-hover{background: @blue; color: #fff;}
	th{padding: 4px 0 6px;}
}
.ui-datepicker-header, .ui-timepicker-div .ui-widget-header { 
	color:@textColor; position: relative; font-size: 14px; line-height: 1.4; margin: 15px 15px 10px; font-weight: bold; text-align: center;
	a{text-decoration:none; padding: 1px 2px; cursor:pointer;}
}
.ui-datepicker-prev, .ui-datepicker-next { 
	display:block; width: 25px; height: 25px; position: absolute; top:0; left:0; text-indent: 0; background: @blue; .transition(all);
	&:before{.icon-arrow; font: 8px/26px @fonti; color: #fff; text-align: center; position: absolute; left: 0; right: 0; top: 0; bottom: 0;}
	.ui-icon{display: none;}
	&:hover{background: #000;}
	&.ui-state-disabled{opacity: .3;}
}
.ui-datepicker-next{
	&:before{transform: scalex(-1);}
}
.ui-datepicker-next{right: 0; left: auto;}

.ui-timepicker-div{
	color: @textColor; position: relative; padding: 10px 30px; margin-bottom: 20px; border-top: 1px solid @borderColor; border-bottom: 1px solid @borderColor;
	.ui-widget-header { margin-bottom: 8px; }
	select{height: 30px; padding: 0 10px; background-size: 11px auto; background-position: right 20px center;}
	dl{display: flex; font-size: 16px; justify-content: center;}
	dt{font-weight: bold; padding-right: 11px; line-height: 17px; padding-top: 6px;}
	.ui_tpicker_minute, .ui_tpicker_hour{width: 50px;}
}
.sw-datepicker{
	line-height: 22px;
	#ui-datepicker-div{left: 0!important; top: 60px!important;}
}
.ui_tpicker_minute_label{padding-left: 35px;}
.ui_tpicker_time_label, .ui_tpicker_time{display: none!important;}
.ui-datepicker-buttonpane{
	padding: 0px 30px 20px;
	button{padding: 0; width: 100%; font-size: 20px; background: @gray; height: 50px; line-height: 50px;}
}
.ui-datepicker-current{display: none!important;}
.ui_tpicker_hour, .ui_tpicker_minute{
	display: block; width: 100%;
	select{display: block; width: 52px; background: url(images/select-bg.svg) no-repeat; background-position: right 10px center; background-size: 8px; font-size: 16px; font-weight: bold;}
}
.ui-datepicker-h1{position: absolute; left: 0; right: 0; top: -33px; font-size: 16px; font-weight: 600;}
.ui-datepicker-title{
	select{height: 26px; width: auto; display: inline-block; vertical-align: top; font-weight: normal; font-size: 12px; padding: 0 25px 0 12px; margin: 0 0 0 -1px; background-position: right 8px center; background-size: 8px auto;}
}
/*------- /datepicker -------*/

/*------- iframe -------*/
.page-iframe{
	.content-section{
		padding-bottom: 100px;

		@media (max-width: @t){padding-bottom: 0;}
	}
}
.iframe-users{
	width: 100%; height: calc(~"100vh - 210px");

	@media (max-width: @m){height: calc(~"100vh - 130px");}
}
/*------- /iframe -------*/

/*------- fancybox -------*/
.fancybox-lock body{overflow: unset!important;}
.fancybox-lock .fancybox-overlay{overflow-x: hidden;}
.fancybox-overlay{background: rgba(0,24,47,0.6);}
.fancybox-opened .fancybox-skin{box-shadow: 0 20px 30px 0 rgba(0,34,67,0.3); border-radius: @borderRadius; padding: 0!important;}
.fancybox-outer, .fancybox-inner{border-radius: @borderRadius;}
.fancybox-close{
	background: @red; width: 54px; height: 54px; line-height: 54px; border-radius: 100%; border: 3px solid @white; top: -25px; right: -25px; display: flex; align-items: center; justify-content: center; box-shadow: 5px 8px 20px 0 rgba(3,32,62,0.2); .transition(all);
	&:before{.icon-x; font: 20px/20px @fonti; color: @white; position: absolute;}
	@media (min-width: @t){
		&:hover{background: @red / 1.2;}
	}

	@media (max-width: @m){
		width: 34px; height: 34px; line-height: 34px; border: 2px solid @white; top: -12px; right: -12px;
		&:before{font-size: 14px; line-height: 14px;}
	}
}
#fancybox-loading{
	background: #fff url(images/loader.svg) no-repeat center center; width: 90px; height: 90px; margin-left: -40px; margin-top: -40px; background-size: 60px auto; border-radius: @borderRadius; opacity: 1; box-shadow: 0 10px 40px 0 rgba(0,0,0,0.3); z-index: 99999;
	div{background: none;}
}
/*------- /fancybox -------*/


@media screen and (max-width: 980px) {
	@-moz-document url-prefix() {
		.main-container img {
			width: 100%;
			height:auto;
		}
		button img{width: auto!important;}
	}

	/*------- 980 tables -------*/
	.table-wrapper { overflow-x: scroll; -webkit-overflow-scrolling:touch; }
	::-webkit-scrollbar {-webkit-appearance: none; width: 3px; background: @gray;}
	::-webkit-scrollbar-thumb {border-radius: 3px; background-color: @blue;}
	::-webkit-scrollbar-track {border: none; } 
	/*------- /980 tables -------*/	
}