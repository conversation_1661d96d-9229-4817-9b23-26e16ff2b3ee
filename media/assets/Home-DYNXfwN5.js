import{u as E,a as F,c as R,o as T,b as o,F as p,r as w,d as H,e as n,n as C,f as s,w as g,v as f,g as c,_ as S,h as _,t as i,i as h,j as k,k as v,l as I,m as L,p as M}from"./index-715AKfMZ.js";const P={class:"categories"},W=["onClick"],q={class:"category-img"},A={class:"category-title"},G={class:"subcategories"},J=["onClick"],K={class:"subcategory-icon"},O={class:"subcategory-title"},Q={key:0,class:"subsubcategories"},U={class:"counter"},X={key:0,class:"subsubcategory"},Y={class:"counter"},Z={key:1,class:"subsubcategory"},b={class:"counter"},y={class:"subsubcategory"},st={__name:"Home",setup(tt){const x=H(),m=I(),d=E(),{generateThumbs:N}=M(),V=F(),j=R(()=>{let l=V.categories;return N({data:l,preset:"categoryEntry"}),l});T(async()=>{d.loading=0;const l=await d.fetchCmsPage(x.path);l!=null&&l.seo_title&&(document.title=l.seo_title)});function z(l,a){l||m.push(a)}function B(l,a){window.innerWidth<=960&&l||m.push(a)}return(l,a)=>{const r=L("router-link");return n(),o("ul",P,[(n(!0),o(p,null,w(j.value,e=>(n(),o("li",{key:e.id,class:C(["category",e.code,{active:e.toggle,"has-children":e.children}])},[s("a",{href:"javascript:void(0)",onClick:t=>(z(e.children,e.url_without_domain),e.toggle=!e.toggle)},[g(s("span",q,[c(S,{loading:"lazy",data:e.main_image_upload_path_thumb},null,8,["data"])],512),[[f,e.main_image]]),s("span",A,[_(i(e.title),1),a[0]||(a[0]=s("span",{class:"toggle-icon"},null,-1))])],8,W),g(s("ul",G,[(n(!0),o(p,null,w(e.children,(t,$)=>(n(),o("li",{key:$,class:C(["subcategory",{active:t.toggle&&t.children,"has-children":t.children}])},[s("a",{href:"javascript:void(0)",onClick:u=>(B(t.children,t.url_without_domain),t.toggle=!t.toggle)},[g(s("span",K,[c(S,{loading:"lazy",src:t.main_image_upload_path,width:"20",height:"20"},null,8,["src"])],512),[[f,t.main_image]]),s("span",O,[_(i(t.title),1),a[1]||(a[1]=s("span",{class:"toggle-icon"},null,-1))])],8,J),t.children?(n(),o("ul",Q,[(n(!0),o(p,null,w(t.children,(u,D)=>(n(),o("li",{key:D,class:"subsubcategory"},[c(r,{to:u.url_without_domain},{default:h(()=>[_(i(u.title),1),s("span",U,i(u.total),1)]),_:2},1032,["to"])]))),128)),t.total_discount!="0"?(n(),o("li",X,[c(r,{to:t.url_without_domain+"?discount=1",class:"red"},{default:h(()=>[_(i(k(d).labels.sale),1),s("span",Y,i(t.total_discount),1)]),_:2},1032,["to"])])):v("",!0),t.total_new!="0"?(n(),o("li",Z,[c(r,{to:t.url_without_domain+"?new=1",class:"blue"},{default:h(()=>[_(i(k(d).labels.new),1),s("span",b,i(t.total_new),1)]),_:2},1032,["to"])])):v("",!0),s("li",y,[c(r,{to:t.url_without_domain},{default:h(()=>[_(i(k(d).labels.show_all),1)]),_:2},1032,["to"])])])):v("",!0)],2))),128))],512),[[f,e.children&&e.toggle]])],2))),128))])}}};export{st as default};
