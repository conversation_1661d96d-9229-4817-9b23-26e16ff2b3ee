import{u as h,z as d,G as l,E as _,l as f}from"./index-715AKfMZ.js";const w={__name:"CopyCart",setup(m){const t=h(),a=d(),o=l(),r=f(),c=window.location.href,s=new URL(c).searchParams;if(s.get("identificator")){t.loading=1;const e=s.get("identificator"),i=o.endpoints.value._get_hapi_copy_cart.replace("%IDENTIFICATOR%",e);async function u(){await _({url:i,method:"GET"}).then(async p=>{p.success&&(await a.fetchCarts(),t.loading=0,r.push({name:"shoppingCart"}))})}u()}return(e,n)=>null}};export{w as default};
