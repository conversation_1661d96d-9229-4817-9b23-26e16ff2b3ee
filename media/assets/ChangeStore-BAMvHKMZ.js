import{q as h,u as v,s as f,x as g,c as b,o as S,b as r,f as e,g as k,h as x,t as n,j as c,w,y,F as C,r as L,e as u,l as V,m as B}from"./index-715AKfMZ.js";const N={class:"auth-login"},D={class:"auth-login-wrapper"},F={class:"auth-logo-container"},M={class:"auth-department-form"},j={disabled:"",selected:"",value:""},q=["value"],A={class:"auth-department-btns"},E={__name:"ChangeStore",setup(I){const s=v(),l=f(),d=V(),o=g(),_=b(()=>(l.locations.find(i=>{i.selected&&(o.value=i.id)}),l.locations));S(()=>{s.loading=0});async function m(){await l.submitLocation(o.value),d.push({name:"homepage"})}return(i,a)=>{const p=B("router-link");return u(),r("div",N,[e("div",D,[e("div",F,[k(p,{to:"/",class:"auth-logo"}),x(" "+n(c(s).labels.sales_assistant),1)]),e("div",M,[w(e("select",{name:"location_id","onUpdate:modelValue":a[0]||(a[0]=t=>o.value=t)},[e("option",j,n(c(s).labels.pa_login_location),1),(u(!0),r(C,null,L(_.value,t=>(u(),r("option",{key:t.id,value:t.id},n(t.title),9,q))),128))],512),[[y,o.value]]),e("div",A,[e("button",{onClick:a[1]||(a[1]=t=>m()),class:"btn-green btn-medium"},n(c(s).labels.confirm),1)])])])])}}},T=h(E,[["__scopeId","data-v-c99f0ef4"]]);export{T as default};
