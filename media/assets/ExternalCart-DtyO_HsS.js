import{q as i,u,z as _,x as p,o as d,b as f,t as m,j as h,k as g,e as x,l as b}from"./index-715AKfMZ.js";const C={key:0,class:"global-error"},v={__name:"ExternalCart",setup(w){const a=u(),n=_(),r=b(),t=p();a.loading=1;async function c(s){await n.restoreCart(s).then(e=>{e.success?r.push({name:"shoppingCart"}):e.data.label_name=="error_cart_with_given_cart_code_already_exists"?r.push({name:"shoppingCart"}):t.value=e.data.label_name})}return d(()=>{const e=window.location.href.split("/"),o=e.indexOf("cart");if(o!==-1&&o<e.length-1){const l=e[o+1];c(l)}a.loading=0}),(s,e)=>t.value?(x(),f("div",C,m(h(a).labels[t.value]),1)):g("",!0)}},y=i(v,[["__scopeId","data-v-fb25c085"]]);export{y as default};
