import{u as z,z as oe,x as w,U as Z,c as I,l as F,m as Y,e as t,b as i,f as s,t as o,j as c,k as u,F as $,r as L,g as E,i as R,h as C,n as x,L as ee,H as V,_ as ie,W as ne,d as O,M as U,o as G,w as S,y as ce,Z as J,q as D,E as re,a3 as te,G as ue,a4 as de,v as _e,a as se,P as me,R as N,V as ve,p as fe}from"./index-715AKfMZ.js";/* empty css                                                               */const ae="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wAARCAA8ADwDASIAAhEBAxEB/8QAGgABAQADAQEAAAAAAAAAAAAAAAUBAwQCB//EADMQAAEEAQICBQoHAAAAAAAAAAEAAgMEBRESITEGFEFRcRMWIjI2dJOhssEVI0JSVZHh/8QAFAEBAAAAAAAAAAAAAAAAAAAAAP/EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAMAwEAAhEDEQA/APsyIiAi02rUFKAz2HhjB2rg84aX7LPwHIKqKZHn6D5GsLpYy46AyRlo18SqaAiIgKNObOTy9inHbfWiqtZu8mOLy4a81ZUjH+0mW8IfpQY8398kbp8hZmax4fsedQSF0Ws5jKcpintNa8c2gF2n9BeM9Zmgx4ZAdss8jYmu7te35LfRxlShAIoomk/qe4alx7yg1S9Sz9B8UVgPYSPSZzafArhdRay62n+OWhYc3c1hOv8Ai9ZiszGvjy1Rvk3seBK1vAPaeeoVc1K7rTbRiaZmjaH6cQEHJhrc1mrK2dwdLXmdC54Gm7b2/NUVI6P+rkff5fsq6ApGP9pMt4Q/Sq6kWa1+pk5b1CKOdthrRLG520gt4Ag+CDfmaUl2htgP50bxJHr2uHYtdXpBRmZpYkFadvB8UnAtK8dezf8AER/HC0TuyVkgz4GvIRyLpWn7IFy23PSsoUtXwB4dPNp6IA7B3lXlGjtZiJgZHhomNHINnACz13OHgMTED3mccEGej/q5H3+X7KuuHE0pKNVwmc1000jpZC3luPcu5AREQEREBERAREQf/9k=",pe={class:"clear cp"},ye={class:"cp-col1"},he={class:"cp-col1-top"},ge={class:"cp-info-top"},be={class:"cp-info-top-left"},Ae={key:0,class:"cp-badges"},ke={key:0,class:"cp-badge uau"},qe={class:"cp-badge-tooltip cp-badge-tooltip-uau"},$e={key:0,class:"cp-badge cp-badge-new new"},we={key:0},xe={class:"cp-badge-tooltip cp-badge-tooltip-new"},Ce={key:1,class:"cp-badge cp-badge-discount discount"},Ee={class:"cp-badge-tooltip cp-badge-tooltip-discount"},Me={key:2,class:"cp-badge cp-badge-gift gift"},Te={key:0},He={class:"cp-badge-tooltip cp-badge-tooltip-gift"},Le={class:"cp-title"},ze={class:"cp-info"},Be={class:"cp-code"},Pe={key:0,class:"cp-category"},Fe={key:1,class:"cp-logistic"},Oe={key:2,class:"cp-condition-wrapper"},Re={class:"cp-condition"},Se=["innerHTML"],De={class:"cp-info-top-right"},Qe={class:"cp-bottom"},je={class:"cp-bottom-left"},Ie={key:0,class:"cp-current-price cp-variation-price"},Ve={class:"cp-old-price cp-price-label"},Ke={class:"cp-old-price line-through"},Ue={class:"cp-current-price cp-discount-price red"},Ne=["innerHTML"],Ze={key:1,class:"cp-current-price"},Ge=["innerHTML"],Je={key:2,class:"cp-discount-expire"},We=["innerHTML"],Ye={key:3,class:"cp-current-price cp-loyalty-price"},Xe=["innerHTML"],et=["onClick"],tt={key:0,class:"cp-badge-coupon-title"},it={key:1,class:"cp-badge-coupon-info"},st={class:"title"},at={class:"icon"},lt={key:0,class:"cp-badge-coupon-tooltip"},ot={key:2,class:"cp-payments-options"},nt=["innerHTML"],ct=["innerHTML"],rt={class:"cp-btns"},ut={class:"cp-col2"},dt={class:"cp-image"},_t={__name:"IndexEntry",props:["item"],setup(e){const a=z(),k=oe(),r=e,p=w(0),{formatCurrency:h,formatDate:d}=ne(),y=Z("t");let l=w(!1),v=w(a.labels.pa_coupon_copy?a.labels.pa_coupon_copy:"Kopiraj kodo");function _(A){navigator.clipboard.writeText(A),y.value==!0?(v.value=a.labels.pa_coupon_copied?a.labels.pa_coupon_copied:"Koda je kopirana",setTimeout(()=>{l.value=!1},2e3)):(v.value=a.labels.pa_coupon_copied?a.labels.pa_coupon_copied:"Koda je kopirana",setTimeout(()=>{v.value=a.labels.pa_coupon_copy?a.labels.pa_coupon_copy:"Kopiraj kodo"},2e3))}const q=I(()=>{let A;return r.item.status=="9"||r.item.status2=="4"?A="not-available":r.item.status=="2"?A="supplier":r.item.status=="7"?A="not-available-in-store":r.item.status=="5"&&(A="preorder"),A}),g=I(()=>{let A="add_to_shopping_cart";return["advanced","configurable"].indexOf(r.item.type)?A="add_to_shopping_cart_configurable":r.item.status=="5"&&(A="add_to_shopping_cart_preorder"),A});I(()=>{let A=[];return Object.entries(r.item.warehouses).forEach(m=>{const[f,B]=m;A.push(f)}),A});const M=I(()=>{var m;let A=0;return(m=r.item.installments_calculation)!=null&&m.installments_min_price&&(A=r.item.installments_calculation.installments_min_price,r.item.installments_calculation.installments_loyalty_min_price&&(A=r.item.installments_calculation.installments_loyalty_min_price)),A}),Q=F();async function j(){p.value=0,await k.addProduct({shopping_cart_code:r.item.shopping_cart_code}).then(A=>{Q.push({name:"shoppingCart"})})}return(A,m)=>{var B,H;const f=Y("router-link");return t(),i("article",pe,[s("div",ye,[s("div",he,[s("div",ge,[s("div",be,[e.item.badges_special_1||e.item.selected_price&&e.item.selected_price=="uau"?(t(),i("div",Ae,[e.item.selected_price&&e.item.selected_price=="uau"?(t(),i("div",ke,[s("span",null,o(c(a).labels.uau_badge_title),1),s("div",qe,o(c(a).labels.uau_badge_title),1)])):u("",!0),e.item.badges_special_1?(t(!0),i($,{key:1},L(e.item.badges_special_1,n=>(t(),i("span",{key:n},[n.category==4?(t(),i("div",$e,[e.item.priority_details?(t(),i("span",we,o(e.item.priority_details.title),1)):u("",!0),s("div",xe,o(c(a).labels.pa_new_badge_tooltip),1)])):u("",!0),n.category==0?(t(),i("div",Ce,[s("span",null,"-"+o(e.item.discount)+" %",1),s("div",Ee,o(c(a).labels.pa_discount_badge_tooltip),1)])):u("",!0),n.category==3?(t(),i("div",Me,[n.label_title?(t(),i("span",Te,"-"+o(n.label_title)+" %",1)):u("",!0),s("div",He,o(n.label_title),1)])):u("",!0)]))),128)):u("",!0)])):u("",!0),s("h2",Le,[E(f,{to:e.item.url_without_domain},{default:R(()=>[C(o(e.item.title),1)]),_:1},8,["to"])]),s("div",ze,[s("div",Be,[s("strong",null,o(c(a).labels.id)+":",1),C(" "+o(e.item.code),1)]),e.item.category_title?(t(),i("div",Pe,[E(f,{to:e.item.category_url_without_domain},{default:R(()=>[C(o(e.item.category_title),1)]),_:1},8,["to"])])):u("",!0),e.item.logistic_class?(t(),i("div",Fe,o(e.item.logistic_class),1)):u("",!0),e.item.product_condition&&e.item.product_condition!="n"?(t(),i("div",Oe,[s("div",Re,[s("span",{innerHTML:c(a).labels["condition_"+e.item.product_condition]},null,8,Se)])])):u("",!0)])]),s("div",De,[E(f,{class:"cp-no-image",to:e.item.url_without_domain},{default:R(()=>m[3]||(m[3]=[s("img",{src:ae,alt:""},null,-1)])),_:1},8,["to"]),s("div",{class:x(["cp-available-qty cp-available-qty-m",[q.value]])},[e.item.status=="5"&&e.item.date_available_humanize?(t(),i($,{key:0},[C(o(e.item.date_available_humanize)+" ("+o(e.item.user_warehouse_available_qty)+") ",1)],64)):(t(),i($,{key:1},[C(o(e.item.user_warehouse_available_qty)+" ("+o(e.item.user_nearby_available_qty)+") ",1),e.item.status!="9"&&e.item.status2!="4"?(t(),i($,{key:0},[C("("+o(parseFloat(e.item.available_qty_supplier).toFixed(0))+")",1)],64)):u("",!0)],64))],2)])])]),s("div",Qe,[s("div",je,[e.item.price_custom>0?(t(),i("div",{key:0,class:x(["cp-price",{"uau-badge":e.item.selected_price&&e.item.selected_price=="uau"}])},[(e.item.type=="advanced"||e.item.type=="configurable")&&e.item.basic_price_custom>e.item.price_custom?(t(),i("div",Ie,[s("span",Ve,o(c(a).labels.price_variation),1),s("span",null,o(c(h)(e.item.price_custom)),1)])):(t(),i($,{key:1},[(e.item.discount_percent_custom>0||e.item.price_custom<e.item.basic_price_custom)&&e.item.selected_price!="promotion2"?(t(),i($,{key:0},[s("div",Ke,o(c(h)(e.item.basic_price_custom)),1),s("div",Ue,[C(" (-"+o(e.item.discount_percent_custom)+"%) "+o(c(h)(e.item.price_custom))+" ",1),M.value?(t(),i("span",{key:0,class:"cp-installments-price",innerHTML:c(a).labels.pa_installments_price_text.replace("%PRICE%",c(h)(M.value))},null,8,Ne)):u("",!0)])],64)):(t(),i("div",Ze,[C(o(c(h)(e.item.price_custom))+" ",1),M.value?(t(),i("span",{key:0,class:"cp-installments-price",innerHTML:c(a).labels.pa_installments_price_text.replace("%PRICE%",c(h)(M.value))},null,8,Ge)):u("",!0)])),e.item.discount_expire?(t(),i("div",Je,[e.item.discount_expire?(t(),i("span",{key:0,innerHTML:c(a).labels.pa_discount_expire.replace("%d%",c(d)(e.item.discount_expire))},null,8,We)):u("",!0)])):u("",!0),e.item.loyalty_price_custom?(t(),i("div",Ye,[s("span",null,[C(o(c(h)(e.item.loyalty_price_custom))+" ",1),M.value?(t(),i("span",{key:0,class:"cp-installments-price",innerHTML:c(a).labels.pa_installments_price_text.replace("%PRICE%",c(h)(M.value))},null,8,Xe)):u("",!0)])])):u("",!0)],64))],2)):u("",!0),e.item.badges?(t(!0),i($,{key:1},L(e.item.badges,n=>(t(),i($,{key:n.code},[n.category==2&&n.label_title_hover?(t(),i("div",{key:0,onMouseover:m[0]||(m[0]=b=>ee(l)?l.value=!0:l=!0),onMouseleave:m[1]||(m[1]=b=>ee(l)?l.value=!1:l=!1),onClick:b=>_(n.label_title_hover),class:"cp-badge-coupon"},[n.label_title?(t(),i("span",tt,[s("span",null,o(n.label_title),1)])):u("",!0),n.label_title_hover?(t(),i("div",it,[s("span",st,o(n.label_title_hover),1),s("span",at,[c(l)?(t(),i("span",lt,o(c(v)),1)):u("",!0)])])):u("",!0)],40,et)):u("",!0)],64))),128)):u("",!0),(B=e.item)!=null&&B.payment_options?(t(),i("div",ot,[(t(!0),i($,null,L((H=e.item)==null?void 0:H.payment_options,(n,b,T)=>(t(),i("div",{key:T,class:"cp-payments-option"},[s("span",{innerHTML:c(a).labels["payment_"+b]?c(a).labels["payment_"+b]:[b]},null,8,nt),m[4]||(m[4]=C()),n.club_only?(t(),i("span",{key:0,innerHTML:c(a).labels.payment_option_club_only},null,8,ct)):u("",!0)]))),128))])):u("",!0)]),s("div",rt,[e.item.is_available&&!e.item.variation_total?(t(),i($,{key:0},[e.item.type=="advanced"||e.item.type=="configurable"?(t(),V(f,{key:0,class:"btn btn-green cp-btn-addtocart cp-btn-details",to:e.item.url_without_domain},{default:R(()=>[C(o(c(a).labels[g.value]),1)]),_:1},8,["to"])):(t(),i("a",{key:1,class:"btn btn-green cp-btn-addtocart",onClick:m[2]||(m[2]=n=>j())},o(c(a).labels[g.value]),1))],64)):(t(),V(f,{key:1,to:e.item.url_without_domain,class:"btn btn-green cp-btn-addtocart cp-btn-details",title:c(a).labels.read_more},null,8,["to","title"]))])])]),s("div",ut,[s("figure",dt,[E(f,{to:e.item.url_without_domain},{default:R(()=>[E(ie,{loading:"lazy",data:e.item.main_image_upload_path_thumb},null,8,["data"])]),_:1},8,["to"])]),s("div",{class:x(["cp-available-qty",[q.value]])},[e.item.status=="5"&&e.item.date_available_humanize?(t(),i($,{key:0},[C(o(e.item.date_available_humanize)+" ("+o(e.item.user_warehouse_available_qty)+") ",1)],64)):(t(),i($,{key:1},[C(o(e.item.user_warehouse_available_qty)+" ("+o(e.item.user_nearby_available_qty)+") ",1),e.item.status!="9"&&e.item.status2!="4"?(t(),i($,{key:0},[C("("+o(parseFloat(e.item.available_qty_supplier).toFixed(0))+")",1)],64)):u("",!0)],64))],2)])])}}},mt={value:""},vt={value:"rates"},ft={value:"new"},pt={value:"old"},yt={value:"expensive"},ht={value:"cheaper"},gt={value:"az"},bt={value:"za"},At={__name:"Sort",setup(e){const a=z(),k=O(),r=F(),p=w(k.query.sort?k.query.sort:""),{emit:h}=J();function d(){const v={...k.query,to_page:void 0,sort:void 0};p.value&&(v.sort=p.value),r.push({query:v}),h("filter",{sort:p.value})}const y=Z("m");U(()=>[k.query.sort,y.value],([v,_])=>{p.value=v||"",l(y.value)}),G(()=>l(y.value));function l(v){const _=document.querySelector(".c-toolbar-sort-container"),q=document.querySelector(".c-items"),g=document.querySelector(".c-toolbar");v?q.prepend(_):g.append(_)}return(v,_)=>S((t(),i("select",{onChange:_[0]||(_[0]=q=>d()),"onUpdate:modelValue":_[1]||(_[1]=q=>p.value=q)},[s("option",mt,o(c(a).labels.ordering_priority),1),s("option",vt,o(c(a).labels.ordering_top_rated),1),s("option",ft,o(c(a).labels.ordering_recent),1),s("option",pt,o(c(a).labels.ordering_older),1),s("option",yt,o(c(a).labels.ordering_expensive),1),s("option",ht,o(c(a).labels.ordering_cheaper),1),s("option",gt,o(c(a).labels.ordering_az),1),s("option",bt,o(c(a).labels.ordering_za),1)],544)),[[ce,p.value]])}},kt={__name:"FilterWithQty",setup(e){const a=z(),k=F(),r=O(),{emit:p}=J();function h(){const d={...r.query,to_page:void 0,with_qty:r.query.with_qty?void 0:1};k.push({query:d}),p("filter",{with_qty:r.query.with_qty?void 0:1})}return(d,y)=>(t(),i("a",{class:x({active:c(r).query.with_qty}),onClick:y[0]||(y[0]=l=>h())},o(c(a).labels.pa_with_qty),3))}},qt=D(kt,[["__scopeId","data-v-6ceb74b1"]]),$t={__name:"FilterDiscount",setup(e){const a=z(),k=F(),r=O(),{emit:p}=J();function h(){const d={...r.query,to_page:void 0,discount:r.query.discount?void 0:1};k.push({query:d}),p("filter",{discount:r.query.discount?void 0:1})}return(d,y)=>(t(),i("a",{class:x(["discount red c-btn-filter",{active:c(r).query.discount}]),onClick:y[0]||(y[0]=l=>h())},o(c(a).labels.pa_discounted_products),3))}},wt=D($t,[["__scopeId","data-v-d1be6c41"]]),xt={class:"c-legend-tooltip"},Ct={class:"c-lt-header"},Et={class:"c-lt-header-title"},Mt={key:0,class:"c-lt-top"},Tt={key:0,class:"c-lt-icon c-lt-img"},Ht={key:2},Lt={key:1,class:"c-lt-bottom"},zt={class:"c-lte-item-col1"},Bt=["innerHTML"],Pt=["innerHTML"],Ft={__name:"Legend",setup(e){const a=z(),k=ue(),r=w(),p=w(),h=w(0);return G(async()=>{await re({url:k.endpoints.value._get_hapi_rotator_elements+"?lang=si&code=pa_legend,pa_legend_extra&limit=20"}).then(d=>{var y;if((y=d.data)!=null&&y.length){const l=d.data.find(_=>_.code=="pa_legend");l&&(r.value=l.items);const v=d.data.find(_=>_.code=="pa_legend_extra");v&&(p.value=v.items)}})}),(d,y)=>r.value||p.value?(t(),i("div",{key:0,class:x(["c-legend",{active:h.value}])},[s("div",{class:"c-legend-label",onClick:y[0]||(y[0]=l=>h.value=!h.value)},[s("span",null,o(c(a).labels.pa_legend),1)]),s("div",xt,[s("div",Ct,[s("div",Et,o(c(a).labels.pa_legend),1),y[1]||(y[1]=s("div",{class:"c-lt-close"},null,-1))]),r.value?(t(),i("div",Mt,[(t(!0),i($,null,L(r.value,l=>(t(),i("div",{class:"c-lt-item",key:l.id},[l.image?(t(),i("div",Tt,[E(ie,{width:"13",height:"13",src:"/upload/"+l.image},null,8,["src"])])):l.element_color_4?(t(),i("div",{key:1,class:x(["c-lt-icon","c-lt-icon-"+l.id]),style:te({backgroundColor:l.element_color_4})},null,6)):u("",!0),l.title?(t(),i("span",Ht,o(l.title),1)):u("",!0)]))),128))])):u("",!0),p.value?(t(),i("div",Lt,[(t(!0),i($,null,L(p.value,l=>(t(),i("div",{class:"c-lte-item",key:l.id},[s("div",zt,[y[2]||(y[2]=s("div",{class:"c-lte-img"},[s("img",{src:ae,alt:""})],-1)),l.element_content_small?(t(),i("div",{key:0,class:x(["c-lte-item-qty","c-lte-item-qty-"+l.id]),style:te({background:l.element_color_4}),innerHTML:l.element_content_small},null,14,Bt)):u("",!0)]),l.content?(t(),i("div",{key:0,class:x(["c-lte-item-col2","c-lte-item-col2-"+l.id]),innerHTML:l.content},null,10,Pt)):u("",!0)]))),128))])):u("",!0)])],2)):u("",!0)}},Ot=D(Ft,[["__scopeId","data-v-90fc9784"]]),Rt={class:"c-toolbar"},St={class:"ci-checkbox-container"},Dt={class:"ci-checkbox ci-discount"},Qt={class:"ci-checkbox ci-available"},jt={class:"c-toolbar-sort-container"},It={class:"sort c-sort"},Vt={__name:"Toolbar",setup(e){return z(),(a,k)=>(t(),i("div",Rt,[s("div",St,[s("div",Dt,[E(wt)]),s("div",Qt,[E(qt)])]),E(Ot),s("div",jt,[s("div",It,[E(At)])])]))}},Kt=D(Vt,[["__scopeId","data-v-7abbb855"]]),Ut={__name:"LoadMore",props:["pagination","autoload"],emits:["loadmore"],setup(e,{emit:a}){const k=z(),r=e,p=a,h=O(),d=F(),y=w(null),l=de(y),v=w(0);function _(){d.push({query:{...h.query,to_page:r.pagination.page.next}}),p("loadmore",r.pagination.page.next)}return U(l,q=>{r.autoload&&q&&v.value<r.autoload&&(_(),v.value++)}),(q,g)=>{var M;return e.pagination&&((M=e.pagination.page)!=null&&M.next)?(t(),i("div",{key:0,class:"load-more-container c-load-more-container",ref_key:"target",ref:y},[s("a",{class:"btn btn-white btn-medium load-more btn-load-more",onClick:_},o(c(k).labels.pa_load_more_catalog),1)],512)):u("",!0)}}},Nt=D(Ut,[["__scopeId","data-v-9584ff97"]]),Zt=["onClick"],Gt={class:"title"},Jt={key:0,class:"toggle-icon"},Wt={key:0,class:"ci-counter"},Yt={key:0},Xt={key:0,class:"ci-counter"},ei={__name:"SidebarCategories",props:["items","current","level"],setup(e){const a=e,k=z(),r=w();let p=w();const h=O(),d=a.current.toString().split(".");d.pop(),r.value=d.join(".");function y(){const l=window.location.pathname;p.value=l}return G(()=>{a.items.children&&a.items.children.forEach(l=>{(l.position_h==a.current||l.position_h==r.value)&&(l.active=!0)}),y()}),U(()=>h.path,async()=>y()),(l,v)=>{const _=Y("router-link"),q=Y("SidebarCategories",!0);return e.items.children?(t(),i("ul",{key:0,class:x({"ci-item-wrapper cf-item-wrapper":e.level==0})},[(t(!0),i($,null,L(e.items.children,g=>(t(),i("li",{key:g.id,class:x({"has-children":g.children,active:g.active})},[g.children?(t(),i("a",{key:0,onClick:M=>g.active=!g.active},[s("span",Gt,o(g.title),1),g.children?(t(),i("span",Jt)):u("",!0)],8,Zt)):(t(),V(_,{key:1,to:g.url_without_domain},{default:R(()=>[s("span",{class:x(["title",{blue:c(p)==g.url_without_domain}])},o(g.title),3),g.total&&!g.children&&g.level>2?(t(),i("span",Wt,o(g.total),1)):u("",!0)]),_:2},1032,["to"])),S(E(q,{current:e.current,items:g,level:"1"},null,8,["current","items"]),[[_e,g.children]])],2))),128)),e.level!=0?(t(),i("li",Yt,[E(_,{to:e.items.url_without_domain},{default:R(()=>[C(o(c(k).labels.show_all)+" ",1),e.items.total?(t(),i("span",Xt,o(e.items.total),1)):u("",!0)]),_:1},8,["to"])])):u("",!0)],2)):u("",!0)}}},ti={class:"cf-item-wrapper"},ii={key:0,class:"cf-row"},si={class:"cf-range"},ai={class:"cf-slider-input"},li=["step","min","max"],oi=["step","min","max"],ni={class:"cf-slider"},ci={class:"cf-slider-range"},ri=["step","min","max"],ui=["step","min","max"],di=["id","name","value","onChange"],_i=["for"],mi={key:0,class:"cp-rate rates-container"},vi=["innerHTML"],fi={key:0,class:"cf-counter"},pi={__name:"Filter",props:["filter","searchPage"],emits:["filter"],setup(e,{emit:a}){const k=z();se();const r=w([]),p=F(),h=O(),d=e,y=a;let l=w();const v=Z("m");function _(){l.value==!1?l.value=!0:l.value=!1}G(()=>{l.value=0});const q=w(d.filter.options_config&&d.filter.options_config.min_value?Math.round(d.filter.options_config.min_value):0),g=w(d.filter.options_config&&d.filter.options_config.max_value?Math.round(d.filter.options_config.max_value):0);function M(){if(r.value=d.filter.options_selected?d.filter.options_selected:[],d.filter.layout=="sf"){const A=d.filter.options_selected?d.filter.options_selected[0].split("-"):[Math.round(d.filter.options_config.min_value),Math.round(d.filter.options_config.max_value)];A!=null&&(q.value=A[0],g.value=A[1])}}function Q(){v.value&&(l.value=0)}me(()=>{Q(v.value),M(d.filter.options)});function j(A,m){let f=d.filter.layout=="sf"?q.value+"-"+g.value:r.value;p.push({query:{...h.query,[d.filter.filter_url]:f,to_page:void 0}}),y("filter"),window.scrollTo({top:0,behavior:"smooth"})}return(A,m)=>e.filter.code=="category"&&e.searchPage==null?(t(),i($,{key:0},[],64)):(t(),i("div",{key:1,class:x(["cf-item","cf-item-"+e.filter.code,{active:c(l)}])},[s("div",{onClick:m[0]||(m[0]=f=>_(e.filter)),class:"cf-title"},[C(o(e.filter.label),1),m[7]||(m[7]=s("span",{class:"toggle-icon"},null,-1))]),s("div",ti,[e.filter.layout=="sf"?(t(),i("div",ii,[s("div",si,[s("div",ai,[S(s("input",{type:"number",step:e.filter.options_config.step,min:e.filter.options_config.min_value,max:e.filter.options_config.max_value,"onUpdate:modelValue":m[1]||(m[1]=f=>q.value=f)},null,8,li),[[N,q.value]]),m[8]||(m[8]=s("span",{class:"cf-slider-input-separator"},null,-1)),S(s("input",{type:"number",step:e.filter.options_config.step,min:e.filter.options_config.min_value,max:e.filter.options_config.max_value,"onUpdate:modelValue":m[2]||(m[2]=f=>g.value=f)},null,8,oi),[[N,g.value]])]),s("div",ni,[s("div",ci,[S(s("input",{type:"range",step:e.filter.options_config.step,min:e.filter.options_config.min_value,max:e.filter.options_config.max_value,"onUpdate:modelValue":m[3]||(m[3]=f=>q.value=f)},null,8,ri),[[N,q.value]]),S(s("input",{type:"range",step:e.filter.options_config.step,min:e.filter.options_config.min_value,max:e.filter.options_config.max_value,"onUpdate:modelValue":m[4]||(m[4]=f=>g.value=f)},null,8,ui),[[N,g.value]])])]),s("button",{onClick:m[5]||(m[5]=f=>j()),class:"btn btn-lightBlue cf-range-btn"},o(c(k).labels.confirm_filters),1)])])):(t(!0),i($,{key:1},L(e.filter.options,(f,B)=>(t(),i("div",{class:x(["cf-row",e.filter.code=="category"?"level-"+f.level:"",{"cf-row-not-available":f&&f.total_available==0}]),key:e.filter.code+"-"+f.code},[S(s("input",{type:"checkbox",id:"search-"+e.filter.code+"-"+f.code+B,name:e.filter.filter_url,value:f.filter_url,onChange:H=>j(f.level,f.position),"onUpdate:modelValue":m[6]||(m[6]=H=>r.value=H)},null,40,di),[[ve,r.value]]),s("label",{class:"",for:"search-"+e.filter.code+"-"+f.code+B},[e.filter.code=="rates"?(t(),i("span",mi,[(t(),i($,null,L(5,H=>s("span",{key:H,class:x(["icon-star-empty",{"icon-star":f.code>=H}])},null,2)),64))])):u("",!0),s("span",{innerHTML:f.title},null,8,vi)],8,_i),f&&f.total_available>0?(t(),i("span",fi,o(f.total_available),1)):u("",!0)],2))),128))])],2))}},yi=D(pi,[["__scopeId","data-v-b477763a"]]),hi={class:"cf-active"},gi=["onClick"],bi={class:"cf-active-item btn-cf-active-clear"},Ai={__name:"ActiveFilters",props:["data"],emits:["remove"],setup(e,{emit:a}){const k=z(),r=F(),p=O(),h=a;async function d(l,v){const _=JSON.parse(JSON.stringify(p.query));if(typeof _[l]=="string")_[l]=void 0;else{const q=_[l].indexOf(v);_[l].splice(q,1)}_.to_page=void 0,r.push({query:_}),h("remove")}async function y(){const l={};p.query.sort&&(l.sort=p.query.sort),p.query.search_q&&(l.search_q=p.query.search_q),r.push({query:l}),h("remove")}return(l,v)=>(t(),i("div",hi,[(t(!0),i($,null,L(e.data.selected,_=>(t(),i("div",{key:_.id,onClick:q=>d(_.attribute_slug,_.slug),class:"cf-active-item"},[s("span",null,o(_.title),1)],8,gi))),128)),e.data.selected&&e.data.selected.length>1?(t(),i("div",{key:0,onClick:v[0]||(v[0]=_=>y()),class:"cf-active-btn-section"},[s("div",bi,[s("span",null,o(c(k).labels.pa_clear_filtering),1)])])):u("",!0)]))}},ki=D(Ai,[["__scopeId","data-v-63d00531"]]),qi={class:"c-header"},$i={key:0,class:"c-title"},wi={class:"c-row"},xi={class:"c-filters-section"},Ci={class:"cf"},Ei={key:0,class:"ci-title cf-title"},Mi={class:"c-items-section"},Ti={key:1,class:"c-empty"},zi={__name:"Category",setup(e){const a=O();F();const k=z(),{generateThumbs:r}=fe(),p=se(),h=w([]),d=w([]),y=w([]),l=w([]),v=w([]),_=w(1),q=w({}),{bus:g}=J(),M=Z("m"),Q=I(()=>{var b,T;let n="Izdelki";return(b=a.query)!=null&&b.search_q&&(n=a.query.search_q),(T=v.value)!=null&&T.title&&(n=v.value.title),document.title=n,n});function j(n){f({page:n})}async function A(){h.value=[],a.params.category&&await p.fetchCategory({slug:a.params.category.join("/"),mode:"full"}).then(n=>{v.value=n.data[0]})}const m=I(()=>{if(!a.query.search_q){const n=v.value.position_h;let b=n&&n.includes(".")?n.split(".")[0]:n;return p.categories.find(T=>T.position_h==b)}return null});async function f(n={}){_.value=1,a.query&&Object.entries(a.query).forEach(b=>{const T=typeof b[1]!="string"?b[1].join(","):b[1];b[0]!="to_page"&&(n[b[0]]=T)}),n.to_page&&(n.to_page=n.to_page),n.page&&(n.page=n.page),n.mode="index",!a.query.search_q&&v.value.id&&(n.category_position=v.value.position_h,n._category_id=v.value.id,n._search_id=v.value.search_id?v.value.search_id:1),a.query.search_q&&(n.search_q=a.query.search_q,n._search_id=!0),a.query.sort&&(n.sort=a.query.sort),a.query.with_qty&&(n.with_qty=a.query.with_qty),a.query.discount&&(n.discount=a.query.discount),await p.fetchProducts(n).then(async b=>{var T,K,P,W,X;q.value=((K=(T=b.data)==null?void 0:T.meta_data)==null?void 0:K.pagination)||{},d.value=((P=b.data)==null?void 0:P.items)||[],y.value=(W=b.data)==null?void 0:W.search_fields,l.value=(X=b.data)==null?void 0:X.search_fields_meta_data,b.data.items&&(n.filter?h.value=b.data.items:b.data.items.forEach(le=>{h.value.push(le)})),_.value=0,k.loading=0,await r({data:d.value,preset:"catalogEntry"})})}async function B(){k.loading=1,await A();const n={};a.query.to_page&&(n.to_page=a.query.to_page),await f(n)}B(),U(()=>a.path,async(n,b)=>{(a.name==="category"||a.name==="products"||a.name==="search")&&await B()});function H(){setTimeout(async()=>{await f({filter:!0})},300)}return U(()=>g.value,()=>{["search","filter"].includes(g.value.event)&&H()}),(n,b)=>{var T,K;return t(),i($,null,[s("div",qi,[Q.value?(t(),i("h1",$i,o(Q.value),1)):u("",!0),E(Kt)]),s("div",wi,[s("div",xi,[s("div",Ci,[m.value?(t(),i("div",{key:0,class:x(["ci-categories",{active:!c(M)}])},[(T=m.value)!=null&&T.title?(t(),i("div",Ei,o(m.value.title),1)):u("",!0),E(ei,{items:m.value,current:v.value.position_h,level:"0"},null,8,["items","current"])],2)):u("",!0),(t(!0),i($,null,L(y.value,P=>(t(),V(yi,{key:P.id,filter:P,searchPage:c(a).query.search_q,onFilter:b[0]||(b[0]=W=>H())},null,8,["filter","searchPage"]))),128))])]),s("div",Mi,[l.value&&((K=l.value.selected)!=null&&K.length)?(t(),V(ki,{key:0,data:l.value,onRemove:b[1]||(b[1]=P=>H())},null,8,["data"])):u("",!0),s("div",{class:x(["fz0 c-items",{loading:_.value}])},[(t(!0),i($,null,L(h.value,P=>(t(),V(_t,{key:P.id,item:P},null,8,["item"]))),128))],2),E(Nt,{onLoadmore:j,class:x({loading:_.value}),autoload:"2",pagination:q.value},null,8,["class","pagination"]),!_.value&&!h.value.length?(t(),i("div",Ti,o(c(k).labels.no_products),1)):u("",!0)])])],64)}}};export{zi as default};
