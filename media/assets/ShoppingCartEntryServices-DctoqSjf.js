import{u as I,a as T,z as B,c as g,x,M as H,e as c,b as o,f as s,t as f,j as h,k as v,F as L,r as N,n as b,w as k,a0 as D,V as U,h as q,W as z}from"./index-715AKfMZ.js";const F={key:0,class:"cd-extra-benefits-header"},R={class:"cd-subtitle cd-extra-benefits-title"},W={class:"cd-extra-benefit-row"},j=["name","value","id"],P=["name","value","id"],Q=["for"],$={class:"cd-extra-benefit-title"},A=["onClick"],G=["innerHTML"],J={class:"cd-extra-benefit-row"},K=["checked","name","id"],O=["for"],X={class:"cd-extra-benefit-title"},Z={__name:"ShoppingCartEntryServices",props:["item","mode"],setup(i){const S=I(),V=T(),M=B(),{formatCurrency:E}=z(),l=i,u=g(()=>{let e=null;return l.mode=="itemDetail"?e=l.item.service_simple:e=l.item.services,e}),m=g(()=>{let e=null;return l.mode=="itemDetail"?e=l.item.insurance_simple:e=l.item.insurances,e}),_=g(()=>{let e=m.value?m.value.available:[],a=[];u.value&&u.value.available&&u.value.available.forEach(r=>{var n;(n=r.options)!=null&&n.selected_value||a.push(r)});const t=a?e.concat(a):e;return w.value=t.filter(r=>r.category_type==="s").length,t}),d=x(null),p=x([]);let w=x();H(()=>l.item,()=>{setTimeout(()=>{C()},100)},{deep:!0});function C(){if(d.value=m.value&&m.value.selected?m.value.selected[0].id:null,u.value&&u.value.selected){let e=[];u.value.selected.forEach(a=>{e.push(a.id)}),p.value=e}}C();function y(){let e=[];d.value!=null&&e.push(d.value),p.value.forEach(a=>e.push(a)),l.mode=="itemDetail"?V.servicesSelected=e:M.updateProduct({shopping_cart_code:l.item.shopping_cart_code,quantity:l.item.quantity,services:e.length?e:[""]})}return(e,a)=>typeof _.value<"u"&&_.value.length>0?(c(),o("div",{key:0,class:b({"cd-extra-benefits cd-col2-box":i.mode=="itemDetail","wp-extra-benefits":i.mode!="itemDetail"})},[i.mode=="itemDetail"?(c(),o("div",F,[s("span",R,f(h(S).labels.pa_services),1)])):v("",!0),(c(!0),o(L,null,N(_.value,(t,r)=>(c(),o("div",{key:r,class:b(["cd-extra-benefit-item",{active:t.desc_active,"wp-extra-benefit-item":i.mode!="itemDetail"}])},[s("div",W,[t.category_type=="s"?k((c(),o("input",{key:0,onChange:y,"onUpdate:modelValue":a[0]||(a[0]=n=>d.value=n),type:"radio",name:"service-"+i.item.shopping_cart_code+"[]",value:t.id,id:"service-"+i.item.shopping_cart_code+"-"+t.id},null,40,j)),[[D,d.value]]):k((c(),o("input",{key:1,onChange:y,"onUpdate:modelValue":a[1]||(a[1]=n=>p.value=n),type:"checkbox",name:"service-"+i.item.shopping_cart_code+"[]",value:t.id,id:"service-"+i.item.shopping_cart_code+"-"+t.id},null,40,P)),[[U,p.value]]),s("label",{class:"cd-extra-benefit",for:"service-"+i.item.shopping_cart_code+"-"+t.id},[s("div",$,[s("span",null,f(t.title),1)])],8,Q),s("div",{class:b(["cd-extra-benefit-price",{"wp-extra-benefit-price":i.mode!="itemDetail"}])},[a[3]||(a[3]=q(" + ")),s("span",null,f(h(E)(t.price)),1)],2),t.description?(c(),o("span",{key:2,class:"cd-extra-benefit-icon",onClick:n=>t.desc_active=!t.desc_active},null,8,A)):v("",!0)]),t.description?(c(),o("div",{key:0,class:"cd-extra-benefit-desc",innerHTML:t.description},null,8,G)):v("",!0),r===h(w)-1&&m.value?(c(),o("div",{key:1,class:b(["cd-extra-benefit-item",{"wp-extra-benefit-item":i.mode!="itemDetail"}])},[s("div",J,[k(s("input",{checked:d.value==null,onChange:y,"onUpdate:modelValue":a[2]||(a[2]=n=>d.value=n),type:"radio",value:"",name:"service-"+i.item.shopping_cart_code+"[]",id:"service-"+i.item.shopping_cart_code+"-0"},null,40,K),[[D,d.value]]),s("label",{class:"cd-extra-benefit",for:"service-"+i.item.shopping_cart_code+"-0"},[s("div",X,f(h(S).labels.pa_no_warranty),1)],8,O)])],2)):v("",!0)],2))),128))],2)):v("",!0)}};export{Z as _};
