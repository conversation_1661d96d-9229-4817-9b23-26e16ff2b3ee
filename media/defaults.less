/*------- mixins -------*/
.transition (@element: color, @speed: .3s) {
	transition: @arguments;
	-webkit-backface-visibility: hidden;
	-webkit-tap-highlight-color: transparent;
}
.grayscale (@bw: 100%) {
	filter: grayscale(@bw);
	-webkit-filter: grayscale(@bw);
	-moz-filter: grayscale(@bw);
}
.text-shadow (@color, @x: 1px, @y: 1px, @blur: 0px) {
	text-shadow: @x @y @blur @color;
}
.gradient(@startColor: #eee, @endColor: white) {
	background: @startColor;
	background-image: linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -webkit-linear-gradient(180deg, @startColor 0%, @endColor 100%);
	background-image: -moz-linear-gradient(@startColor 0%, @endColor 100%);
	background-image: -ms-linear-gradient(180deg, @startColor 0%, @endColor 100%);
}
.horizontal-gradient (@startColor: #eee, @endColor: white) {
 	background-color: @startColor;
	background-image: -webkit-linear-gradient(90deg, @startColor, @endColor);
	background-image: -ms-linear-gradient(90deg, @startColor, @endColor);
	background-image: linear-gradient(90deg, @startColor, @endColor);
}
.font(@fontSize, @fontLineHeight, @fontType) {
	font-size: @fontSize;
	line-height: @fontLineHeight; 
	font-family: @fontType;
}
.pseudo(@width, @height) {
	content:"";
	position: absolute;
	display: block;
	width:@width;
	height:@height;
}
.animation (@name, @duration: 300ms, @delay: 0, @ease: ease) {
	-webkit-animation: @name @duration @delay @ease;
	-moz-animation:    @name @duration @delay @ease;
	-ms-animation:     @name @duration @delay @ease;
}
.transform(@string){
	transform: @string;
	-webkit-transform: @string;
	-ms-transform: 		 @string;
}
.scale (@factor) {
	transform: scale(@factor);
	-webkit-transform: scale(@factor);
	-ms-transform: 		 scale(@factor);
}
.scaleX(@factor) {
	transform: scaleX(@factor);
	-webkit-transform: scaleX(@factor);
	-ms-transform: 		 scaleX(@factor);	
}
.scaleY (@factor) {
	transform: scaleY(@factor);
	-webkit-transform: scaleY(@factor);
	-ms-transform: scaleY(@factor);
}
.rotate (@deg) {
	transform: rotate(@deg);
	-webkit-transform: rotate(@deg);
	-ms-transform: 		 rotate(@deg);
}
.skew (@deg, @deg2) {
	transform: skew(@deg, @deg2);
	-webkit-transform: skew(@deg, @deg2);
	-ms-transform: skew(@deg, @deg2);
}
.translate (@x, @y:0) {
	transform: translate(@x, @y);
	-webkit-transform: translate(@x, @y);
	-ms-transform: translate(@x, @y);
}
.translate3d (@x, @y: 0, @z: 0) {
	transform: translate3d(@x, @y, @z);
	-webkit-transform: translate3d(@x, @y, @z);
	-ms-transform: translate3d(@x, @y, @z);
}
.perspective (@value: 1000) {
	perspective: 		@value;
	-webkit-perspective: 	@value;
}
.clear { 
	clear:both;
	&:before, &:after {content:""; display: table; }
	&:after { clear: both; }
}
.placeholder(@color,@focusColor) {
	&::-webkit-input-placeholder { color: @color; }
	&:-ms-input-placeholder { color: @color; }
	&::-moz-placeholder { color: @color; }
	&:focus {
		&::-webkit-input-placeholder { color: @focusColor; }
		&:-ms-input-placeholder { color: @focusColor; }
		&::-moz-placeholder { color: @focusColor; }
	}
}
.shadow { content:""; position: absolute; left: 35px; right: 35px; top: 20px; bottom: 5px; box-shadow: 0px 10px 65px rgba(0,0,0,.6); }
.lloader {
	background: #fff url(images/loader.svg) no-repeat center center; 
	img { opacity: 0; .transition(opacity); }
	&.loaded {
		background: #fff;
		img { opacity: 1; }
	}
}
/*------- /mixins -------*/


/*------- fonts -------*/
/*
@font-face {
	font-family: 'OpenSans';
	src: url('fonts/opensans-bold.eot');
	src: local('☺'), url('fonts/opensans-bold.woff') format('woff'), url('fonts/opensans-bold.ttf') format('truetype'), url('fonts/opensans-bold.svg#webfontQ0ewW9f7') format('svg');
	font-weight: normal;
	font-style: normal;
}
*/
@font-face {
    font-family: 'icomoon';
    src: url('fonts/icomoon.woff?4vq4o9') format('woff');
    font-weight: normal;
    font-style: normal;
}
/*------- /fonts -------*/

/*------- icons -------*/
.icon-upload2 {
  content: "\e934";
}
.icon-merge {
  content: "\e933";
}
.icon-delivery {
  content: "\e932";
}
.icon-user-loyalty {
  content: "\e931";
}
.icon-location {
  content: "\e930";
}
.icon-condition {
  content: "\e92f";
}
.icon-seller {
  content: "\e92e";
}
.icon-reset {
  content: "\e92d";
}
.icon-service {
  content: "\e92c";
}
.icon-eancode {
  content: "\e92b";
}
.icon-face-scan {
  content: "\e92a";
}
.icon-trolley-cart {
  content: "\e929";
}
.icon-truck {
  content: "\e928";
}
.icon-split {
  content: "\e927";
}
.icon-credit-card {
  content: "\e926";
}
.icon-sort {
  content: "\e925";
}
.icon-userlist {
  content: "\e924";
}
.icon-upload {
  content: "\e923";
}
.icon-check-special {
  content: "\e922";
}
.icon-sms {
  content: "\e921";
}
.icon-sheet {
  content: "\e920";
}
.icon-share {
  content: "\e91f";
}
.icon-scan {
  content: "\e91e";
}
.icon-eye {
  content: "\e91d";
}
.icon-arrows {
  content: "\e91c";
}
.icon-mail {
  content: "\e91b";
}
.icon-wp {
  content: "\e91a";
}
.icon-coupon {
  content: "\e919";
}
.icon-viber {
  content: "\e918";
}
.icon-calendar {
  content: "\e917";
}
.icon-barcode {
  content: "\e916";
}
.icon-arrow-down {
  content: "\e915";
}
.icon-user-club {
  content: "\e914";
}
.icon-user-active {
  content: "\e913";
}
.icon-trash {
  content: "\e912";
}
.icon-user-empty {
  content: "\e911";
}
.icon-cart-empty {
  content: "\e910";
}
.icon-edit {
  content: "\e90f";
}
.icon-user {
  content: "\e90e";
}
.icon-arrow {
  content: "\e90d";
}
.icon-gift {
  content: "\e90c";
}
.icon-loyalty {
  content: "\e90b";
}
.icon-x {
  content: "\e90a";
}
.icon-settings {
  content: "\e909";
}
.icon-search {
  content: "\e908";
}
.icon-pin {
  content: "\e907";
}
.icon-logout {
  content: "\e906";
}
.icon-info {
  content: "\e905";
}
.icon-danger-white {
  content: "\e904";
}
.icon-danger-red {
  content: "\e903";
}
.icon-compare {
  content: "\e902";
}
.icon-cart {
  content: "\e900";
}
.icon-check {
  content: "\e901";
}
/*------- /icons -------*/

/*------- site vars -------*/
@l: 1600px;
@t: 1400px;
@m: 980px;
@ms: 760px;
@fancyboxT: 1220px;
@fancyboxM: 920px;
@iframeT: 860px;
@iframeM: 600px;
@iframeMs: 450px;

@pageWidth: 990px;
@fontSize: 16px;
@lineHeight: 1.4;
@textColor: #333333;
@borderRadius: 2px;

@linkColor: #333333;
@linkHoverColor: #333333;

@primaryColor: #333333;
@secondaryColor: #0078BA;
@red: #C9221B;
@blue: #0078BA;
@black: #333333;
@gray: #DBE3EA;
@orange: #F0A547;
@white: #fff;
@green: #0092A0;
@green2: #6EC93E;
@yellow: #F4BB0A;
@borderColor: @gray;

@font: "OpenSans", Arial, Helvetica, sans-serif;
@fonti: "icomoon";

@errorColor: @red;
@warningColor: @orange;
@successColor: @green;
/*------- /site vars -------*/


/*
	LEGEND
	screen sizes: 

	- normalize
	- helpers
	- tables
	- selectors
	- forms
	- info messages
	- buttons
	- navigation
*/