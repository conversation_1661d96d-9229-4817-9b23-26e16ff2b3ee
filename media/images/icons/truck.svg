<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="17px" viewBox="0 0 24 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>truck (1)</title>
    <defs>
        <filter x="-20.0%" y="-3.0%" width="140.0%" height="106.0%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="10" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="10" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0.131609488   0 0 0 0 0.263218976  0 0 0 0.08 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <filter id="filter-2">
            <feColorMatrix in="SourceGraphic" type="matrix" values="0 0 0 0 0.000000 0 0 0 0 0.470588 0 0 0 0 0.729412 0 0 0 1.000000 0"></feColorMatrix>
        </filter>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Košarica---odpreme---dostava-na-dom-2x" transform="translate(-179.000000, -1469.000000)">
            <g id="Group-27" filter="url(#filter-1)" transform="translate(15.000000, 15.000000)">
                <g id="Group-40" transform="translate(0.000000, 1256.000000)">
                    <g id="truck-(1)" transform="translate(15.000000, 93.000000)" filter="url(#filter-2)">
                        <g transform="translate(149.000000, 105.000000)">
                            <path d="M5.34746294,11.8646652 C4.08354645,11.8646652 3.0556995,12.8925122 3.0556995,14.1564286 C3.0556995,15.4203451 4.08354645,16.4481921 5.34746294,16.4481921 C6.61137942,16.4481921 7.63922637,15.4203451 7.63922637,14.1564286 C7.63922637,12.8925122 6.61133466,11.8646652 5.34746294,11.8646652 Z M5.34746294,15.684256 C4.50483702,15.684256 3.81963557,14.9990098 3.81963557,14.1564286 C3.81963557,13.3138027 4.50488179,12.6286013 5.34746294,12.6286013 C6.19004409,12.6286013 6.87529031,13.3138475 6.87529031,14.1564286 C6.87529031,14.9990098 6.19004409,15.684256 5.34746294,15.684256 Z" id="Shape" fill="#000000" fill-rule="nonzero"></path>
                            <path d="M18.3341075,11.8646652 C17.070191,11.8646652 16.042344,12.8925122 16.042344,14.1564286 C16.042344,15.4203451 17.070191,16.4481921 18.3341075,16.4481921 C19.598024,16.4481921 20.6258709,15.4203451 20.6258709,14.1564286 C20.6258709,12.8925122 19.598024,11.8646652 18.3341075,11.8646652 Z M18.3341075,15.684256 C17.4914816,15.684256 16.8062801,14.9990098 16.8062801,14.1564286 C16.8062801,13.3138027 17.4915263,12.6286013 18.3341075,12.6286013 C19.1767334,12.6286013 19.8619349,13.3138475 19.8619349,14.1564286 C19.8619349,14.9990098 19.1767334,15.684256 18.3341075,15.684256 Z" id="Shape" fill="#000000" fill-rule="nonzero"></path>
                            <path d="M22.8568937,9.74823062 L19.4192485,4.40076769 C19.3489737,4.29150608 19.2278952,4.22543883 19.0979988,4.22543883 L14.8964623,4.22543883 C14.6852351,4.22543883 14.5145167,4.39656015 14.5145167,4.60738449 L14.5145167,14.1563839 C14.5145167,14.3676111 14.6852351,14.5383743 14.8964623,14.5383743 L16.4242897,14.5383743 L16.4242897,13.7743935 L15.278408,13.7743935 L15.278408,4.9893749 L18.8894573,4.9893749 L22.1536983,10.0671528 L22.1536983,13.774483 L20.2438805,13.774483 L20.2438805,14.5383743 L22.5356439,14.5383743 C22.7468711,14.5383743 22.9175897,14.3676558 22.9175897,14.1564286 L22.9175897,9.95484742 C22.9175897,9.88188698 22.8966414,9.80968748 22.8568937,9.74823062 Z" id="Path" fill="#000000" fill-rule="nonzero"></path>
                            <path d="M16.8062801,9.19091136 L16.8062801,6.51720227 L19.8619796,6.51720227 L19.8619796,5.7532662 L16.4243345,5.7532662 C16.2131073,5.7532662 16.0423888,5.92438752 16.0423888,6.13521186 L16.0423888,9.57285701 C16.0423888,9.78408419 16.2131073,9.95480266 16.4243345,9.95480266 L22.153743,9.95480266 L22.153743,9.19086659 L16.8062801,9.19086659 L16.8062801,9.19091136 Z" id="Path" fill="#000000" fill-rule="nonzero"></path>
                            <path d="M14.8964623,1.55172975 L0.381945652,1.55172975 C0.171121321,1.55172975 0,1.72285107 0,1.9336754 L0,14.1564286 C0,14.3676558 0.171121321,14.5383743 0.381945652,14.5383743 L3.43764515,14.5383743 L3.43764515,13.7744382 L0.763936065,13.7744382 L0.763936065,2.31562105 L14.5145167,2.31562105 L14.5145167,13.7744382 L7.25723596,13.7744382 L7.25723596,14.5383743 L14.8964623,14.5383743 C15.1076895,14.5383743 15.278408,14.3676558 15.278408,14.1564286 L15.278408,1.9336754 C15.278408,1.72285107 15.1076895,1.55172975 14.8964623,1.55172975 Z" id="Path" fill="#000000" fill-rule="nonzero"></path>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="0.381945652" y="12.2466109" width="2.29176344" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="8.02117202" y="12.2466109" width="6.49334465" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="21.0078166" y="12.2466109" width="1.52782737" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="1.52782737" y="3.07955712" width="13.368635" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="4.96547252" y="13.7744382" width="1" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="17.9521618" y="13.7744382" width="1" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="1.52782737" y="0.0238576154" width="5.34746294" height="1"></rect>
                            <rect id="Rectangle" fill="#000000" fill-rule="nonzero" x="0" y="0.0238576154" width="1" height="1"></rect>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>