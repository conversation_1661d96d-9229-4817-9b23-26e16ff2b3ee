<?xml version="1.0" encoding="UTF-8"?>
<svg width="23px" height="21px" viewBox="0 0 23 21" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>cart-empty</title>
    <defs>
        <filter x="-36.2%" y="-107.4%" width="172.5%" height="314.8%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="5" dy="5" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="12.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0124284644   0 0 0 0 0.127449513   0 0 0 0 0.242470562  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="Desktop" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Product-details---export" transform="translate(-1506.000000, -795.000000)" fill="#FFFFFF">
            <g id="Group-42" transform="translate(1250.000000, 100.000000)">
                <g id="Group-30" transform="translate(156.000000, 679.000000)">
                    <g id="Group-14" filter="url(#filter-1)" transform="translate(69.000000, 0.000000)">
                        <g id="Group-28" transform="translate(31.000000, 16.000000)">
                            <path d="M7.3998435,13.457622 L7.40089484,13.457622 C7.40177096,13.457622 7.40264713,13.4574468 7.40352325,13.4574468 L19.6030142,13.4574468 C19.9033535,13.4574468 20.1674209,13.2582135 20.2499529,12.9694391 L22.9414423,3.54922638 C22.9994425,3.34613808 22.9587898,3.127805 22.83175,2.95923648 C22.7045351,2.79066796 22.5056523,2.69148936 22.2945035,2.69148936 L5.84803166,2.69148936 L5.36703304,0.526908117 C5.29851925,0.219033943 5.02551529,0 4.71010638,0 L0.67287234,0 C0.30121552,0 0,0.30121552 0,0.67287234 C0,1.04452916 0.30121552,1.34574468 0.67287234,1.34574468 L4.17040669,1.34574468 C4.25556712,1.72931695 6.47219082,11.704299 6.59975621,12.278168 C5.88465413,12.5890209 5.38297872,13.3020203 5.38297872,14.1303191 C5.38297872,15.2433621 6.28855275,16.1489362 7.40159574,16.1489362 L19.6030142,16.1489362 C19.974671,16.1489362 20.2758865,15.8477207 20.2758865,15.4760638 C20.2758865,15.104407 19.974671,14.8031915 19.6030142,14.8031915 L7.40159574,14.8031915 C7.03063983,14.8031915 6.7287234,14.5012751 6.7287234,14.1303191 C6.7287234,13.7598889 7.02958845,13.4584982 7.3998435,13.457622 L7.3998435,13.457622 Z M21.402422,4.03723404 L19.0953811,12.1117021 L7.94129543,12.1117021 L6.14696919,4.03723404 L21.402422,4.03723404 Z" id="Shape"></path>
                            <path d="M6.7287234,18.1675532 C6.7287234,19.2805962 7.63429743,20.1861702 8.74734043,20.1861702 C9.86038342,20.1861702 10.7659574,19.2805962 10.7659574,18.1675532 C10.7659574,17.0545102 9.86038342,16.1489362 8.74734043,16.1489362 C7.63429743,16.1489362 6.7287234,17.0545102 6.7287234,18.1675532 Z M8.74734043,17.4946809 C9.11829634,17.4946809 9.42021277,17.7965973 9.42021277,18.1675532 C9.42021277,18.5385091 9.11829634,18.8404255 8.74734043,18.8404255 C8.37638451,18.8404255 8.07446809,18.5385091 8.07446809,18.1675532 C8.07446809,17.7965973 8.37638451,17.4946809 8.74734043,17.4946809 Z" id="Shape"></path>
                            <path d="M16.2386525,18.1675532 C16.2386525,19.2805962 17.1442265,20.1861702 18.2572695,20.1861702 C19.3703125,20.1861702 20.2758865,19.2805962 20.2758865,18.1675532 C20.2758865,17.0545102 19.3703125,16.1489362 18.2572695,16.1489362 C17.1442265,16.1489362 16.2386525,17.0545102 16.2386525,18.1675532 Z M18.2572695,17.4946809 C18.6282254,17.4946809 18.9301418,17.7965973 18.9301418,18.1675532 C18.9301418,18.5385091 18.6282254,18.8404255 18.2572695,18.8404255 C17.8863136,18.8404255 17.5843972,18.5385091 17.5843972,18.1675532 C17.5843972,17.7965973 17.8863136,17.4946809 18.2572695,17.4946809 Z" id="Shape"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>