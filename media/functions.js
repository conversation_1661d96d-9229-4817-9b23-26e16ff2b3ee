$(function () {
	cmswebshop = CmsWebshop();
	cmswebshop.init();
	cmswebshop.shopping_cart.set_min_order_total();

	cmscoupon = CmsCoupon();
	cmscoupon.init();

	// variations
	if ($('[data-variations]').size()) {
		cmscatalog = CmsCatalog();
		cmscatalog.variation_choices('bxslider', 'input', 1);
		cmscatalog.change_variation_detail(0, $('[data-variations]').data('variation_active_id'));
	}

	var winWidth = window.parent.innerWidth;
	var body = $('body');
	var shopping_carts = $('[data-selector="shopping_cart"]');
	var shopping_cart_active = $('[data-selector_active="shopping_cart"]');
	var shipping_dates = [];
	var customer_data = [];
	var selected_products_warehouses = [];

	cmscompare = CmsCompare();
	cmscompare.init({
		'tracking_enable': {
			'index': 'ec:AddToComparelist|Homepage|%ITEM_TITLE%',
		},
	});

	check_cart_errors_after_refresh();

	// compare search autocomplete
	var accItems = winWidth < 980 ? 3 : 5;
	setTimeout(function () {
		$("input[name^='compare_autocomplete-']").each(function () {
			$(this).CmsAutocomplete({
				module: 'catalog',
				result_per_page: 'catalogproduct:' + accItems,
				lang: site_lang,
				result_image: '70x70_r',
				result_fields: 'all:image,price',
				layout: '<span class="image"><img src="%item_image%" alt=""></span><span class="search-col2"><span class="search-title">%item_label%</span><span class="search-price">%item_price%</span></span>',
				mode: 'compare',
			});
		});
	}, 1000);

	var totalAttrs = $('.c-compare-sidebar-attributes').data('total-attributes'),
		rowHeight = 0;

	function equalCompareTable() {
		$('tr.attr-row .col-title').css('height', '');
		for (var i = 1; i <= totalAttrs; i++) {
			$('tr.attr-row' + i).each(function (i) {
				var outerHeight = $(this).find('.col-title').outerHeight();
				if (outerHeight > rowHeight) {
					rowHeight = outerHeight;
				}
			});
			$('tr.attr-row' + i)
				.find('.col-title')
				.css('height', rowHeight);
			rowHeight = 0;
		}
	}

	equalCompareTable();

	$('.c-compare-btn-diff').on('click', function () {
		$('.table-c-all-attributes, .cp-attributes-compare').addClass('active');
	});
	$('.c-compare-btn-all').on('click', function () {
		$('.table-c-all-attributes, .cp-attributes-compare').removeClass('active');
	});

	if (winWidth < 1400) {
		//compare input
		$('.cp-special .cp-compare-input').focus(function () {
			$(this).parent().addClass('active');
		});

		//compare overflow
		$('.c-compare-items').scroll(function () {
			var scrollPosition = $(this).scrollLeft();

			if (scrollPosition > 0) {
				$('.compare-cnt>.wrapper').addClass('active');
			} else {
				$('.compare-cnt>.wrapper').removeClass('active');
			}
		});

		var compareSlider = $('.c-compare-items');
		if (compareSlider.length) {
			const slider = document.querySelector('.c-compare-items');
			const preventClick = e => {
				e.preventDefault();
				e.stopImmediatePropagation();
			};
			let isDown = false;
			let isDragged = false;
			let startX;
			let scrollLeft;

			slider.addEventListener('mousedown', e => {
				isDown = true;
				slider.classList.add('active');
				startX = e.pageX - slider.offsetLeft;
				scrollLeft = slider.scrollLeft;
			});
			slider.addEventListener('mouseleave', () => {
				isDown = false;
				slider.classList.remove('active');
			});
			slider.addEventListener('mouseup', e => {
				isDown = false;
				const elements = document.querySelectorAll('a');
				if (isDragged) {
					for (let i = 0; i < elements.length; i++) {
						elements[i].addEventListener('click', preventClick);
					}
				} else {
					for (let i = 0; i < elements.length; i++) {
						elements[i].removeEventListener('click', preventClick);
					}
				}
				slider.classList.remove('active');
				isDragged = false;
			});
			slider.addEventListener('mousemove', e => {
				if (!isDown) return;
				isDragged = true;
				e.preventDefault();
				const x = e.pageX - slider.offsetLeft;
				const walk = (x - startX) * 2;
				slider.scrollLeft = scrollLeft - walk;
			});
		}
	}

	// thank you page show password
	if ($('#field-password').size()) {
		$('#field-password').showPassword();
	}

	// variations
	if ($('[data-variations]').size()) {
		cmscatalog = CmsCatalog();
		cmscatalog.variation_choices('bxslider', 'input', 1);
		cmscatalog.change_variation_detail(0, $('[data-variations]').data('variation_active_id'));
	}

	// close elements on outside click
	function closeActiveElement(el) {
		$(document).on('mouseup', function (e) {
			var container = el;
			if (!container.is(e.target) && container.has(e.target).length === 0) {
				container.removeClass('active');
				container.removeClass('active-submenu');
				body.removeClass('c-legend-active');
			}
		});
	}

	shopping_carts.on('click', '.w-cart-shipping-tooltip', function (e) {
		// klik unutar drop elementa ne zatvara drop element
		e.stopPropagation();
	});

	closeActiveElement($('.c-legend'));
	closeActiveElement($('.cd-installments-note'));
	closeActiveElement($('.cd-loyalty-info'));
	closeActiveElement($('.cd-ean.longer'));
	closeActiveElement($('.wp-ean.longer'));
	//closeActiveElement($('.w-cart-select'));
	if (winWidth < 1400) {
		closeActiveElement($('.wp-price-container'));
	}
	if (winWidth > 980) {
		closeActiveElement($('.sidebar-nav>li'));
	}

	// autocomplete
	/*
    $("input[name='search_q']").CmsAutocomplete({
        module: 'catalog',
        lang: site_lang,
        result_fields: 'all:image,price,code',
        layout_default: '<span class="image"><img src="%item_image%" alt=""></span><span class="search-title">%item_label%</span><span class="search-price">%item_price%</span>',
        auto_redirect: true
    });
    */

	// custom selectbox
	/*
    var winWidth = $(window).width();
    if(!Modernizr.touch || Modernizr.touch && winWidth > 1040) {
        $("select").chosen({
            disable_search_threshold: 9999999
        });
    }
    */

	//badge tooltip
	/*if(winWidth >= 1400) {
        $(".cp-badge").mouseenter(function() {
            $(this).addClass("active");
        }).mouseleave(function(){
            $(".cp-badge").removeClass('active');
        });
    }*/

	//departments fancybox
	var guideWidth = 400,
		guideMargin = 0,
		guideHeight = 'auto',
		guideAutoHeight = false;

	if (winWidth < 980) {
		var guideWidth = '100%',
			guideMargin = 25;
	}

	$('.btn-department-change').fancybox({
		'margin': guideMargin,
		'padding': 0,
		'scrolling': 'auto',
		'width': guideWidth,
		'height': guideHeight,
		'autoSize': guideAutoHeight,
		'autoScale': true,
		'fitToView': true,
		'autoCenter': true,
		'wrapCSS': 'fancybox-size-guide',
		afterLoad: function (current, previous) {
			$('body').addClass('departments-active');
		},
		afterClose: function () {
			$('body').removeClass('departments--active');
		},
	});

	//cart scan fancybox
	var scanWidth = 500,
		scanHeight = 'auto',
		scanMargin = 25,
		scanAutoHeight = true;

	if (winWidth < 760) {
		var scanWidth = '90%',
			scanMargin = 15;
	}

	/*
    $(".w-scan-btn, .w-cart-qr-share, .w-card-code-scan, .sw-scan-btn, .wp-title-barcode, .w-scan-item-btn").fancybox({
        'type': 'iframe',
        'padding': 0,
        'margin': scanMargin,
        'scrolling': 'auto',
        'width': scanWidth,
        'height': scanHeight,
        'autoSize': scanAutoHeight,
        'autoScale': true,
        'fitToView': true,
        'autoCenter': true,
    });
    */

	//legend tooltip
	if (winWidth >= 1400) {
		$('.c-legend')
			.mouseenter(function () {
				$(this).addClass('active');
			})
			.mouseleave(function () {
				$('.c-legend').removeClass('active');
			});
	} else {
		$('.c-legend-label').on('click', function (e) {
			$(this).parent().addClass('active');
			body.addClass('c-legend-active');
		});

		$('.c-lt-close').on('click', function (e) {
			$('.c-legend').removeClass('active');
			body.removeClass('c-legend-active');
		});
	}

	//gift image zoom
	if (winWidth >= 1400) {
		$('.cd-gift-img')
			.mouseenter(function () {
				$(this).parent().addClass('active');
			})
			.mouseleave(function () {
				$('.cd-gift-section').removeClass('active');
			});
	}

	//services tooltip
	$('.cd-extra-benefit-icon').live('click', function () {
		var $this = $(this).parent().parent();
		$this.toggleClass('active');
	});

	$('.cd-extra-benefit-tooltip a, .cd-installments-note-tooltip a, .cd-loyalty-info-tooltip a').click(function () {
		window.open(this.href);
		return false;
	});

	//ean tooltip
	$('.cd-ean.longer, .wp-ean.longer').live('click', function () {
		$(this).toggleClass('active');
	});

	//installments tooltip
	if (winWidth >= 1400) {
		$('.cd-installments-note')
			.mouseenter(function () {
				$(this).addClass('active');
			})
			.mouseleave(function () {
				$('.cd-installments-note').stop(true).removeClass('active');
			});
	} else {
		$('.cd-installments-note>.cd-installments-note-title').on('click', function () {
			var $this = $(this).parent();

			if ($this.hasClass('active')) {
				$this.toggleClass('active');
			} else {
				$('.cd-installments-note').removeClass('active');
				$this.addClass('active');
			}
		});

		$('.cd-installments-note-close').on('click', function () {
			$('.cd-installments-note').removeClass('active');
		});
	}

	//loyalty tooltip
	if (winWidth >= 1400) {
		$('.cd-loyalty-info')
			.mouseenter(function () {
				$(this).addClass('active');
			})
			.mouseleave(function () {
				$('.cd-loyalty-info').stop(true).removeClass('active');
			});
	} else {
		$('.cd-loyalty-info>.cd-loyalty-info-label').on('click', function () {
			var $this = $(this).parent();

			if ($this.hasClass('active')) {
				$this.toggleClass('active');
			} else {
				$('.cd-loyalty-info').removeClass('active');
				$this.addClass('active');
			}
		});

		$('.cd-loyalty-info-close').on('click', function () {
			$('.cd-loyalty-info').removeClass('active');
		});
	}

	// floating labels
	$('.field-b_company_name, .field-b_company_oib, .field-b_company_address').addClass('field-cnt').removeClass('field');

	$('.field-b_r1').on('click', function () {
		setTimeout(function () {
			$('.field-cnt').floatingFormLabels();
		}, 200);
	});

	if ($('.field').length) {
		$('.field').floatingFormLabels();
	}
	setTimeout(function () {
		if (winWidth > 980) {
			$('#webshop_form:not(".step3") .field:first').addClass('ffl-floated');
		} else {
			$('#webshop_form:not(".step3") .field:first input').blur();
		}
	}, 50);

	$('.field-cnt').floatingFormLabels();

	//autocomplete
	var acItems = 5;
	$("input[name='search_q']").CmsAutocomplete({
		module: 'catalog',
		result_per_page: 'catalogproduct:' + acItems,
		lang: site_lang,
		result_image: '70x70_r',
		result_fields: 'all:image,basic_price,price,code,category_title',
		layout_default:
			'<span class="search-image"><img src="%item_image%" alt=""></span>' +
			'<span class="search-cnt">' +
			'<span class="search-title">%item_label%</span><span class="search-category">%item_category_title%</span>' +
			'<span class="search-price"><span class="search-old-price">%item_basic_price%</span><span class="search-current-price">%item_price%</span></span>' +
			'</span>',
		auto_redirect: true,
	});

	// close shopping cart preview
	/*
    $('.product-message-modal .close, .modal-continue-shopping').live('click', function() {
        $('.product-message-modal').removeClass('active');
    });

    $(document).on('mouseup', function(e) {
        var container = $('div.modal-box');

        if(!container.is(e.target) && container.has(e.target).length === 0) {
            $('div.product-message-modal').removeClass('active');
        }
    });
    */

	// datepicker
	$.datepicker.setDefaults($.datepicker.regional[site_i18n_lang]);

	var dateToday = new Date();
	$('.field-datepicker').datepicker({
		minDate: dateToday,
		altFormat: 'yy-mm-dd',
		changeYear: true,
		changeMonth: true,
		yearRange: '-100:+0',
	});

	//filter categories toggle
	$('.ci-title').click(function (e) {
		e.preventDefault();
		$(this).parent().toggleClass('active');
		body.toggleClass('body-overflow');
	});

	$('.ci-item-wrapper>li.has-children>a').click(function (e) {
		e.preventDefault();
		$(this).parent().toggleClass('active');
	});

	$('.ci-item-wrapper>li>ul>li>a').each(function () {
		if ($(this).hasClass('active')) {
			var parent = $(this).parent().parent().parent();
			parent.addClass('active');
		}
	});

	//categories toggle
	$('.categories>li>a').click(function (e) {
		var parent = $(this).parent();

		if (parent.hasClass('has-children')) {
			e.preventDefault();
			parent.toggleClass('active');
		}
	});
	if (winWidth < 980) {
		$('.subcategories>li>a').click(function (e) {
			var parent = $(this).parent();

			if (parent.hasClass('has-children')) {
				e.preventDefault();
				parent.toggleClass('active');
			}
		});
	}

	//mobile settings toggle
	if (winWidth < 980) {
		$('.sidebar-settings-m>a').on('click', function (e) {
			body.toggleClass('setting-nav-active');
			$(this).parent().toggleClass('active');
		});
	}

	if (winWidth > 1400) {
		$('.sidebar-nav>li.has-children')
			.mouseenter(function () {
				$(this).addClass('active-submenu');
			})
			.mouseleave(function () {
				$('.sidebar-nav>li').removeClass('active-submenu');
			});
	} else if (winWidth > 980) {
		$('.sidebar-nav>li>a').on('click', function (e) {
			var parent = $(this).parent();

			if (parent.hasClass('has-children')) {
				e.preventDefault();
				if (!parent.hasClass('active-submenu') && winWidth > 980) {
					$('.nav-categories>li').removeClass('active-submenu');
				}
				parent.toggleClass('active-submenu');
			}
		});
	}

	//cart checked
	$('.w-cart-item .w-cart-header input:radio').change(function () {
		$('.w-cart-item').removeClass('active');

		if ($(this).is(':checked')) {
			$(this).parent().parent().parent().addClass('active');
		} else {
			$(this).parent().parent().parent().removeClass('active');
		}
	});

	function store_reload() {
		window.location = window.location.pathname + '?' + Math.floor(Date.now() / 1000);
		//location.reload();
	}

	//cart more details
	$('.w-cart-more-btn').on('click', function (e) {
		if ($(this).closest('form').data('selector_active') != undefined && !$(this).parent().parent().hasClass('details-active') && $(window).width() < 980) {
			store_reload();
		}
		$(this).parent().parent().toggleClass('details-active');
	});

	shopping_carts.on('click', '.w-cart-shipping-choose', function () {
		var this_form = shopping_cart_active;
		var cart_token_id = this_form.data('cart_token_id');
		get_warehouses_from_selected_products();
		$(this).parent().parent().addClass('active');
		//$('body').addClass('cart-shipping-tooltip');
		$('.w-cart-shipping-bar').removeClass('active-item');
		$('body').removeClass('cart-active-item');
		$('body').addClass('cart-shipping-tooltip');
		if (winWidth <= 980) {
			$('#choosenItems').clone().appendTo('.w-cart-shipping-tooltip-title.special');
		}

		var itemCheckboxCheckedClone = $('.wp-check-button').find('input[type=checkbox]:checked');
		$(itemCheckboxCheckedClone).parent().parent().clone().appendTo('.w-cart-shipping-tooltip .w-cart-shipping-tooltip-row1');

		$(function () {
			var shippingItems = $('.w-cart-shipping-tooltip-row1 .wp-order-numb');
			shippingItems.each(function (i) {
				var shippingItemCount = i + 1;
				$(this).text(shippingItemCount + '.');
			});
		});

		check_BB_shipping();

		console.debug('cart-shipping-choose, customer_data', customer_data);

		if (customer_data.length < 1 || customer_data[cart_token_id].cart_reservation_for == null || customer_data[cart_token_id].cart_reservation_for == '') {
			$('input[name="shipping"]').siblings('.field_error').remove();
			$('input[name="shipping"][value="3"]').attr('disabled', true);
			$('input[name="shipping"][value="3"]').next('label').after('<span id="field-error-shipping" class="field_error error error_cart_product_shipping" style="">Nije moguće odabrati jer nije unesen komitent</span>');
			$('input[name="shipping"][value="4"]').attr('disabled', true);
			$('input[name="shipping"][value="4"]').next('label').after('<span id="field-error-shipping" class="field_error error error_cart_product_shipping" style="">Nije moguće odabrati jer nije unesen komitent</span>');
		} else {
			$('input[name="shipping"][value="3"]').attr('disabled', false);
			$('input[name="shipping"][value="3"]').next('span').remove();
			$('input[name="shipping"][value="4"]').attr('disabled', false);
			$('input[name="shipping"][value="4"]').next('span').remove();
		}
	});

	//cart shipping bar
	$('.w-cart-item.details-active').each(function () {
		var itemCheckbox = $('.wp-check-button').find('input[type=checkbox]');
		var itemCheckboxChecked = $('.wp-check-button').find('input[type=checkbox]:checked').length;
		if (itemCheckboxChecked > 0) {
			$('.w-cart-shipping-bar').addClass('active-item');
			$('body').addClass('cart-active-item');
			$('#choosenItems').html('(' + itemCheckboxChecked + ')');
		}

		itemCheckbox.live('change', function () {
			$('.w-cart-shipping-tooltip-row1 .wp-row1 .wp-check-button').remove();
			var itemCheckboxChecked = $('.wp-check-button').find('input[type=checkbox]:checked').length;
			if (itemCheckboxChecked > 0) {
				$('.w-cart-shipping-bar').addClass('active-item');
				$('body').addClass('cart-active-item');
				$('#choosenItems').html('(' + itemCheckboxChecked + ')');
			} else {
				$('.w-cart-shipping-bar').removeClass('active-item');
				$('body').removeClass('cart-active-item');
				$('#choosenItems').html('(' + itemCheckboxChecked + ')');
			}
		});
	});

	$('.w-cart-group-title').live('click', function () {
		var groupTitle = $(this).parent().data('shopping_shipping_group');
		if ($("*[data-shopping_shipping='" + groupTitle + "']:checked").length == $("*[data-shopping_shipping='" + groupTitle + "']").length) {
			$("*[data-shopping_shipping='" + groupTitle + "']").prop('checked', false);
			var itemCheckboxChecked = $('.wp-check-button').find('input[type=checkbox]:checked').length;
		} else {
			$("*[data-shopping_shipping='" + groupTitle + "']").prop('checked', true);
			var itemCheckboxChecked = $('.wp-check-button').find('input[type=checkbox]:checked').length;
		}
		if (itemCheckboxChecked > 0) {
			$('.w-cart-shipping-bar').addClass('active-item');
			$('body').addClass('cart-active-item');
			$('#choosenItems').html('(' + itemCheckboxChecked + ')');
		} else {
			$('.w-cart-shipping-bar').removeClass('active-item');
			$('body').removeClass('cart-active-item');
			$('#choosenItems').html('(' + itemCheckboxChecked + ')');
		}
	});

	// remove selected products
	shopping_carts.on('click', '.w-cart-shipping-remove', function (e) {
		var this_form = shopping_cart_active;
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		change_shipping_data_for_products(this_form, data, 'delete');
		cmswebshop.shopping_cart.remove_qty(selected_product_ids);
	});

	// deselect all products
	shopping_carts.on('click', '.w-cart-shipping-deselect', function (e) {
		var itemCheckbox = $('.wp-check-button').find('input[type=checkbox]');
		var itemCheckboxChecked = $('.wp-check-button').find('input[type=checkbox]:checked').length;
		$(itemCheckbox).prop('checked', false);
		$('#choosenItems').html('(' + itemCheckboxChecked + ')');
		$('.w-cart-shipping-bar').removeClass('active-item');
		$('body').removeClass('cart-active-item');
		$('.w-cart-shipping-tooltip-row1 .wp-row1').remove();
	});

	// hide shipping popup
	shopping_carts.on('click', '.w-cart-shipping-tooltip-close', function (e) {
		var itemCheckbox = $('.wp-check-button').find('input[type=checkbox]');
		var itemCheckboxChecked = $('.wp-check-button').find('input[type=checkbox]:checked').length;
		var itemCheckbox_shipping = $('.w-cart-shipping-tooltip').find('input[type=radio]');
		$(itemCheckbox_shipping).prop('checked', false);
		$('.w-cart-shipping-tooltip').find('input[type=radio]:first').prop('checked', true);
		$('.w-cart-shipping-tooltip').find('input[type=radio]:first').trigger('change');
		$(itemCheckbox).prop('checked', false);
		$('#choosenItems').html('(' + itemCheckboxChecked + ')');
		$('.w-cart-shipping-bar').removeClass('active');
		$('body').removeClass('cart-shipping-tooltip');
		$('.w-cart-shipping-tooltip-row1 .wp-row1').remove();
		if (winWidth <= 980) {
			$('.w-cart-shipping-tooltip-title.special #choosenItems').remove();
		}
	});

	// submit new shipping method
	shopping_carts.on('click', '.w-cart-shipping-submit', function (e) {
		console.debug('CLICK w-cart-shipping-submit');
		$(this).prop('disabled', true); // prevent double submit

		var this_form = shopping_cart_active;
		var cart_token_id = this_form.data('cart_token_id');
		var api_code2 = cart_token_id in customer_data && customer_data[cart_token_id] != null ? customer_data[cart_token_id].api_code2 : '';

		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		if (selected_product_ids.length) {
			var selected_shipping_method_val = data.shipping_method_val;
			console.debug('- selected_shipping_method_val 1', selected_shipping_method_val);

			switch (selected_shipping_method_val) {
				case '1': // pickup in this store
					change_shipping_data_for_products(this_form, data);
					break;
				case '2': // pickup in another store
					// uncheck ship_to_address_change_type
					// mora postojati pickup location, inace greska
					if (data.pickup_location_val) {
						change_shipping_data_for_products(this_form, data);
					} else {
						// FIXME friendly error message
						alert('Izberite poslovalnicu!');
					}
					break;
				case '3': // ship to address
				case '4':
					if (data.ship_to_address_change_type == 1) {
						// exist customer address
						change_shipping_data_for_products(this_form, data);
					} else {
						// developed for batch change shipping data on product level
						var address_data_container = $(this_form)
							.find($('input[name="ship_to_address_change_type"]:checked'))
							.closest('.field-shipping-row')
							.next('[data-selector="shipping-form-' + data.ship_to_address_change_type + '"]');
						var this_form = shopping_cart_active;
						var data = get_shipping_data(this_form, address_data_container);
						var selected_product_ids = data.product_ids;

						if (selected_product_ids.length) {
							change_shipping_data_for_products(this_form, data);
						} else {
							$(this).prop('disabled', false);
							show_error($('.content-section'), ['Izberite nekaj izdelkov']);
						}
					}
					break;
				default:
					// FIXME friendly error message
					alert('Izberite način otpreme');
			}
		} else {
			$(this).prop('checked', false);
			show_error($('.content-section'), ['Izberite nekaj izdelkov']);
		}
	});

	// developed for batch change shipping data on product level
	// 1 - pickup in this store, 2 - pickup in another store, 3 - ship to address 4 - bigbang dostava
	shopping_carts.on('change', 'input[name="shipping"][data-change="batch"]', function (e) {
		var this_element = this;

		var this_form = $(this_element.form);
		var cart_token_id = this_form.data('cart_token_id');
		var api_code2 = cart_token_id in customer_data && customer_data[cart_token_id] != null ? customer_data[cart_token_id].api_code2 : '';

		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		// ovisno o dostavi, enabled ili disabled odabira dostave
		var submit_button = $('.w-cart-shipping-submit');
		submit_button.addClass('disabled');

		if (selected_product_ids.length) {
			var selected_shipping_method_val = data.shipping_method_val;
			console.debug('- selected_shipping_method_val 2', selected_shipping_method_val);

			switch (selected_shipping_method_val) {
				case '1': // pickup in this store
					// uncheck pickup locations
					this_form.find('input[name="pickup_location"]').prop('checked', false);
					// uncheck ship_to_address_change_type
					this_form.find('input[name="ship_to_address_change_type"]').prop('checked', false);
					submit_button.removeClass('disabled');
					break;
				case '2': // pickup in another store
					// uncheck ship_to_address_change_type
					this_form.find('input[name="pickup_location"]').each(function () {
						$(this).next('label').css('font-weight', 'normal');
					});
					for (index in selected_products_warehouses) {
						this_form
							.find('input[name="pickup_location"][value=' + selected_products_warehouses[index] + ']')
							.next('label')
							.css('font-weight', 'bold');
					}
					this_form.find('input[name="ship_to_address_change_type"]').prop('checked', false);
					break;
				case '3': // ship to address
				case '4':
					if (!api_code2) {
						$(this_element).closest('.field-shipping-row').next('.cd-shipping-subitem').find($("input[name='ship_to_address_change_type'][value='1']")).closest('.field-shipping-row').hide();
						$(this_element).closest('.field-shipping-row').next('.cd-shipping-subitem').find($("input[name='ship_to_address_change_type'][value='2']")).closest('.field-shipping-row').hide();
					}

					this_form.find('input[name="pickup_location"]').prop('checked', false); // uncheck pickup locations

					//hide other shipping method
					$('input[name="shipping"]:not(:checked)').closest('.field-shipping-row').next('.cd-shipping-subitem').hide();
					break;
			}
		} else {
			$(this).prop('checked', false);
			show_error($('.content-section'), ['Izberite nekaj izdelkov']);
		}
	});

	// shipping - change pickup location
	shopping_carts.on('change', 'input[name="pickup_location"][data-change="batch"]', function () {
		// developed for batch change shipping data on product level
		var this_form = $(this.form);
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		var submit_button = $('.w-cart-shipping-submit');
		submit_button.addClass('disabled');

		if (selected_product_ids.length) {
			submit_button.removeClass('disabled');
		} else {
			$(this).prop('checked', false);
			show_error($('.content-section'), ['Izberite nekaj izdelkov']);
		}
	});

	// shipping - select shipping type
	shopping_carts.on('change', 'input[name="ship_to_address_change_type"][data-change="batch"]', function () {
		// developed for batch change shipping data on product level
		var this_form = $(this.form);
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		var submit_button = $('.w-cart-shipping-submit');
		submit_button.addClass('disabled');

		if (selected_product_ids.length) {
			populate_inputs(this_form, selected_product_ids, $(this).val());
			submit_button.removeClass('disabled');
		} else {
			$(this).prop('checked', false);
			show_error($('.content-section'), ['Izberite nekaj izdelkov']);
		}
	});

	function check_if_finishing_order_forbidden() {
		const cart_hash_entered = check_if_cart_hash_entered();
		const cart_token_id = $(shopping_cart_active).data('cart_token_id');
		const exist_customer_data = customer_data.length > 0 && 'cart_reservation_for' in customer_data[cart_token_id] && customer_data[cart_token_id].cart_reservation_for != null && customer_data[cart_token_id].cart_reservation_for != '' ? true : false;
		var this_form = shopping_cart_active;
		var shipping_data = get_shipping_data(this_form, false);
		var all_products = shipping_data.cart_all_products;
		var all_errors = [];
		var allowed = true;

		console.debug('all_products', all_products);
		console.debug('exist_customer_data', exist_customer_data);

		if (!cart_hash_entered) {
			all_errors.push({
				'cart_token_id': cart_token_id,
				'parent_field': '.w-card-code-input',
				'error_input': '.w-card-code-input',
				'message': 'Nije unesen kod šoping košarice',
				'error_indentificatior': 'cart_hash_entered',
				'extra_error_class': 'error_cart_hash_entered',
			});
		}

		all_products.forEach(product => {
			console.debug('- product', product);

			if (product.serial_code_mandatory) {
				if (product.serial_code) {
					const serial_numbers = product.serial_code.replace('/s/g', '').split(',');
					let serial_number_count = 0;

					serial_numbers.forEach(serial_number => {
						if (serial_number.trim().length > 0) {
							serial_number_count++;
						}
					});

					if (serial_number_count != product.qty) {
						all_errors.push({
							'id': product.id,
							'parent_field': '.wp-serial-number-input',
							'error_input': '#serial_code_' + product.id,
							'message': 'Nisu uneseni serijski brojevi (ukupno ' + product.qty + ')',
							'error_indentificatior': 'cart_product_sn_' + product.id,
							'extra_error_class': 'error_cart_product_sn',
						});
					}
				} else {
					all_errors.push({
						'id': product.id,
						'parent_field': '.wp-serial-number-input',
						'error_input': '#serial_code_' + product.id,
						'message': 'Nije unesen serijski broj',
						'error_indentificatior': 'cart_product_sn_' + product.id,
						'extra_error_class': 'error_cart_product_sn',
					});
				}
			}

			if (!exist_customer_data) {
				if (product.avans_product) {
					all_errors.push({
						'id': product.id,
						'parent_field': '.w-cart-checkbox-avans',
						'error_input': '#checkboxAvans-' + product.id,
						'message': 'Za avansni proizvod je obavezan odabir komitenta',
						'error_indentificatior': 'cart_product_avans_' + product.id,
						'extra_error_class': 'error_cart_product_avans',
					});
				}

				/*
                if (product.available_qty <= 0) {
                    all_errors.push({
                        'id': product.id,
                        'children_field': '.wp-code',
                        'error_field': '#product_details_' + product.id,
                        'message': 'Proizvod nema zaliha',
                        'error_indentificatior': 'cart_product_qty_' + product.id,
                        'extra_error_class': 'error_cart_product_qty',
                    });
                }
              */

				if (product.group_shipping_method != 1 || product.group_shipping_today != 1) {
					all_errors.push({
						'cart_token_id': cart_token_id,
						'id': product.id,
						'error_input': '[data-sort_group_position="' + product.sort_group_position + '"]',
						'message': 'Za ovu odpremnu narudžbu je obavezan odabir komitenta',
						'error_indentificatior': 'cart_customer_entered_' + product.sort_group_position,
						'extra_error_class': 'error_cart_customer_entered',
					});

					all_errors.push({
						'cart_token_id': cart_token_id,
						'error_input': '.w-cart-sw-input',
						'parent_field': '.w-cart-sw-input',
						'message': 'Nije odabran komitent',
						'error_indentificatior': 'cart_customer_entered',
						'extra_error_class': 'error_cart_customer_entered_sw',
					});
				}
			}
		});

		//Pronadji mi sva moguca osiguranja u kosarici
		const insurances_and_services = $('[data-service_extra_category="insurance"]');
		if (insurances_and_services.length > 0 && !exist_customer_data) {
			insurances_and_services.each((index, insurance) => {
				//Ako mi je osiguranje odabrano na proizvodu, i ne radi se o "Ne želim dodatnega zavarovanja", tada je potreban komitent
				if (insurance.checked && insurance.value != 0) {
					const insurance_id_parts = insurance.id.split('-');
					const product_id = insurance_id_parts[1];

					all_errors.push({
						'cart_token_id': cart_token_id,
						'id': product_id,
						'error_input': '#wp-row1_' + product_id,
						'message': 'Za proizvod s osiguranjem obavezan je odabir komitenta',
						'error_indentificatior': 'insurance_selected' + product_id,
						'extra_error_class': 'error_cart_customer_entered',
					});
				}
			});
		}

		console.debug('all_errors', all_errors);

		if (all_errors.length > 0) {
			show_error($('.content-section'), [''], all_errors, true);
			allowed = false;
		}

		return allowed;
	}

	function check_if_cart_hash_entered() {
		return $('#id_cart_hash').val() != '';
	}

	function on_serial_number_blur(el, inputEl) {
		setTimeout(function () {
			el.parent().removeClass('active');
			console.debug('on_serial_number_blur');
			// submit on blur
			let data = form_data_for_serial_number_update(inputEl[0], inputEl.closest('form'));
			set_serial_number(data);
		}, 100);
	}

	//cart serial number focus
	shopping_carts
		.on('focus', '.wp-serial-number-input', function () {
			$(this).parent().addClass('active');
		})
		.on('blur', '.wp-serial-number-input', function () {
			on_serial_number_blur($(this), $(this));
		});

	//cart serial number clear value
	$('.wp-serial-number-clear').live('mousedown', function (e) {
		$(this).closest('.wp-serial-number').find('input').val('');
		on_serial_number_blur($(this), $(this));
	});

	//cart serial number confirm value
	shopping_carts.on('click', '.wp-serial-number-confirm', function () {
		on_serial_number_blur($('.wp-serial-number-input'), $(this));
	});

	function form_data_for_serial_number_update(input_element, this_form) {
		const serial_number_input_element = $(input_element).closest('.wp-serial-number').find('input');
		const serial_number = serial_number_input_element.val();
		const product_code = serial_number_input_element[0].getAttribute('code');
		const cart_token_id = this_form.data('cart_token_id');

		const data = {
			'cart_token_id': cart_token_id,
			'product_code': product_code,
			'serial_number': serial_number,
		};

		return data;
	}

	function set_serial_number(data) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_serial_number/',
			data: data,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (res_data) {
				if (res_data.response === 'ok') {
					$('#serial_code_' + data.product_code).attr('db_value', data.serial_number);
				} else {
					show_error($('.content-section'), res_data.message);
				}

				loading_indicator('hide');
				remove_error('cart_product_sn_' + data.product_code);
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	$('#btn-finish-shopping,#btn-reserve-card').click(function (e) {
		const cart_hash_entered = check_if_cart_hash_entered();

		if (!cart_hash_entered) {
			e.preventDefault();

			error_message = 'Treba je izpolniti kartično številko.';

			//potreban timeout zato sto se loading indicator pojavi tek kasnije.
			//takoder ovim nacinom dobivamo konzistentnost kod submitanja formi
			setTimeout(function () {
				loading_indicator('hide');
				show_error($('.content-section'), [error_message]);
			}, 500);
		}
	});

	$('.wp-btn-qty').click(function () {
		setTimeout(function () {}, 300);
	});

	//cart name focus
	$('.w-cart-name')
		.focus(function () {
			$(this).parent().addClass('active');
			$(this).parent().parent().addClass('name-edit');
		})
		.blur(function () {
			$(this).parent().removeClass('active');
			$(this).parent().parent().removeClass('name-edit');
		});

	//cart name confirm value
	$('.w-cart-name-confirm').click(function () {
		$('.w-cart-name').blur('');
	});

	//cart item details
	/*$('.wp-item-details').on('click', function (e) {
        $(this).parent().toggleClass('active-item-details');
    });*/

	//cart price tooltip
	if (winWidth >= 1400) {
		$('.wp-price-container')
			.mouseenter(function () {
				$(this).addClass('active');
			})
			.mouseleave(function () {
				$('.wp-price-container').removeClass('active');
			});
	} else {
		$('.wp .wp-price-container').on('click', function (e) {
			e.preventDefault();
			if (!$(this).hasClass('active')) {
				$('.wp-price-container').removeClass('active');
			}
			$(this).toggleClass('active');
		});
	}

	//cart remove
	var cartRemoveWidth = 580,
		cartRemoveHeight = 'auto',
		cartRemoveAutoHeight = false;

	if (winWidth < 760) {
		cartRemoveWidth = '100%';
	}

	/*
    $(".w-cart-remove").fancybox({
        'width': cartRemoveWidth,
        'height': cartRemoveHeight,
        'autoSize': cartRemoveAutoHeight,
        'padding': 0,
        'autoScale': true,
        'fitToView': true,
        'autoCenter': true,
        'wrapCSS': 'fancybox-remove-cart',
        afterLoad: function (current, previous) {
            $('body').addClass('body-overflow');
        },
        afterClose: function () {
            $('body').removeClass('body-overflow');
        }
    });

    $('body').on('click', '.w-cart-remove-tooltip-btns .btn-cancel', function () {
        $.fancybox.close();
    });
    */

	//manufacturers toggle
	if (winWidth < 980) {
		$('.m-letter').on('click', function () {
			$(this).parent().toggleClass('active');
		});
	}

	//publish filters button
	if ($('.p-categories').find('input').is(':checked')) {
		$('.p-categories-item').addClass('active');
	}

	//modal box
	if ($('body').hasClass('modal-fancybox-small')) {
		if (winWidth < 980) {
			var modalWidth = 600;
		} else {
			var modalWidth = '100%';
		}
	} else {
		if (winWidth < 1400) {
			var modalWidth = '100%';
		} else {
			var modalWidth = 1230;
		}
	}

	var modalHeight = 'auto',
		modalMargin = 30,
		modalAutoHeight = true;

	if (winWidth < 980) {
		var modalHeight = '100%',
			modalMargin = 0,
			modalAutoHeight = false;
	}

	// Frontend team added to show the modal design
	// $(".cd-btn-add").fancybox({
	// 	'type': 'iframe',
	// 	'padding': 0,
	// 	'margin': modalMargin,
	// 	'scrolling': 'auto',
	// 	'width': modalWidth,
	// 	'height': modalHeight,
	// 	'autoSize': modalAutoHeight,
	// 	'autoScale': true,
	// 	'fitToView': true,
	// 	'wrapCSS': 'fancybox-modal',
	// 	afterLoad: function (current, previous) {
	// 		$('body').addClass('fancybox-active');
	//         $('.fancybox-modal').parent().addClass('fancybox-cart-modal');
	// 	},
	// 	afterClose : function(){
	// 		$('body').removeClass('fancybox-active');
	//         $('.fancybox-modal').parent().removeClass('fancybox-cart-modal');
	// 	}
	// });

	var product_price_el = $('[data-product_price_number]');
	var product_price = parseFloat(product_price_el.data('product_price_number'));

	$('[data-sum_to_price]').on('change', function () {
		var extra_costs = 0;

		$('[data-sum_to_price=""]').each(function () {
			var current_extra_item = $(this);

			if (current_extra_item.is(':checked')) {
				var current_extra_item_price = parseFloat(current_extra_item.data('service_extra_price_number'));
				extra_costs = extra_costs + current_extra_item_price;
			}
		});

		var product_price_final = product_price + extra_costs;
		var product_price_formated = decimal_2_price(product_price_final, site_currency_display || '%s');

		product_price_el.text(product_price_formated);
	});

	$('.cd-btn-add').on('click', function (e) {
		// prevent submit and show error if selected shipping method is "pickup in another store" and no pickup location is not selected
		var selected_shipping_method_id = $('input[name="shipping"]:checked').val();
		var selected_pickup_location_id = $('input[name="pickup_location"]:checked').val();

		if (!selected_shipping_method_id) {
			e.preventDefault();
			show_error($('.content-section'), ['Izberite način pošiljanja']);
		} else if (selected_shipping_method_id == 2 && !selected_pickup_location_id) {
			e.preventDefault();
			show_error($('.content-section'), ['Izberite mesto prevzema']);
		}
	});

	$('body').on('change', 'input[name="shipping"]', function () {
		// if shipping method is not "pickup in another store" uncheck selected pickup location
		if ($(this).val() != 2) {
			$('input[name="pickup_location"]').prop('checked', false);
		}
	});

	$('body').on('change', 'input[name="pickup_location"]', function () {
		// if pickup location selected and shipping method is not "pickup in another store" select this method
		var selected_shipping_method_el = $('input[name="shipping"]');
		if (selected_shipping_method_el.val() != 2) {
			$('input[name="shipping"][value="2"]').prop('checked', true);
		}
	});

	//checkbox section
	if (winWidth <= 980) {
		$('.w-cart-checkbox-section-btn').live('click', function () {
			var $this = $(this).parent();
			$this.toggleClass('active');
		});

		$('.w-cart-shipping-tooltip-title.special').on('click', function () {
			var $this = $(this).parent();
			$this.toggleClass('active');
		});
	}

	//cart item qty
	/*
    $('.wp').each(function () {
        var $this = $(this);
        var wpItemQty = $this.find('.product_qty_special');
        var wpItemQtyNumber = wpItemQty.html();
        if(wpItemQtyNumber > 1) {
            wpItemQty.parent().addClass('visible');
        } else {
            wpItemQty.parent().removeClass('visible');
        }

        $('.wp-btn-qty').on("click", function (e) {
            setTimeout(function () {
                var wpItemQtyNumber = wpItemQty.html();
                if(wpItemQtyNumber > 1) {
                    wpItemQty.parent().addClass('visible');
                } else {
                    wpItemQty.parent().removeClass('visible');
                }
            }, 7000);
        });
        $('.wp-input-qty').on("change", function (e) {
            setTimeout(function () {
                var wpItemQtyNumber = wpItemQty.html();
                if(wpItemQtyNumber > 1) {
                    wpItemQty.parent().addClass('visible');
                } else {
                    wpItemQty.parent().removeClass('visible');
                }
            }, 7000);
        });
    });
    */

	//tab blank
	$('.cd-installments-text-tooltip a, .cd-shipping-info-tooltip a, .cd-extra-benefit-desc a').click(function () {
		window.open(this.href);
		return false;
	});

	//energy fancybox
	var energyWidth = 420,
		energySize = false;

	if (winWidth < 760) {
		var energyWidth = '100%',
			energySize = 'auto';
	}

	$('.cd-energy-link').fancybox({
		'width': energyWidth,
		'height': 'auto',
		'autoSize': energySize,
	});

	if (winWidth <= 1400) {
		$('.cd-desc-header').live('click', function () {
			var $this = $(this).parent();
			$this.toggleClass('active');
		});
	}

	// css var
	if (winWidth > 1400) {
		var contentCol = $('.cd-col1');
		if (winWidth > 1600) {
			var cdCol = 800;
		} else {
			var cdCol = 640;
		}

		function checkWidth() {
			if (contentCol.length) {
				var contentColPosition = contentCol.offset().left;
				var screenWidth = $(window).width();
				var colWidth = screenWidth - contentColPosition - cdCol;
				document.documentElement.style.setProperty('--contentWidth', colWidth + 'px');
			}
		}

		checkWidth();
		$(window).resize(checkWidth);
	}

	ssm.addState({
		query: '(max-width: 1400px)',
		onEnter: function () {
			$('.wp').each(function () {
				var $this = $(this);
				$this.find('.wp-row1 .wp-col4').detach().appendTo($this.find('.wp-row2'));
			});
			$('.c-compare-items').append('<article class="cp-special-empty"></article>');

			$('.cd-top, .cd-price, .cd-installments-note, .cd-energy-info').detach().appendTo('.cd-header-extra-col1');
		},
		onLeave: function () {
			$('.wp').each(function () {
				var $this = $(this);
				$this.find('.wp-row2 .wp-col4').detach().insertBefore($this.find('.wp-row1 .wp-total'));
			});
		},
	});

	ssm.addState({
		query: '(max-width: 980px)',
		onEnter: function () {
			$('.ci-categories').detach().prependTo('.main-header');
			$('.cd-energy-info').detach().appendTo('.cd-header-extra-col2');
			$('.cd-ean-box').detach().insertAfter('.cd-header');
			$('.cd-discount-expire').insertAfter('.cd-price-row1');
			$('.c-legend, .c-toolbar-sort-container').insertAfter('.c-filters-section');
			$('.c-legend, .c-toolbar-sort-container').wrapAll('<div class="c-toolbar-bottom"/>');
			$('.ww').detach().insertAfter('.sidebar-nav>li:nth-child(2)');
			$('.cw-compare').detach().appendTo('.sidebar-settings-m>ul');
			$('.sidebar-nav .m-move').detach().appendTo('.sidebar-settings-m>ul');
			$('.sidebar-footer-nav>li').detach().appendTo('.sidebar-settings-m>ul');
			$('.w-cart-body').each(function () {
				var $this = $(this);
				$this.find('.w-cart-content-bottom').detach().appendTo($this.find('.w-cart-bottom'));
			});
			$('.wp').each(function () {
				var $this = $(this);
				$this.find('.w-cart-checkbox-section').detach().appendTo($this.find('.wp-col4'));
			});
		},
		onLeave: function () {
			if (!$('body').hasClass('quick')) {
				setTimeout(function () {
					store_reload();
				}, 1000);
			}
		},
	});

	////// PIM/CS product content switcher /////////////////////////////////////////////
	var loadbeeProductFoundCallback = function () {
		$('div[data-type_of_content="pim_content"]').hide();
		$('div[data-type_of_content="cs_api_content"]').show();
	};

	if (typeof flixJsCallbacks === 'object') {
		flixJsCallbacks.setLoadCallback(function () {
			try {
				$('div[data-type_of_content="pim_content"]').hide();
				$('div[data-type_of_content="cs_api_content"]').show();
			} catch (e) {}
		}, 'inpage');
	}

	function checkApiContent() {
		if (($('div[data-type_of_content="cs_api_content"]') && $('div[data-type_of_content="cs_api_content"]').text().length > $('div[data-type_of_content="pim_content"]').text().length) || $('div[data-type_of_content="cs_api_content"]').attr('data-cs_provider') == 'Logitech') {
			$('div[data-type_of_content="pim_content"]').hide();
			$('div[data-type_of_content="cs_api_content"]').show();
		}
	}

	setTimeout(function () {
		checkApiContent();
	}, 2000);

	setTimeout(function () {
		checkApiContent();
	}, 4000);

	window.addEventListener(
		'message',
		function (e) {
			if (e.hasOwnProperty('originalEvent')) {
				var origin = e.originalEvent.origin || e.origin;
			} else {
				var origin = e.origin;
			}
			if (origin !== 'https://bigbang.parhelion.hr') return;
			document.getElementById('parhelion-frames').style.height = e.data.frameHeight + 'px';
		},
		false
	);
	/////////////////////////////////////////////////////////////////////////////////////

	$('body').on('change', '[data-change_store="location"]', function () {
		var selectedStoreLocation = $(this).val();

		$('[data-change_store="department"] > option[data-location_id="' + selectedStoreLocation + '"]').show();
		$('[data-change_store="department"] > option[data-location_id!="' + selectedStoreLocation + '"]').hide();
		$('[data-change_store="department"] > option[data-location_id="0"]').show().attr('selected', 'selected');
	});

	// developed for batch change shipping data on product level
	function refresh_shipping_info(form, data) {
		var content_element = form.find('[data-selector="cart-body"]');
		content_element.html(data.content);
		set_max_shipping_data(form, data.additional_data);
	}

	function change_shipping_data_for_products(form, data, action = '') {
		// developed for batch change shipping data on product level
		loading_indicator('show');

		delete data.shipping_method_el;
		delete data.shipping_method_val;
		delete data.pickup_location_el;
		delete data.pickup_location_val;

		data.cart_id = form.data('cart_id');
		data.cart_token_id = form.data('cart_token_id');
		data['shipping_group_items'] = [];
		var group_items = get_group_items();
		for (group_item in group_items) {
			if (group_items[group_item].bb_shipping_items.length < 1) {
				group_items[group_item].all_items.forEach(item_id => {
					data.shipping_group_items.push(item_id);
				});

				if (action == 'delete') {
					data['shipping_group_action'] = action;
					data.product_ids = data.shipping_group_items;
				} else {
					data['shipping_group_action'] = group_items[group_item].action;
				}
			}
		}

		if (action == 'delete') {
			data.shipping_method_id = 1;
		}

		jQuery.ajax({
			url: '/api/webshop/change_shipping_on_product/',
			data: data,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					$('.w-cart-shipping-bar').removeClass('active');
					$('body').removeClass('cart-shipping-tooltip');
					refresh_shipping_info(form, data);
				} else {
					show_error($('.content-section'), data.message);

					var $response_form = form;
					for (var f in data.errors) {
						display_error = 0;
						visible_errors = 0;
						enable_global_errors = false;

						if (f == 'shipping_city' || f == 'shipping_zipcode') {
							var field = f.slice(9);
							var error_el = $response_form.find('[id^="field-error-' + field + '"]');
							var label_el = $response_form.find('label[for^="field-' + field + '"]');
							var display_field = $response_form.find('[id^="field-' + field + '"]');
						} else {
							var error_el = $response_form.find('[id^="field-error-' + f + '"]');
							var label_el = $response_form.find('label[for^="field-' + f + '"]');
							var display_field = $response_form.find('[id^="field-' + f + '"]');
						}

						if (error_el.size() > 0) {
							// only visible fields for step form
							if (!enable_global_errors) {
								if (display_field.is(':visible')) {
									display_error = 1;
									visible_errors++;
								}
							} else {
								display_error = 1;
							}

							if (display_error === 1) {
								label_el.addClass('field_error_label');
								error_el.html(data.errors[f]).fadeIn();

								if (display_field.size()) {
									display_field.addClass('field_error_input');
									if (display_field.is('select') && site_select_plugin === 'chosen') {
										$response_form.find('[id^="field-' + f + '_chosen"]').addClass('field_error_input');
									}
								} else {
									// input radio, checkbox
									$('input[name="' + f + '"]', $response_form).addClass('field_error_input_radio');
								}
							}
						} else {
							console.debug('siteforms error, field: ' + f);
						}
					}
				}

				loading_indicator('hide');
				$('button[data-trigger="ship_to_address_submit"]').prop('disabled', false); // prevent double submit - enable button
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				$('button[data-trigger="ship_to_address_submit"]').prop('disabled', false); // prevent double submit - enable button

				console.log(textStatus, errorThrown);
			},
		});
	}

	function get_timestamp(form, date_element, time_element, input_val) {
		if (input_val == 1) {
			form = form.find('input[name="ship_to_address_change_type"]:checked').closest('.field-shipping-row');
			var selected_shipping_date = form.find(date_element).text();
		} else {
			form = form.find('input[name="ship_to_address_change_type"]:checked').closest('.field-shipping-row').next();
			var selected_shipping_date = form.find(date_element).val();
		}
		// transform dd.mm.yyyy to mm.dd.yyyy because Date.parse accepts this format
		var selected_shipping_date_parts = selected_shipping_date.split('.');
		selected_shipping_date = selected_shipping_date_parts[1] + '/' + selected_shipping_date_parts[0] + '/' + selected_shipping_date_parts[2];

		var selected_shipping_datetime = selected_shipping_date;
		var selected_time = form.find(time_element);
		var selected_shipping_time = selected_time.val();
		var use_time = selected_time.parent().data('real_time');
		if (use_time) {
			var selected_shipping_datetime = selected_shipping_date + ' ' + selected_shipping_time;
		}

		var selected_shipping_timestamp = Date.parse(selected_shipping_datetime);
		selected_shipping_timestamp = selected_shipping_timestamp / 1000;

		return [selected_shipping_timestamp, selected_shipping_time];
	}

	function get_shipping_data(form, address_data_container) {
		// developed for batch change shipping data on product level
		var selected_product_ids = [];
		var selected_items = form.find('input[data-element="cart-item"]:checked');
		var product_shipping_dates = {};

		var cart_all_products = [];
		var all_items = form.find('input[data-element="cart-item"]').closest('.wp');
		all_items.each(function () {
			cart_all_products.push({
				'group_position': $(this).prevAll('.w-cart-group:first').data('sort_group_position'),
				'id': $(this).data('product_id'),
				//'available_qty': $(this).data('available_qty'),
				'qty': $(this)
					.find('input[name="qty[' + $(this).data('product_id') + ']"]')
					.val(),
				'serial_code': $(this).find('input[name="code"]').attr('db_value') || '',
				'serial_code_mandatory': $(this).find('input[name="code"]').data('mandatory') || 0,
				'avans_product': $(this).find('input[name="checkbox-avans"]').is(':checked'),
				'group_shipping_method': $(this).prevAll('.w-cart-group:first').data('shopping_shipping_group').split('-')[1],
				'group_shipping_today': $(this).prevAll('.w-cart-group:first').data('shopping_shipping_group_today'),
				'sort_group_position': $(this).prevAll('.w-cart-group:first').data('sort_group_position'),
			});
		});

		selected_items.each(function () {
			var product_id = $(this).data('id');
			selected_product_ids.push(product_id);
			product_shipping_dates[product_id] = {
				'earliest_shipping_date': form.find('[data-product_id=' + product_id + ']').data('max_shipping_date'),
				'max_shipping_date': form.find('[data-product_id=' + product_id + ']').data('shopping_shipping_date'),
			};
		});

		var selected_shipping_method_el = form.find('input[name="shipping"]:checked');
		var selected_shipping_method_val = selected_shipping_method_el.val();
		var selected_shipping_method_id = selected_shipping_method_el.data('shipping_id');

		var selected_pickup_location_el = form.find('input[name="pickup_location"]:checked');
		var selected_pickup_location_val = selected_pickup_location_el.size() ? selected_pickup_location_el.val() : '';
		var selected_pickup_location_id = selected_shipping_method_val == 1 ? selected_shipping_method_el.data('pickup_location_id') : selected_pickup_location_val;

		var selected_ship_to_address_change_type = selected_shipping_method_id == 9 || selected_shipping_method_id == 3 ? form.find('input[name="ship_to_address_change_type"]:checked').val() : '';

		var max_shipping_timestamp = form.data('max_shipping');
		var selected_shipping_timestamp = '';
		var selected_shipping_time = '';
		var group_first_product_id = '';

		switch (selected_ship_to_address_change_type) {
			case '1': // default data
				var selected_shipping_date = form.find('input[name="shipping"]:checked').closest('.field-shipping-row').next().find('[data-js=print-delivery-date]').text();
				var shipping_date_arr = selected_shipping_date.split('.');
				selected_shipping_timestamp = Date.parse(shipping_date_arr[1] + '/' + shipping_date_arr[0] + '/' + shipping_date_arr[2]) / 1000;
				selected_shipping_time = form.find('[data-js=print-delivery-time]').text() ? form.find('[data-js=print-delivery-time]').text() : '';
				break;
			case '2': // new date, time
				[selected_shipping_timestamp, selected_shipping_time] = get_timestamp(form, '[name="shipping_date_only_date"]', '[name="shipping_time_only_time"]', selected_ship_to_address_change_type);
				break;
			case '3': // new date, time, address data
				[selected_shipping_timestamp, selected_shipping_time] = get_timestamp(form, '[name="shipping_date"]', '[name="shipping_time"]', selected_ship_to_address_change_type);
				break;
			case '4': //pridruzi postojecoj odpremi
				selected_shipping_timestamp = form.find('input[name=ship_to_address_change_type]:checked').data('appended_shipping_date');
				group_first_product_id = form.find('input[name=ship_to_address_change_type]:checked').data('group_first_product_id');
				break;
		}

		// ship to address data
		var address_data = {};
		var ship_to_address_fields = address_data_container ? address_data_container.find('[data-selector="ship_to_address_fields"]') : form.find('.shipping-address-form').find('[data-selector="ship_to_address_fields"]');

		if (ship_to_address_fields) {
			ship_to_address_fields.each(function () {
				var address_data_field = $(this).attr('name');
				if (address_data_field == 'city' || address_data_field == 'zipcode') {
					address_data_field = 'shipping_' + address_data_field;
				}
				address_data[address_data_field] = address_data_container ? $(this).val() : '';
			});
		}

		var data = {
			'product_ids': selected_product_ids,
			'shipping_method_el': selected_shipping_method_el,
			'shipping_method_val': selected_shipping_method_val,
			'shipping_method_id': selected_shipping_method_id,
			'pickup_location_el': selected_pickup_location_el,
			'pickup_location_val': selected_pickup_location_val,
			'pickup_location_id': selected_pickup_location_id,
			'shipping_timestamp': isNaN(selected_shipping_timestamp) ? '' : selected_shipping_timestamp,
			'shipping_time': selected_shipping_time,
			'ship_to_address_change_type': selected_ship_to_address_change_type,
			'ship_to_address_data': address_data,
			'product_shipping_dates': product_shipping_dates,
			'group_first_product_id': group_first_product_id,
			'cart_all_products': cart_all_products,
		};

		return data;
	}

	shopping_carts.on('change', 'input[data-element="cart-item"]', function () {
		// developed for batch change shipping data on product level
		var this_form = $(this.form);

		let cart_token_id = $(this_form).data('cart_token_id');
		if (!customer_data[cart_token_id] || customer_data[cart_token_id] == null) {
			get_customer_data(cart_token_id);
		}

		var selected_items = this_form.find('input[data-element="cart-item"]:checked');
		append_shipping_checkboxes(this_form);
		var max_shipping_date = parseInt(Date.now());
		var time_formated = 0;
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		if ($('.w-cart-shipping-bar').hasClass('active')) {
			if (selected_product_ids.length < 1) {
				$('.w-cart-shipping-bar').removeClass('active');
			} else {
				var selected_checbox = $("input[name='ship_to_address_change_type']:checked").val();
				populate_inputs(this_form, selected_product_ids, selected_checbox);
			}
		}

		var delivery_dates = earliest_delivery_date(this_form, selected_product_ids, data.product_shipping_dates);
		var date = new Date(parseInt(delivery_dates.max_shipping_date * 1000));
		var date_formated = String('00' + date.getDate()).slice(-2) + '.' + String('00' + (date.getMonth() + 1)).slice(-2) + '.' + date.getFullYear();
		if (!time_formated) {
			time_formated = String('00' + date.getHours()).slice(-2) + ':' + String('00' + date.getMinutes()).slice(-2) + ':' + String('00' + date.getSeconds()).slice(-2);
		}

		this_form.data('max_shipping', max_shipping_date);

		this_form.find('[data-js="print-delivery-date"]').text(date_formated);
		this_form.find('[data-js="print-delivery-time"]').text(time_formated);
	});

	//set inital shipping data
	$(document).ready(function () {
		if ($('form[name="shopping_cart"]').size()) {
			setTimeout(function () {
				jQuery.ajax({
					url: '/api/webshop/get_shopping_shipping_data/',
					dataType: 'json',
					cache: false,
					method: 'POST',
					type: 'POST', // For jQuery < 1.9
					success: function (data) {
						shipping_dates = data.additional_data.shipping_data;
					},
				});

				restore_cart_data();

				let cart_token_id = $(shopping_cart_active).data('cart_token_id');

				setTimeout(function () {
					get_customer_data(cart_token_id);
				}, 500);
			}, 500);
		}
	});

	function restore_cart_data() {
		const url = new URL(location.href);
		const success = url.searchParams.get('success');

		if (success == 'success_cloned_shopping_cart') {
			const cart_number = url.searchParams.get('cart_hash');
			const coupon = url.searchParams.get('coupon');
			const user_bbis_id = url.searchParams.get('user_bbis_id');

			if (cart_number) {
				const current_pa_cart_code = $("[name='pa_cart_code']").attr('value');

				if (current_pa_cart_code == cart_number) {
					//Restore kupona
					const coupon_exists = $('.ww-coupons.active').length > 0;

					if (coupon != '' && !coupon_exists) {
						$("[name='coupon_code']").attr('value', coupon);
						$('.ww-btn-add')[0].click();
					}

					//Restore usera
					const user_added = $('input[name="pa_search_user"]').attr('value').length > 0;

					if (user_bbis_id != '' && !user_added) {
						$('input[name="pa_search_user"]').attr('value', user_bbis_id);
						$('input[name="pa_search_user"]').trigger('change');
					}
				}
			}
		}
	}

	$('body').on('change', '[data-js="toggle-visibility"]', function () {
		// developed for batch change shipping data on product level
		var this_form = $(this.form);
		var selected = this_form.find('input[data-element="cart-item"]:checked');

		if (!selected.length) {
			return;
		}

		var group_target = $(this).data('grouptarget');
		var target_selector = $(this).data('target');

		this_form.find('[data-group="' + group_target + '"]').hide();

		if (target_selector.length) {
			this_form.find(target_selector).show();
		}
	});

	function loading_indicator(action) {
		if (action == 'show') {
			var waiting_message = cmswebshop.shopping_cart.config.waiting_messages[site_lang] || cmswebshop.shopping_cart.config.waiting_messages['en'];
			$('.form-loading').remove(); // remove possible previous loading indicator
			$('body').append('<div class="loading form-loading"><span>' + waiting_message + '</span></div>'); // show loading indicator
		} else if (action == 'hide') {
			$('.form-loading').fadeOut().remove(); // remove loading
		}
	}

	function set_cart_title(form, name_input_el, data_to_send) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_cart_title/',
			data: data_to_send,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok' && data.title) {
					form.find('[data-selector="cart_title_modal"]').text(data_to_send.cart_name);
					//console.log(data);
				} else {
					show_error($('.content-section'), data.message);
					name_input_el.val('');
				}

				loading_indicator('hide');

				if (data.cart_name == '') {
					name_input_el.val('');
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				name_input_el.val('');
				console.log(textStatus, errorThrown);
			},
		});
	}

	function on_cart_name_blur(this_form) {
		//var cart_id = this_form.data('cart_id');
		var cart_token_id = this_form.data('cart_token_id');
		var name_input_el = this_form.find('[data-selector="cart_name"]');
		var cart_name = name_input_el.val();

		var data = {
			//'cart_id': cart_id,
			'cart_token_id': cart_token_id,
			'cart_name': cart_name,
		};

		set_cart_title(this_form, name_input_el, data);
	}

	shopping_carts
		.on('click', '[data-js="set-cart-name"]', function () {
			on_cart_name_blur($(this).closest('form'));
		})
		.on('blur', '[data-selector="cart_name"]', function () {
			on_cart_name_blur($(this).closest('form'));
		});

	$(document).on('change', 'input[data-js="warehouse-pickup-item"]', function () {
		const input_element = $(this)[0];
		const warehouse_pickup = input_element.checked;
		const product_code = input_element.getAttribute('code');

		const this_form = $(this).closest('form');
		const cart_id = this_form.data('cart_id');
		const cart_token_id = this_form.data('cart_token_id');

		const data = {
			'cart_id': cart_id,
			'cart_token_id': cart_token_id,
			'product_code': product_code,
			'warehouse_pickup': warehouse_pickup,
		};

		set_warehouse_pickup_item(data);
	});

	function set_warehouse_pickup_item(data) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_warehouse_pickup/',
			data: data,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					if (data.content != '') {
						shopping_cart_active.find('[data-selector="cart-body"]').html(data.content);
					}
				} else {
					show_error($('.content-section'), data.message);
				}

				loading_indicator('hide');
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	$(document).on('change', 'input[data-js="avans-item"]', function () {
		const input_element = $(this)[0];
		const avans_item = input_element.checked;
		const product_code = input_element.getAttribute('code');

		const this_form = $(this).closest('form');
		const cart_id = this_form.data('cart_id');
		const cart_token_id = this_form.data('cart_token_id');

		const data = {
			'cart_id': cart_id,
			'cart_token_id': cart_token_id,
			'product_code': product_code,
			'avans_item': avans_item,
		};

		set_avans_item(data);
	});

	function set_avans_item(data) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_avans_item/',
			data: data,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					if (data.content != '') {
						shopping_cart_active.find('[data-selector="cart-body"]').html(data.content);
					}
				} else {
					show_error($('.content-section'), data.message);
				}

				loading_indicator('hide');
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	function set_cart_hash(form, hash_input_el, data_to_send) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_cart_hash/',
			data: data_to_send,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok' && data.cart_hash) {
					form.find('[data-selector="cart_hash"]').text(data_to_send.cart_hash);
					let current_href = $('.cart-finish-shopping:first').attr('href');
					let current_href_url = new URL(current_href);
					let current_pa_cart_code = current_href_url.searchParams.get('pa_cart_code');

					if (current_pa_cart_code) {
						current_href = current_href.substr(0, current_href.length - current_pa_cart_code.length);
					}

					let new_href = current_href + data_to_send.cart_hash;
					$('.cart-finish-shopping:first').attr('href', new_href);

					remove_error('cart_hash_entered');
				} else {
					show_error($('.content-section'), data.message);
					hash_input_el.val('');
				}

				loading_indicator('hide');

				if (data.cart_hash == '') {
					hash_input_el.val('');
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				hash_input_el.val('');
				console.log(textStatus, errorThrown);
			},
		});
	}

	function on_cart_hash_blur() {
		//var cart_id = this_form.data('cart_id');
		var this_form = shopping_cart_active;
		var cart_token_id = this_form.data('cart_token_id');
		var hash_input_el = this_form.find('[data-selector="cart_hash"]');
		var cart_hash = hash_input_el.val();

		var data = {
			//'cart_id': cart_id,
			'cart_token_id': cart_token_id,
			'cart_hash': cart_hash,
		};

		set_cart_hash(this_form, hash_input_el, data);
	}

	shopping_carts
		.on('click', '[data-confirm="cart_hash"]', function () {
			on_cart_hash_blur();
		})
		.on('blur', '[data-selector="cart_hash"]', function () {
			on_cart_hash_blur();
		});

	function set_cart(form, data_param) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_cart/',
			data: data_param,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					if (data_param.update_cart_content == 1) {
						$('[data-js="change-cart"]').prop('checked', false);
						form.find('[data-js="change-cart"]').prop('checked', true);

						$('.w-cart-item').removeClass('active');
						form.find('.w-cart-item').addClass('active');

						var content_element = form.find('[data-selector="cart-body"]');
						set_cart_content(content_element, data.content);
						set_max_shipping_data(form, data.additional_data);
						closeActiveElement($('.w-cart-shipping-bar')); // init close select element
					}

					store_reload();
				} else {
					show_error($('.content-section'), data.message);
				}

				loading_indicator('hide');
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	$('[data-js="change-cart"]').on('change', function () {
		var this_form = $(this.form);

		$('[data-js="change-cart"]').not(this).prop('checked', false); // nisu u istoj formi pa druge ugasimo jer radio polje radi unutar forme

		if ($(this).is(':checked')) {
			var cart_token_id = $(this).val();
			var cart_id = this_form.data('cart_id');

			var data = {
				'cart_id': cart_id,
				'cart_token_id': cart_token_id,
			};

			set_cart(this_form, data);
		}
	});

	function new_cart() {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/new_cart/',
			//data: data,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					// console.log(data);
					store_reload();
				} else {
					show_error($('.content-section'), data.message);
				}

				loading_indicator('hide');
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	$('[data-js="new-cart"]').on('click', function () {
		new_cart();
	});

	function remove_cart(form, data_to_send) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/remove_cart/',
			data: data_to_send,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					console.log(data);
					form.remove();

					window.location = window.location.href;
					store_reload();
				} else {
					show_error($('.content-section'), data.message);
					loading_indicator('hide');
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	$('body').on('click', '[data-js="remove-cart"]', function () {
		var cart_token_id = $(this).data('cart_token_id');
		var this_form = $('form[data-cart_token_id="' + cart_token_id + '"]');

		var data = {
			'cart_token_id': cart_token_id,
		};

		$.fancybox.close();
		remove_cart(this_form, data);
	});

	function set_max_shipping_data(form, additional_data) {
		form.attr('data-max_shipping', additional_data.max_shipping_timestamp);
		shipping_dates = additional_data.shipping_data;
	}

	function empty_carts_content_parts(carts) {
		var carts = carts || $('[data-selector="shopping_cart"]');
		//console.log(carts);

		carts.find('[data-selector="empty_on_inactive_carts"]').empty();
		carts.find('[data-selector="remove_on_inactive_carts"]').remove();
	}

	function set_cart_content(content_element, content) {
		//$('[data-selector="cart-body"]').empty(); // empty content on all carts
		empty_carts_content_parts();

		var this_cart = content_element.closest('.w-cart-item');
		$('.w-cart-item').not(this_cart).removeClass('details-active');

		content_element.html(content);
	}

	function get_cart_content(form, data, content_element) {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/get_cart_content/',
			data: data,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				if (data.response === 'ok') {
					if (content_element) {
						set_cart_content(content_element, data.content);
						set_max_shipping_data(form, data.additional_data);
						closeActiveElement($('.w-cart-shipping-bar')); // init close select element
					}
				} else {
					show_error($('.content-section'), data.message);
				}

				loading_indicator('hide');
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
			},
		});
	}

	$('[data-js="get-cart-content"]').on('click', function () {
		var this_form = $(this).closest('form');
		var cart_id = this_form.data('cart_id');
		var cart_token_id = this_form.data('cart_token_id');
		var content_element = this_form.find('[data-selector="cart-body"]');
		var is_selected_cart = this_form.find('input[data-js="change-cart"]:checked').length;

		var data = {
			'cart_id': cart_id,
			'cart_token_id': cart_token_id,
			'update_cart_content': 1,
		};

		if (is_selected_cart) {
			get_cart_content(this_form, data, content_element);
			//console.log('is_selected_cart');
		} else {
			set_cart(this_form, data);
			//console.log('NOT_is_selected_cart');
		}
	});

	function get_values_within_group(group_name) {
		var active_group_elements = $('input[data-filter_group="' + group_name + '"]:checked');
		var group_value = [];
		active_group_elements.each(function () {
			group_value.push($(this).val());
		});
		return group_value.join();
	}

	$('input[data-js="checkbox-as-link-filter"]').on('change', function () {
		var group_name = $(this).data('filter_group');
		var base_url = window.location.href.split('?')[0];
		var group_value = get_values_within_group(group_name);

		var redirect_url = group_value.length ? base_url + '?' + group_name + '=' + group_value : base_url;
		window.location.href = redirect_url;
	});

	$('[data-js="clear-checkbox-group-and-redirect"]').on('click', function (e) {
		e.preventDefault();
		// ako želimo očistiti polja prije redirekta (na browser back budu ugašena ali bi trebalo i url onda prilagoditi ?)
		// var filters_group = $(this).data('group');
		// $('input[data-filter_group="'+filters_group+'"]:checked').prop("checked", false);

		window.location.href = window.location.href.split('?')[0];
	});

	$('#selectAll').live('change', function () {
		if ($(this).is(':checked')) {
			$('[data-element="cart-item"]').attr('checked', 'checked');
		} else {
			$('[data-element="cart-item"]').removeAttr('checked');
		}
	});

	/*
      parent_class => selector of elelemnt to prepend to
      error_message => error message to show
     */
	function show_error(parent_class, error_messages, all_errors = [], field_errors = false) {
		if (field_errors) {
			$('[data-error_indentificatior]').removeClass('field_error_input');
			$('span.field_error').remove();
		}
		var this_form = shopping_cart_active;
		for (index in all_errors) {
			var error_input = all_errors[index].error_input;
			var parent_field = all_errors[index].parent_field;
			var error_message = all_errors[index].message;
			var error_field = all_errors[index].error_field;
			var children_field = all_errors[index].children_field;
			var error_indentificatior = all_errors[index].error_indentificatior;
			var extra_error_class = all_errors[index].extra_error_class;

			var error_exist = error_indentificatior ? $('[data-error_indentificatior="' + error_indentificatior + '"]').size() : 0;

			if (error_input) {
				$(this_form).find(error_input).addClass('field_error_input').data('error_indentificatior', error_indentificatior);
				if (parent_field && $(this_form).find(error_input).closest(parent_field).length) {
					$(this_form).find(error_input).closest(parent_field).addClass('field_error').data('error_indentificatior', error_indentificatior);
					if (!error_exist) {
						$(this_form)
							.find(error_input)
							.closest(parent_field)
							.after('<span class="field_error error ' + extra_error_class + '" data-error_indentificatior="' + error_indentificatior + '" style="">' + error_message + '</span>');
					}
				} else {
					if (!error_exist) {
						$(this_form)
							.find(error_input)
							.after('<span class="field_error error ' + extra_error_class + '" data-error_indentificatior="' + error_indentificatior + '" style="">' + error_message + '</span>');
					}
				}
			}

			if (error_field && $(this_form).find(error_field).find(children_field).length) {
				if (!error_exist) {
					$(this_form)
						.find(error_field)
						.find(children_field)
						.after('<span class="field_error error ' + extra_error_class + '" style="">' + error_message + '</span>');
				}
			}
		}

		console.debug('error_messages', error_messages);

		for (var error_message in error_messages) {
			if ($('.wc-global-error').text() != error_messages[error_message] && error_messages[error_message]) {
				parent_class.prepend("<p class='wc-global-error error global-error' data-error=''>" + error_messages[error_message] + '</p>');
				$('#id_error_message').hide();

				setTimeout(function () {
					$('.wc-global-error:contains(' + error_messages[error_message] + ')').remove();
				}, 8000);
			}
		}
	}

	function remove_error(error_identificator) {
		$('[data-error_indentificatior="' + error_identificator + '"]').removeClass('field_error_input');
		$('span.field_error[data-error_indentificatior="' + error_identificator + '"]').remove();
	}

	/*
    let paOrigin = 'https://devasistent.bigbang.si';
    let paOriginExtension = '/people/';
    if (window.location.hostname == 'pa.bigbang.si') {
        paOrigin = 'https://asistent.bigbang.si';
        //paOriginExtension = '/existingCustomers';
    }
    let paSearchUserEl;

    window.addEventListener('message', (event) => {
        console.debug('addEventListener message, receive', event.origin, 'need', paOrigin, event);
        if (event.origin !== paOrigin) {
            return;
        }

        if (typeof paSearchUserEl === 'undefined') {
            // iframe
            let msg = JSON.parse(event.data);
            console.debug('addEventListener message (iframe) SUCCESS', msg);

            if (msg.type === 'customer' && msg.event === 'selected') {
                // webshop/store/

                let bbis_id = msg.customerBbisId || msg.payload.bbisId || '';
                let active_cart_token_id = $('html').data('active_cart_token_id') || '';

                if (bbis_id != "" && bbis_id != null) {
                    let data = {
                        'cart_token_id': active_cart_token_id,
                        'bbis_id': bbis_id,
                    }

                    set_cart_customer_data(null, null, data, false, '/webshop/store/');
                }
            }

        } else {
            // pop-up
            paSearchUserEl.close();

            let searchField = 'pa_search_user';
            let msg = JSON.parse(event.data);

            console.debug('addEventListener message (pop-up) SUCCESS', searchField, msg);

            if (msg.type === 'customer' && msg.event === 'selected') {
                $('input[name="' + searchField + '"]').val(msg.customerBbisId || msg.payload.bbisId || '');
                $('input[name="' + searchField + '"]').trigger("change"); // Change se ne registrira zbog window.open() pa koristimo ručni trigger
                $('input[name="search_q"]').focus();
            }

        }

    });

    $('input[name="pa_search_user"]').live('click', function () {
        paSearchUserEl = window.open(paOrigin + paOriginExtension, '_blank');
    });
    */

	//QR Scanner
	function decodeContinuously(codeReader, selectedDeviceId) {
		codeReader.decodeFromInputVideoDeviceContinuously(selectedDeviceId, 'video', (result, err) => {
			if (result) {
				if ($('#field-pa-cart-code').length > 0) {
					$('#field-pa-cart-code').val(result.text);
				}

				if ($('#field-barcode').length > 0) {
					$('#field-barcode').val(result.text);
				}

				$('#id_submit_scan').click();
			}
		});
	}

	if ($('#field-barcode').length > 0) {
		$('#field-barcode').focus();
	}

	if ($('#field-pa-cart-code,#field-barcode').length > 0) {
		let selectedDeviceId;
		let codeReader = new ZXing.BrowserQRCodeReader();

		if ($('#field-barcode').length > 0) {
			codeReader = new ZXing.BrowserBarcodeReader();
		}

		codeReader
			.getVideoInputDevices()
			.then(videoInputDevices => {
				const sourceSelect = document.getElementById('sourceSelect');
				selectedDeviceId = videoInputDevices[0].deviceId;
				if (videoInputDevices.length >= 1) {
					videoInputDevices.forEach(element => {
						const sourceOption = document.createElement('option');
						sourceOption.text = element.label;
						sourceOption.value = element.deviceId;
						sourceSelect.appendChild(sourceOption);
					});

					sourceSelect.onchange = () => {
						selectedDeviceId = sourceSelect.value;
						decodeContinuously(codeReader, selectedDeviceId); // Kod okretanja kamere, automatski pokreni citanje QR koda
					};

					const sourceSelectPanel = document.getElementById('sourceSelectPanel');
					sourceSelectPanel.style.display = 'flex';
				}

				decodeContinuously(codeReader, selectedDeviceId);
			})
			.catch(err => {
				console.error(err);
			});
	}

	$('input[name="pa_search_user"]').live('change', function () {
		let this_form = $(this).closest('form');
		let cart_token_id = this_form.data('cart_token_id');
		let pa_search_user_input_el = this_form.find('[data-selector="pa_search_user"]');
		let pa_search_user_val = pa_search_user_input_el.val();

		let pa_search_bbis_id = '0';
		if (pa_search_user_val != '' && pa_search_user_val != null) {
			pa_search_bbis_id = pa_search_user_val;
			pa_search_user_input_el.val('');

			let data = {
				'cart_token_id': cart_token_id,
				'bbis_id': pa_search_bbis_id,
			};

			set_cart_customer_data(this_form, pa_search_user_input_el, data, true);
			return;
		}

		let data = {
			'cart_token_id': cart_token_id,
			'bbis_id': pa_search_bbis_id,
		};

		set_cart_customer_data(this_form, pa_search_user_input_el, data, true);
	});

	function set_cart_customer_data(form, pa_search_user_input_el, data_to_send, use_reload = false, redirect_on_success = '') {
		loading_indicator('show');

		jQuery.ajax({
			url: '/api/webshop/set_cart_customer_data/',
			data: data_to_send,
			dataType: 'json',
			cache: false,
			method: 'POST',
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				loading_indicator('hide');
				if (pa_search_user_input_el && pa_search_user_input_el.size()) {
					pa_search_user_input_el.val(data.cart_reservation_for);
				}
				if (redirect_on_success) {
					window.location.href = redirect_on_success;
				}
				if (use_reload) {
					store_reload();
				}
			},
			error: function (jqXHR, textStatus, errorThrown) {
				loading_indicator('hide');
				console.log(textStatus, errorThrown);
				alert(errorThrown);
				if (pa_search_user_input_el && pa_search_user_input_el.size()) {
					pa_search_user_input_el.val('');
				}
			},
		});
	}

	function populate_inputs(this_form, selected_product_ids, value) {
		if (selected_product_ids.length < 1) {
			return;
		}

		if (value == 3) {
			var cart_token_id = this_form.data('cart_token_id');
			var api_code2 = cart_token_id in customer_data && customer_data[cart_token_id] != null ? customer_data[cart_token_id].api_code2 : '';
			if (api_code2) {
				for (var shipping_field in customer_data[cart_token_id]) {
					if (shipping_field == 'city' || shipping_field == 'zipcode' || shipping_field == 'location') {
						var field = this_form.find('[name=' + shipping_field + ']');
					} else {
						var search_field = 'shipping_' + shipping_field;
						var field = this_form.find('[name=' + search_field + ']');
					}

					if (field.length < 1) {
						continue;
					}

					if (shipping_field == 'location') {
						customer_data[cart_token_id][shipping_field] = customer_data[cart_token_id]['city'] + '(' + customer_data[cart_token_id]['zipcode'] + ')';
					}

					field.val(customer_data[cart_token_id][shipping_field]);
					field.parent().addClass('ffl-floated');
				}
			} else {
				selected_product_ids.forEach(product_id => {
					if (shipping_dates[product_id] != undefined && shipping_dates[product_id].shipping_date != undefined && shipping_dates[product_id].shipping_date != 0) {
						for (var shipping_field in shipping_dates[product_id]) {
							if (shipping_field == 'shipping_id' || shipping_field == 'shipping_date') {
								continue;
							}

							if (shipping_field == 'shipping_zipcode' || shipping_field == 'shipping_city') {
								var striped_field = shipping_field.slice(9);
								var field = this_form.find('[name=' + striped_field + ']');
							} else {
								var field = this_form.find('[name=' + shipping_field + ']');
							}

							field.val(shipping_dates[product_id][shipping_field]);
							field.parent().addClass('ffl-floated');
						}
					}
				});
			}
		}

		var data = get_shipping_data(this_form, false);
		var delivery_dates = earliest_delivery_date(this_form, selected_product_ids, data.product_shipping_dates);
		var date = new Date(parseInt(delivery_dates.max_shipping_date * 1000));
		var date_formated = String('00' + date.getDate()).slice(-2) + '.' + String('00' + (date.getMonth() + 1)).slice(-2) + '.' + date.getFullYear();

		if (value == 2) {
			var field = this_form.find('[name="shipping_date_only_date"]');
		} else {
			var field = this_form.find('[name="shipping_date"]');
		}

		field.val(date_formated);
		field.parent().addClass('ffl-floated');

		var date = new Date(parseInt(delivery_dates.earliest_shipping_date * 1000));
		var date_formated = String('00' + date.getDate()).slice(-2) + '.' + String('00' + (date.getMonth() + 1)).slice(-2) + '.' + date.getFullYear();

		$('.field-datepicker').datepicker('destroy');
		$(this_form).find('input[name="ship_to_address_change_type"]:checked').closest('.field-shipping-row').next().find('.field-datepicker').datepicker({
			minDate: date_formated,
			altFormat: 'yy-mm-dd',
			changeYear: true,
			changeMonth: true,
			yearRange: '-100:+0',
		});
	}

	function get_customer_data(cart_token_id) {
		if (!cart_token_id) {
			return;
		}

		jQuery.ajax({
			url: '/api/webshop/get_cart_customer_data/',
			dataType: 'json',
			cache: false,
			method: 'POST',
			data: {'cart_token_id': cart_token_id},
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				customer_data[cart_token_id] = data;
			},
		});
	}

	function earliest_delivery_date(this_form, selected_product_ids, product_shipping_dates) {
		var max_shipping_date = 0;
		var earliest_shipping_date = 0;
		selected_product_ids.forEach(product_id => {
			if (product_shipping_dates[product_id].earliest_shipping_date > earliest_shipping_date) {
				earliest_shipping_date = product_shipping_dates[product_id].earliest_shipping_date;
			}

			if (product_shipping_dates[product_id].max_shipping_date > max_shipping_date) {
				max_shipping_date = product_shipping_dates[product_id].max_shipping_date;
			}
		});

		if (!max_shipping_date || max_shipping_date < earliest_shipping_date) {
			max_shipping_date = earliest_shipping_date;
		}

		return {
			'max_shipping_date': max_shipping_date,
			'earliest_shipping_date': earliest_shipping_date,
		};
	}

	$('[data-js="reserve-order-button"]').live('click', function () {
		loading_indicator('show');
	});

	$('.cart-finish-shopping').live('click', function (event) {
		loading_indicator('show');
		var allowed = check_if_finishing_order_forbidden();
		if (!allowed) {
			event.preventDefault();
			setTimeout(function () {
				loading_indicator('hide');
			}, 1000);
		}
	});

	function append_shipping_checkboxes(this_form) {
		var available_dates = [];
		var available_bb_dates = [];
		var cart_groups = this_form.find('.w-cart-group');
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		$('[data-appended_date=true]').remove();
		$('[data-appended_date_bb=true]').remove();

		var selected_items_data = [];
		var selected_item_methods = [];
		var selected_max_shipping_date = 0;
		selected_product_ids.forEach(product_id => {
			var selected_shipping_adress = $('[data-product_id=' + product_id + ']')
				.prevAll('.w-cart-group:first')
				.find('.w-cart-group-address')
				.children()
				.text();
			var selected_item_date_method = $('[data-product_id=' + product_id + ']')
				.prevAll('.w-cart-group:first')
				.data('shopping_shipping_group');
			var default_shipping_date = $('[data-product_id=' + product_id + ']').data('max_shipping_date');
			var selected_item_date = selected_item_date_method.split('-')[0];
			var date_address_code = selected_shipping_adress + selected_item_date;

			if (default_shipping_date > selected_max_shipping_date) {
				selected_max_shipping_date = default_shipping_date;
			}

			if (!selected_items_data[date_address_code]) {
				selected_items_data[date_address_code] = {
					'shipping_date': selected_item_date,
					'shipping_address': selected_shipping_adress,
				};
			}
			if (!selected_item_methods.includes(selected_item_date_method)) {
				selected_item_methods.push(selected_item_date_method);
			}
		});

		cart_groups.each(function (index) {
			var shipping_date_method = $(this).data('shopping_shipping_group').split('-');
			var shipping_date = parseInt(shipping_date_method[0]);
			var shipping_method = shipping_date_method[1];
			var shipping_address = $(this).find('.w-cart-group-address').children().text();
			var date_address_code = shipping_address + shipping_date;
			var group_first_product_id = $(this).next('.wp').data('product_id');

			if (selected_max_shipping_date > shipping_date) {
				return;
			}

			if (!selected_items_data[date_address_code] || (selected_item_methods.length == 1 && (selected_items_data[date_address_code].shipping_date != shipping_date || selected_items_data[date_address_code].shipping_address != shipping_address)) || selected_item_methods.length > 1) {
				if (shipping_method == 3 && available_dates.shipping_date != shipping_date && available_dates.shipping_address != shipping_address) {
					available_dates.push({
						'shipping_date': shipping_date,
						'shipping_address': shipping_address,
						'group_first_product_id': group_first_product_id,
					});
				} else if (shipping_method == 4 && available_dates.shipping_date != shipping_date && available_dates.shipping_address != shipping_address) {
					available_bb_dates.push({
						'shipping_date': shipping_date,
						'shipping_address': shipping_address,
						'group_first_product_id': group_first_product_id,
					});
				}
			}
		});

		var checkbox_html = '';
		available_dates.forEach(available_date => {
			var date = new Date(parseInt(available_date.shipping_date * 1000));
			var date_formated = String('00' + date.getDate()).slice(-2) + '.' + String('00' + (date.getMonth() + 1)).slice(-2) + '.' + date.getFullYear();
			var ship_date_address_code = available_date.shipping_date + available_date.shipping_address;
			checkbox_html +=
				'\n' +
				'    <div class="field-shipping-row" data-appended_date=\'true\'>\n' +
				'        <span class="field radio-field">\n' +
				'            <input type="radio" data-change="batch" data-appended_shipping_date=' +
				available_date.shipping_date +
				' name="ship_to_address_change_type" value="4" id="field-ship-to-address-change-type-' +
				ship_date_address_code +
				'" data-grouptarget="shipping-address-form" data-group_first_product_id=' +
				available_date.group_first_product_id +
				' data-target="" data-js="toggle-visibility">\n' +
				'            <label for="field-ship-to-address-change-type-' +
				ship_date_address_code +
				'">\n' +
				'                <span>\n' +
				'Pridruži postojećoj odpremi: ' +
				'                    <strong>' +
				date_formated +
				'</strong><br>\n' +
				'                    <span>' +
				available_date.shipping_address +
				'</span>\n' +
				'                </span>\n' +
				'            </label>\n' +
				'        </span>\n' +
				'    </div>';
		});
		$('.ship_to_address > .field-shipping-row:first').after(checkbox_html);

		checkbox_html = '';
		available_bb_dates.forEach(available_date => {
			var date = new Date(parseInt(available_date.shipping_date * 1000));
			var date_formated = String('00' + date.getDate()).slice(-2) + '.' + String('00' + (date.getMonth() + 1)).slice(-2) + '.' + date.getFullYear();
			var ship_date_address_code = available_date.shipping_date + available_date.shipping_address;
			checkbox_html +=
				'\n' +
				'    <div class="field-shipping-row" data-appended_date_bb=\'true\'>\n' +
				'        <span class="field radio-field">\n' +
				'            <input type="radio" data-change="batch" data-appended_shipping_date=' +
				available_date.shipping_date +
				' name="ship_to_address_change_type" value="4" id="field-ship-to-address-change-type-bb-' +
				ship_date_address_code +
				'" data-grouptarget="shipping-address-form" data-group_first_product_id=' +
				available_date.group_first_product_id +
				' data-target="" data-js="toggle-visibility">\n' +
				'            <label for="field-ship-to-address-change-type-bb-' +
				ship_date_address_code +
				'">\n' +
				'                <span>\n' +
				'Pridruži postojećoj odpremi: ' +
				'                    <strong>' +
				date_formated +
				'</strong><br>\n' +
				'                    <p>' +
				available_date.shipping_address +
				'</p>\n' +
				'                </span>\n' +
				'            </label>\n' +
				'        </span>\n' +
				'    </div>';
		});
		$('.ship_to_address_bb > .field-shipping-row:first').after(checkbox_html);
	}

	function check_BB_shipping() {
		var this_form = shopping_cart_active;
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;
		var cart_token_id = this_form.data('cart_token_id');
		var api_code2 = cart_token_id in customer_data && customer_data[cart_token_id] != null ? customer_data[cart_token_id].api_code2 : '';
		var bb_element = $(this_form).find('[data-shipping_id="3"]').closest('.field-shipping-row');

		var bb_delivery = 0;
		selected_product_ids.forEach(product_id => {
			var product_bb_delivery = $(this_form)
				.find('[data-product_id=' + product_id + ']')
				.data('bb_shipping');
			if (product_bb_delivery && bb_delivery == 0) {
				bb_delivery = 1;
			}
		});

		var bb_join_checkbox = $(this_form).find('[data-appended_date_bb="true"]').length;

		if (!bb_delivery && bb_join_checkbox < 1) {
			bb_element.hide();
		} else {
			bb_element.show();
		}

		if (!bb_delivery && bb_join_checkbox > 0) {
			if (!api_code2) {
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="3"]').closest('.field-shipping-row').hide();
			} else {
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="1"]').closest('.field-shipping-row').hide();
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="2"]').closest('.field-shipping-row').hide();
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="3"]').closest('.field-shipping-row').hide();
			}
		} else {
			if (!api_code2) {
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="3"]').closest('.field-shipping-row').show();
			} else {
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="1"]').closest('.field-shipping-row').show();
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="2"]').closest('.field-shipping-row').show();
				bb_element.next('.ship_to_address_bb').find('input[name="ship_to_address_change_type"][value="3"]').closest('.field-shipping-row').show();
			}
		}
	}

	function get_group_items() {
		var this_form = shopping_cart_active;
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		var groups = {};
		var is_selected_bb_product = 0;
		selected_product_ids.forEach(product_id => {
			var selected_product_el = $(this_form).find('[data-product_id=' + product_id + ']');
			var selected_group = selected_product_el.prevAll('.w-cart-group:first');
			var items_in_group = selected_group.nextUntil('.w-cart-group');
			var selected_group_method = selected_group.data('shopping_shipping_group');
			if (!(selected_group_method in groups)) {
				groups[selected_group_method] = {
					'all_items': [],
					'bb_shipping_items': [],
					'action': '',
				};

				items_in_group.each((index, item) => {
					var item_id = $(item).data('product_id');
					if (item_id) {
						var bb_shipping_item = $(item).data('bb_shipping');
						groups[selected_group_method].all_items.push(item_id);
						if (bb_shipping_item) {
							groups[selected_group_method].bb_shipping_items.push(item_id);
						}
					}
				});
			}

			var changed_shipping_method_val = $('input[name="shipping"]:checked').val();
			var selected_time_method = selected_group_method.split('-');
			var selected_method_id = selected_time_method[1];
			var selected_time = selected_time_method[0];
			var selected_shipping_timestamp = selected_time;
			var selected_checbox_val = $('input[name="ship_to_address_change_type"]:checked').val();
			if (selected_method_id == 4 && selected_method_id == changed_shipping_method_val) {
				var allowed_checkbox_val = ['1', '2', '3'];
				if (selected_checbox_val == 1) {
					[selected_shipping_timestamp, selected_shipping_time] = get_timestamp(this_form, '[data-js="print-delivery-date"]', false, selected_checbox_val);
				} else if (allowed_checkbox_val.includes(selected_checbox_val)) {
					[selected_shipping_timestamp, selected_shipping_time] = get_timestamp(this_form, '.field-datepicker', false, selected_checbox_val);
				}
			}

			if (groups[selected_group_method].bb_shipping_items.includes(product_id) && selected_method_id == 4 && selected_method_id != changed_shipping_method_val) {
				is_selected_bb_product++;
				groups[selected_group_method].bb_shipping_items = jQuery.grep(groups[selected_group_method].bb_shipping_items, value => {
					return value != product_id;
				});

				groups[selected_group_method].all_items = jQuery.grep(groups[selected_group_method].all_items, value => {
					return value != product_id;
				});

				groups[selected_group_method].action = 'changed_shipping_method';
			} else if (groups[selected_group_method].bb_shipping_items.includes(product_id) && selected_method_id == 4 && selected_time != selected_shipping_timestamp) {
				is_selected_bb_product++;
				groups[selected_group_method].bb_shipping_items = jQuery.grep(groups[selected_group_method].bb_shipping_items, value => {
					return value != product_id;
				});

				groups[selected_group_method].all_items = jQuery.grep(groups[selected_group_method].all_items, value => {
					return value != product_id;
				});

				groups[selected_group_method].action = 'changed_shipping_date';
			}
		});

		if (is_selected_bb_product) {
			return groups;
		}

		return [];
	}

	function get_warehouses_from_selected_products() {
		var this_form = shopping_cart_active;
		var data = get_shipping_data(this_form, false);
		var selected_product_ids = data.product_ids;

		jQuery.ajax({
			url: '/api/webshop/get_warehouses_with_available_products/',
			dataType: 'json',
			cache: false,
			method: 'POST',
			data: {'selected_product_ids': selected_product_ids},
			type: 'POST', // For jQuery < 1.9
			success: function (data) {
				selected_products_warehouses = data.available_warehouses;
			},
		});
	}

	function check_cart_errors_after_refresh() {
		const query_string = window.location.search;
		const url_params = new URLSearchParams(query_string);
		const cart_error_code = url_params.get('carterrorcode');

		if (cart_error_code) {
			const cart_token_id = $(shopping_cart_active).data('cart_token_id');
			let all_errors = [];

			if (cart_error_code == 'missing_customer_phone') {
				all_errors.push({
					'cart_token_id': cart_token_id,
					'error_input': '.w-cart-sw-input',
					'message': 'Potrebna je stranka z vneseno telefonsko številko.',
					'error_indentificatior': 'cart_customer_entered',
					'extra_error_class': 'error_cart_customer_entered_sw',
				});
			}

			if (all_errors.length > 0) {
				show_error($('.content-section'), [''], all_errors, true);
			}
		}
	}
});

//variation
function setProductVariation(selected_code) {
	$('[data-variation_code]').hide();
	$('[data-variation_active_code]').removeClass('active');
	$('[data-variation_code="' + selected_code + '"]').show();
	$('[data-variation_active_code="' + selected_code + '"]').addClass('active');

	$('a.cd-thumb[data-variation_code]').removeClass('cd-thumb').addClass('cd-thumb-hidden');
	$('a.cd-thumb-hidden[data-variation_code="' + selected_code + '"]')
		.removeClass('cd-thumb-hidden')
		.addClass('cd-thumb');

	//$('.cd-thumbs-slider')[0].slick.refresh();
}
